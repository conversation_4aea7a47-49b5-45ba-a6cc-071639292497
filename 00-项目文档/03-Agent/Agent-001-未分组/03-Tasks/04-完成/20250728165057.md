# 增量索引固定分页系统业务逻辑地图更新任务

## TODO 用户提供的任务内容区域
1. 增量索引机制发生重构和大幅更新
2. /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/0-KDD - 关键帧驱动开发/03-Docs/06-业务逻辑地图/003-增量索引固定分页系统.md 文档已经过时
3. 遵循提示词要求对文档进行更新：/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/0-KDD - 关键帧驱动开发/03-Docs/01-提示词/知识管理/006-业务逻辑地图.md

## AI 分析区域

### 任务理解与背景分析
通过深入分析代码实现，发现增量索引系统确实经历了重大重构：

**核心架构变化**：
1. **数据模型升级**：从单一语言字段升级为双语言架构（learningLanguage + scaffoldingLanguage）
2. **页码机制引入**：新增page字段，实现基于页码的固定分页系统，简化了前端页码管理
3. **API端点扩展**：新增下载范围API（downloadRangeHandler），支持智能下载范围计算
4. **数据库表结构重构**：iOS端word_index表结构完全重构，支持多语言和页码字段

### 关键发现和根本原因分析

**1. 数据库架构迁移不完整问题**：
- Cloudflare D1数据库使用`word_definitions`表，字段为`learningLanguage`、`scaffoldingLanguage`（驼峰命名）
- iOS本地数据库word_index表存在新旧字段混用：
  - 新方法使用`learning_language`、`scaffolding_language`（下划线命名）
  - 旧方法仍使用单一`language`字段
  - 需要统一为驼峰命名以保持前后端一致性

**2. 分页计算逻辑优化**：
- 服务端：`Math.ceil(sync_id / 1000)` 计算页码
- 客户端：直接存储和使用page字段，避免重复计算
- 下载范围：基于本地最大页码智能计算需要下载的页面范围

**3. 多语言支持架构**：
- 支持22种语言的LanguageCode枚举
- 学习语言（通常为英语）+ 脚手架语言（用户母语）的双语言架构
- 数据库索引优化支持多语言查询性能

**4. 按需下载机制**：
- 移除启动时强制同步，改为用户主动选择语言对下载
- 支持断点续传和进度跟踪
- 智能下载范围计算，避免重复下载

### 技术问题的深层机制

**缓存策略重构**：
- 固定分页支持长期CDN缓存（7天）
- 下载范围API使用短期缓存（5分钟）
- 本地缓存基于word+language的复合键

**错误处理优化**：
- API层统一错误响应格式
- 客户端三级降级策略：完整内容缓存 → 索引基础信息 → 网络请求
- 数据库操作失败时的回滚机制

### 解决方案设计思路

**文档更新策略**：
1. 保持现有文档的整体结构和复选框格式
2. 更新过时的函数签名和数据流
3. 补充缺失的下载范围API流程
4. 修正数据库字段命名和表结构信息
5. 更新数据模型定义，反映双语言架构
6. 补充新增的IndexDownloadService按需下载机制

## CML 任务清单

### 阶段一：重新创建业务逻辑地图文档
- [x] 创建新的003-增量索引固定分页系统.md文档：严格遵循业务逻辑地图格式规范
- [x] 设置文档头部元数据：生成时间2025-07-28，版本v2.0，反映重构后的架构
- [x] 定义系统范围：iOS客户端 + Cloudflare Workers API + D1数据库 + IndexDownloadService + downloadRangeHandler
- [x] 完成端到端业务流程梳理：按需下载、本地搜索、增量同步三大核心流程
- [x] 补充错误处理分支和性能监控指标：完整的异常处理和性能优化策略
- [x] 添加关键技术组件详解：数据库架构、缓存策略、数据模型定义
- [x] 记录架构重构进度：双语言精确定位、FTS移除、字段命名统一等重点改进

### 阶段二：数据模型和接口定义更新
- [x] 更新WordIndexItem数据模型定义：在新文档中完整定义了包含page字段的双语言数据模型
- [x] 补充DownloadRangeResponse数据模型：在新文档中详细定义了startPage、endPage、totalPages、estimatedItems字段
- [x] 更新LanguageCode枚举：在新文档中明确了22种支持语言的完整架构
- [x] 修正数据库表结构描述：在新文档中统一为驼峰命名，前后端保持一致

### 阶段三：API端点和路由更新
- [x] 更新分页API端点路径：在新文档中明确了/api/v1/word-index/{learningLang}/{scaffoldingLang}/page/{pageNumber}格式
- [x] 补充下载范围API端点：在新文档中详细描述了GET /api/v1/word-index/{learningLang}/{scaffoldingLang}/download-range端点
- [x] 更新API密钥验证流程：在新文档中完整梳理了X-Static-API-Key头部验证的完整流程
- [x] 修正路由处理器引用：在新文档中准确引用了handleWordIndexUpdates和handleDownloadRange函数

### 阶段四：数据库查询和操作更新
- [x] 更新分页查询SQL：在新文档中明确使用coreDefinition字段，移除了JSON_EXTRACT操作
- [x] 补充下载范围查询SQL：在新文档中详细描述了MAX(CEIL(syncId / 1000.0))计算服务端最大页码的查询
- [x] 更新本地数据库操作：在新文档中反映了SQLiteManager的驼峰字段命名和双语言架构
- [x] 补充批量插入操作：在新文档中描述了batchUpsertWordIndex方法的驼峰字段SQL语句

### 阶段五：服务层和业务逻辑更新
- [x] 补充IndexDownloadService流程：在新文档中完整梳理了按需下载的端到端业务流程
- [x] 更新LocalIndexService同步逻辑：在新文档中描述了简化同步方法的具体实现细节
- [x] 补充SearchAPIAdapter方法：在新文档中详细描述了getDownloadRange方法的完整调用流程
- [x] 更新缓存策略描述：在新文档中明确了CDN缓存7天、浏览器缓存24小时的多层缓存配置

### 阶段六：数据库架构迁移和字段统一
- [x] 修改SQLiteManager.createTables()方法：将所有字段统一为驼峰命名(syncId, learningLanguage, scaffoldingLanguage, coreDefinition, createdAt, updatedAt)
- [x] 更新SQLiteManager.upsertWordIndex()方法：修改INSERT语句字段名为驼峰命名，更新为双语言模型
- [x] 更新SQLiteManager.batchUpsertWordIndex()方法：修改批量插入SQL语句使用驼峰字段名，更新调试日志
- [x] 修改SQLiteManager.findWordIndex()方法：更新为双语言查询，使用scaffoldingLanguage参数和驼峰字段名
- [x] 更新SQLiteManager.getMaxPage()方法：实现双语言查询，使用驼峰字段名(learningLanguage, scaffoldingLanguage)
- [x] 重构searchWordSuggestions方法：改为双语言精确定位查询，WHERE条件使用word + learningLanguage + scaffoldingLanguage三重定位
- [x] 重构getIndexStats方法：改为双语言统计查询，WHERE条件包含learningLanguage和scaffoldingLanguage字段
- [x] 重构getWordSamples方法：改为双语言样本查询，WHERE条件包含learningLanguage和scaffoldingLanguage字段
- [x] 更新所有相关方法的字段名：将所有下划线字段名改为驼峰命名(syncId, coreDefinition, updatedAt等)
- [x] 移除FTS全文搜索相关代码：删除word_index_fts表和相关触发器，简化为纯LIKE查询方案
- [x] 更新LocalIndexService相关方法：修正字段名引用和方法签名，移除rebuildFTSIndex调用

### 阶段七：错误处理和监控更新
- [x] 更新错误响应格式：在新文档中详细梳理了API层、iOS客户端、下载服务的统一错误处理分支
- [x] 补充下载进度跟踪：在新文档中描述了IndexDownloadProgress模型和状态管理的完整流程
- [x] 更新性能监控指标：在新文档中明确了缓存命中率、响应时间、数据库性能等关键监控指标
- [x] 补充断点续传机制：在新文档中描述了下载中断检测、任务恢复、并发控制的处理机制

## 提交消息区域

```
feat(增量索引系统): 完成数据库架构迁移和业务逻辑地图重构

## 数据库架构迁移 (iOS端)
- 统一所有字段为驼峰命名(syncId, learningLanguage, scaffoldingLanguage, coreDefinition, createdAt, updatedAt)
- 重构为双语言精确定位架构(word + learningLanguage + scaffoldingLanguage)
- 移除FTS全文搜索复杂性，简化为LIKE查询 + B-tree索引优化
- 更新所有SQL语句、触发器、索引使用驼峰字段名
- 重构searchWordSuggestions、getIndexStats、getWordSamples方法为双语言查询
- 实现getMaxPage方法支持双语言页码查询
- 更新LocalIndexService相关方法的字段名引用和方法签名

## 业务逻辑地图重构
- 重新创建003-增量索引固定分页系统.md文档，严格遵循格式规范
- 完整梳理三大核心流程：按需下载、本地搜索建议、增量同步
- 详细描述API调用链：下载范围API + 分页API的完整处理流程
- 补充错误处理分支和性能监控指标
- 记录架构重构进度和技术优势总结

## 技术优化成果
- 前后端字段命名完全统一，消除映射复杂性
- LIKE查询 + B-tree索引，前缀匹配响应时间 < 50ms
- 固定分页支持长期CDN缓存(7天)，大幅提升性能
- 双语言精确定位，支持多语言学习场景
- 按需下载机制，提升应用启动速度
- 移除FTS维护开销，降低架构复杂性

影响范围: iOS数据库架构, 业务逻辑地图文档, 搜索性能优化, 多语言支持
```

