### AI 编码任务：优化权益判断逻辑

**分析：**

当前 `AnonymousPurchaseService` 类中的权益判断逻辑 (`transaction.productID.contains("premium") || transaction.productID.contains("pro")`) 存在潜在风险。这种基于字符串部分匹配的判断方式，不够健壮，容易因产品 ID 命名变化、拼写错误或未来新产品而导致误判。

为了提高代码的**安全性、可读性和可维护性**，我们将采用**枚举**（`enum`）的方式来管理所有有效的产品 ID。这种方法将所有产品 ID 集中管理，确保了类型安全，避免了硬编码的字符串依赖，并使得权益判断逻辑更加清晰和精确。

-----

## AI 分析区域

### 关键发现和根本原因分析

1. **现有产品ID体系分析**：
   - 项目中已存在 `ProductId` 枚举（在 `PurchaseAPIModels.swift` 中定义）
   - 实际产品ID格式：`com.senseword.premium.monthly`、`com.senseword.premium.yearly`、`com.senseword.premium.lifetime`
   - 当前判断逻辑使用字符串包含匹配，存在误判风险

2. **代码重复和不一致性问题**：
   - `AnonymousPurchaseService.swift` 中有4处使用相同的不安全判断逻辑
   - 第69行：`checkEntitlements()` 方法
   - 第254行：`updateEntitlementsFromTransaction()` 方法
   - 第400行：`processUnfinishedTransactions()` 方法
   - 缺乏统一的产品ID验证机制

3. **技术问题的深层机制**：
   - 字符串包含匹配可能误判包含"premium"或"pro"的非订阅产品
   - 硬编码字符串分散在多个方法中，维护困难
   - 缺乏类型安全保障，容易出现拼写错误

### 解决方案设计思路

1. **复用现有枚举结构**：
   - 利用已定义的 `ProductId` 枚举，避免重复定义
   - 在 `AnonymousPurchaseService` 中创建Pro产品ID集合
   - 统一所有权益判断逻辑

2. **集中化权益验证**：
   - 创建私有方法 `isProProduct(_:)` 统一处理产品ID验证
   - 使用 `Set<ProductId>` 提供O(1)查找性能
   - 确保所有权益判断都通过统一入口

3. **向后兼容性保障**：
   - 保持现有方法签名不变
   - 内部逻辑重构，外部接口保持稳定
   - 添加详细日志记录便于调试

-----

**任务步骤：**

1.  **定义产品 ID 枚举**

      * 在 `AnonymousPurchaseService.swift` 文件中，创建一个名为 `ProductID` 的新枚举。
      * 该枚举应该遵循 `String` 类型，并为每个 Pro 权益产品 ID 定义一个静态成员。
      * 示例：
        ```swift
        enum ProductID: String {
            case monthlyPro = "com.senseword.premium_subscription.monthly"
            case yearlyPro = "com.senseword.premium_subscription.yearly"
            // ... 更多 Pro 产品ID
        }
        ```

2.  **创建 Pro 权益 ID 集合**

      * 在 `AnonymousPurchaseService` 类的私有属性中，定义一个 `Set` 集合，用于存储所有代表 Pro 权益的 `ProductID` 枚举成员。
      * 示例：
        ```swift
        private let proProductIDs: Set<ProductID> = [.monthlyPro, .yearlyPro]
        ```

3.  **重构权益判断逻辑**

      * 在 `checkEntitlements()` 方法中，找到原有的 `if transaction.productID.contains(...)` 判断语句。
      * 使用新的逻辑进行替换：首先尝试将 `transaction.productID` 字符串转换为 `ProductID` 枚举成员。
      * 然后，检查这个枚举成员是否包含在 `proProductIDs` 集合中。
      * 示例：
        ```swift
        if let productID = ProductID(rawValue: transaction.productID),
           proProductIDs.contains(productID) {
            hasProEntitlement = true
            // ...
        }
        ```

4.  **更新其他相关方法**

      * 检查并修改所有直接使用硬编码字符串进行产品 ID 判断的地方，例如在 `updateEntitlementsFromTransaction`、`processUnfinishedTransactions` 和 `purchaseProduct` 等方法中。
      * 确保所有与 Pro 权益相关的判断都使用新的 `proProductIDs` 集合。

5.  **代码清理**

      * 移除所有旧的、硬编码的 `productID.contains("premium") || productID.contains("pro")` 判断语句，确保代码库中不再存在这种不安全的模式。
      * 添加必要的注释，解释 `ProductID` 枚举和 `proProductIDs` 集合的作用。

-----

## CML 任务清单

### 阶段一：导入依赖和创建Pro产品集合
- [x] 在AnonymousPurchaseService.swift文件第11行后添加导入语句：`import` PurchaseAPIModels中的ProductId枚举（已跳过，ProductId在同一项目中无需导入）
- [x] 在AnonymousPurchaseService类第36行后添加私有属性：`private let proProductIDs: Set<ProductId> = [.monthlyPremium, .yearlyPremium, .lifetimePremium]`
- [x] 在同一位置添加私有方法：`private func isProProduct(_ productID: String) -> Bool`，实现逻辑为`ProductId(rawValue: productID).map { proProductIDs.contains($0) } ?? false`

### 阶段二：重构checkEntitlements方法权益判断逻辑
- [x] 在checkEntitlements方法第81行替换判断条件：将`if transaction.productID.contains("premium") || transaction.productID.contains("pro")`改为`if isProProduct(transaction.productID)`
- [x] 在同一方法中添加详细日志：在第99行后添加else分支记录非Pro产品的验证日志

### 阶段三：重构updateEntitlementsFromTransaction方法
- [x] 在updateEntitlementsFromTransaction方法第268行替换判断条件：将`if transaction.productID.contains("premium") || transaction.productID.contains("pro")`改为`if isProProduct(transaction.productID)`
- [x] 在同一方法中添加验证日志：在第275行后添加`NSLog("🔍 AnonymousPurchaseService: Transaction更新验证 - \(transaction.productID): \(isProProduct(transaction.productID) ? "Pro产品" : "非Pro产品")")`

### 阶段四：重构processUnfinishedTransactions方法
- [x] 在processUnfinishedTransactions方法第415行替换判断条件：将`if transaction.productID.contains("premium") || transaction.productID.contains("pro")`改为`if isProProduct(transaction.productID)`
- [x] 在同一方法中添加处理日志：在第421行后添加`NSLog("🔍 AnonymousPurchaseService: 未完成交易验证 - \(transaction.productID): \(isProProduct(transaction.productID) ? "Pro产品" : "非Pro产品")")`

### 阶段五：代码清理和文档完善
- [x] 在AnonymousPurchaseService类顶部添加文档注释：解释proProductIDs集合的作用和isProProduct方法的用途
- [x] 验证所有修改：确保没有遗漏的字符串匹配判断逻辑（已确认所有contains("premium")和contains("pro")都已替换）
- [x] 运行编译检查：确保导入语句正确且无编译错误（编译通过，无错误）

-----

## 提交消息区域

```
refactor(purchase): 优化权益判断逻辑使用枚举替代字符串匹配

- feat: 添加基于ProductId枚举的Premium产品集合验证机制
- refactor: 重构checkEntitlements方法使用类型安全的产品ID验证
- refactor: 重构updateEntitlementsFromTransaction方法权益判断逻辑
- refactor: 重构processUnfinishedTransactions方法产品验证
- improve: 添加详细的产品ID验证日志记录
- docs: 完善AnonymousPurchaseService类文档注释

提升代码安全性和可维护性，避免字符串匹配误判风险

注意：发现Pro/Premium命名不一致问题，需要后续统一处理
```