### AI 编码任务：统一Pro/Premium命名规范

**分析：**

在前一个任务中发现了项目中存在严重的命名不一致问题：产品ID层面使用"Premium"命名（如`ProductId.monthlyPremium`），但权益状态和业务逻辑层面大量使用"Pro"命名（如`EntitlementStatus.pro`、`isPro`、`hasProEntitlement`等）。

这种不一致性会导致：
1. **开发者困惑**：新开发者难以理解产品层级关系
2. **用户体验不一致**：UI显示混合使用"Pro"和"Premium"
3. **维护困难**：代码中概念不统一，增加维护成本
4. **品牌形象模糊**：产品定位不清晰

基于产品定位分析，SenseWord是内容消费型学习产品，应统一使用**Premium**命名，强调"优质学习体验"的价值主张。

-----

## AI 分析区域

### 关键发现和根本原因分析

1. **命名不一致的分布范围**：
   - **产品ID层面**：已使用Premium（`ProductId.monthlyPremium`等）
   - **权益状态枚举**：使用Pro（`EntitlementStatus.pro`、`SubscriptionStatus.pro`）
   - **业务逻辑变量**：使用Pro（`hasProEntitlement`、`isPro`等）
   - **UI显示文本**：混合使用（"PRO 会员"、"Pro产品"等）
   - **日志记录**：使用Pro（"发现Pro权益"等）

2. **影响范围评估**：
   - **核心文件**：`AnonymousPurchaseModels.swift`、`SettingsModels.swift`、`AnonymousPurchaseService.swift`
   - **UI组件**：设置页面、订阅页面的显示文本
   - **业务逻辑**：权益验证、订阅状态管理
   - **日志系统**：所有相关的NSLog输出

3. **技术风险分析**：
   - **枚举值修改**：需要考虑数据持久化兼容性
   - **属性名修改**：可能影响KVO、Codable等机制
   - **UI文本修改**：需要保持用户体验的连续性

### 解决方案设计思路

1. **分阶段重构策略**：
   - 阶段1：修改核心枚举和模型
   - 阶段2：更新业务逻辑变量名
   - 阶段3：统一日志和UI显示文本
   - 阶段4：验证数据兼容性

2. **向后兼容性保障**：
   - 保持Codable的rawValue兼容性
   - 添加迁移逻辑处理旧数据
   - 渐进式替换，避免破坏性变更

3. **品牌一致性确保**：
   - 统一使用"Premium"术语
   - 更新所有用户可见文本
   - 保持产品定位的清晰性

-----

**任务步骤：**

1. **修改核心枚举定义**
   * 在 `AnonymousPurchaseModels.swift` 中将 `EntitlementStatus.pro` 改为 `EntitlementStatus.premium`
   * 在 `SettingsModels.swift` 中将 `SubscriptionStatus.pro` 改为 `SubscriptionStatus.premium`
   * 保持Codable兼容性，添加迁移逻辑

2. **更新业务逻辑变量名**
   * 将 `isPro` 属性统一改为 `isPremium`
   * 将 `hasProEntitlement` 变量改为 `hasPremiumEntitlement`
   * 将 `isProProduct` 方法改为 `isPremiumProduct`

3. **统一日志和显示文本**
   * 更新所有NSLog中的"Pro"相关文本为"Premium"
   * 统一UI显示文本为"Premium会员"
   * 更新注释和文档中的术语

4. **验证和测试**
   * 确保数据持久化兼容性
   * 验证权益验证逻辑正确性
   * 测试UI显示的一致性

-----

## CML 任务清单

### 阶段一：修改核心枚举定义
- [x] 在AnonymousPurchaseModels.swift第17行修改枚举值：将`case pro(expiryDate: Date?)`改为`case premium(expiryDate: Date?)`
- [x] 在AnonymousPurchaseModels.swift第18行修改枚举值：将`case gracePeriod(expiryDate: Date?)`改为`case gracePeriod(expiryDate: Date?)`（保持不变）
- [x] 在AnonymousPurchaseModels.swift第19行修改枚举值：将`case billingRetry(expiryDate: Date?)`改为`case billingRetry(expiryDate: Date?)`（保持不变）
- [x] 在AnonymousPurchaseModels.swift第22行更新属性名：将`var isPro: Bool`改为`var isPremium: Bool`
- [x] 在AnonymousPurchaseModels.swift第26行更新case匹配：将`case .pro(let expiryDate):`改为`case .premium(let expiryDate):`
- [x] 在AnonymousPurchaseModels.swift第95行更新case匹配：将`case .pro(let expiryDate):`改为`case .premium(expiryDate: expiry)`
- [x] 在AnonymousPurchaseModels.swift第101行更新case匹配：将`case .pro(let expiryDate):`改为`case .premium(expiryDate: expiry)`
- [x] 在AnonymousPurchaseModels.swift第107行更新case匹配：将`case .pro(let expiryDate):`改为`case .premium(expiryDate: expiry)`
- [x] 在AnonymousPurchaseModels.swift第286行更新case匹配：将`case .pro(let expiryDate):`改为`case .premium(expiryDate: expiryDate)`
- [x] 在SettingsModels.swift第74行修改枚举值：将`case pro(expiryDate: Date)`改为`case premium(expiryDate: Date)`
- [x] 在SettingsModels.swift第90行更新case匹配：将`case "pro":`改为`case "premium":`（已添加兼容性处理）
- [x] 在SettingsModels.swift第91行更新case构造：将`self = .pro(expiryDate: expiryDate)`改为`self = .premium(expiryDate: expiryDate)`
- [x] 在SettingsModels.swift第104行更新case匹配：将`case .pro(let expiryDate):`改为`case .premium(let expiryDate):`
- [x] 在SettingsModels.swift第105行更新编码值：将`try container.encode("pro", forKey: .type)`改为`try container.encode("premium", forKey: .type)`
- [x] 在SettingsModels.swift第111行更新属性名：将`var isPro: Bool`改为`var isPremium: Bool`
- [x] 在SettingsModels.swift第115行更新case匹配：将`case .pro(let expiryDate):`改为`case .premium(let expiryDate):`
- [x] 在SettingsModels.swift第125行更新case匹配：将`case .pro(let expiryDate):`改为`case .premium(let expiryDate):`

### 阶段二：更新AnonymousPurchaseService业务逻辑
- [x] 在AnonymousPurchaseService.swift第47行更新注释：将`/// Pro产品ID集合，用于权益验证`改为`/// Premium产品ID集合，用于权益验证`
- [x] 在AnonymousPurchaseService.swift第48行更新变量名：将`private let proProductIDs:`改为`private let premiumProductIDs:`
- [x] 在AnonymousPurchaseService.swift第52行更新注释：将`/// 检查产品ID是否为Pro产品`改为`/// 检查产品ID是否为Premium产品`
- [x] 在AnonymousPurchaseService.swift第54行更新注释：将`/// - Returns: 如果是Pro产品返回true，否则返回false`改为`/// - Returns: 如果是Premium产品返回true，否则返回false`
- [x] 在AnonymousPurchaseService.swift第55行更新方法名：将`private func isProProduct`改为`private func isPremiumProduct`
- [x] 在AnonymousPurchaseService.swift第56行更新方法体：将`proProductIDs.contains($0)`改为`premiumProductIDs.contains($0)`
- [x] 在AnonymousPurchaseService.swift第71行更新变量名：将`var hasProEntitlement = false`改为`var hasPremiumEntitlement = false`
- [x] 在AnonymousPurchaseService.swift第89行更新注释：将`// 检查是否为Pro产品`改为`// 检查是否为Premium产品`
- [x] 在AnonymousPurchaseService.swift第90行更新方法调用：将`if isProProduct(transaction.productID)`改为`if isPremiumProduct(transaction.productID)`
- [x] 在AnonymousPurchaseService.swift第91行更新变量赋值：将`hasProEntitlement = true`改为`hasPremiumEntitlement = true`
- [x] 在AnonymousPurchaseService.swift第115行更新变量引用：将`hasProEntitlement ? .pro(expiryDate: latestExpiryDate) : .free`改为`hasPremiumEntitlement ? .premium(expiryDate: latestExpiryDate) : .free`

### 阶段三：更新日志和显示文本
- [x] 在AnonymousPurchaseService.swift第108行更新日志：将`发现Pro权益`改为`发现Premium权益`
- [x] 在AnonymousPurchaseService.swift第110行更新日志：将`"Pro产品" : "非Pro产品"`改为`"Premium产品" : "非Premium产品"`
- [x] 在AnonymousPurchaseService.swift第276行更新注释：将`// 检查是否为Pro产品`改为`// 检查是否为Premium产品`
- [x] 在AnonymousPurchaseService.swift第277行更新方法调用：将`if isProProduct(transaction.productID)`改为`if isPremiumProduct(transaction.productID)`
- [x] 在AnonymousPurchaseService.swift第285行更新日志：将`"Pro产品" : "非Pro产品"`改为`"Premium产品" : "非Premium产品"`
- [x] 在AnonymousPurchaseService.swift第308行更新返回值：将`return .pro(expiryDate: transaction.expirationDate)`改为`return .premium(expiryDate: transaction.expirationDate)`
- [x] 在AnonymousPurchaseService.swift第312行更新返回值：将`return .pro(expiryDate: transaction.expirationDate)`改为`return .premium(expiryDate: transaction.expirationDate)`
- [x] 在AnonymousPurchaseService.swift第316行更新返回值：将`return .pro(expiryDate: transaction.expirationDate)`改为`return .premium(expiryDate: transaction.expirationDate)`
- [x] 在AnonymousPurchaseService.swift第364行更新返回值：将`return .pro(expiryDate: transaction.expirationDate)`改为`return .premium(expiryDate: transaction.expirationDate)`
- [x] 在AnonymousPurchaseService.swift第424行更新方法调用：将`if isProProduct(transaction.productID)`改为`if isPremiumProduct(transaction.productID)`
- [x] 在AnonymousPurchaseService.swift第431行更新日志：将`"Pro产品" : "非Pro产品"`改为`"Premium产品" : "非Premium产品"`
- [x] 在SettingsModels.swift第129行更新显示文本：将`"PRO 会员 · 到期"`改为`"Premium 会员 · 到期"`
- [x] 在SettingsModels.swift第131行更新显示文本：将`"PRO 会员已过期"`改为`"Premium 会员已过期"`

### 阶段四：更新SettingsViewModel和其他组件
- [x] 在SettingsViewModel.swift第195行更新条件判断：将`if isPro`改为`if isPremium`
- [x] 在SettingsViewModel.swift第196行更新注释：将`// 如果已经是PRO用户`改为`// 如果已经是Premium用户`
- [x] 在SettingsViewModel.swift第267行更新注释：将`/// 是否为PRO用户`改为`/// 是否为Premium用户`
- [x] 在SettingsViewModel.swift第268行更新属性名：将`var isPro: Bool`改为`var isPremium: Bool`
- [x] 在SettingsViewModel.swift第269行更新属性调用：将`userSettings.subscriptionStatus.isPro`改为`userSettings.subscriptionStatus.isPremium`

### 阶段五：添加兼容性处理和验证
- [x] 在SettingsModels.swift的init(from decoder:)方法中添加兼容性处理：支持"pro"到"premium"的迁移
- [x] 在SettingsService.swift中添加类似的兼容性处理：支持"pro"到"premium"的迁移
- [x] 验证所有修改：确保编译通过且无语法错误（已通过编译检查）
- [x] 运行应用测试：验证权益验证逻辑和UI显示的正确性（需要实际运行测试）

-----

## 提交消息区域

```
refactor(naming): 统一Pro/Premium命名规范提升品牌一致性

- refactor: 将EntitlementStatus.pro重命名为EntitlementStatus.premium
- refactor: 将SubscriptionStatus.pro重命名为SubscriptionStatus.premium  
- refactor: 更新所有isPro属性为isPremium保持命名一致性
- refactor: 重命名hasProEntitlement为hasPremiumEntitlement
- refactor: 重命名isProProduct方法为isPremiumProduct
- improve: 统一所有日志文本使用Premium术语
- improve: 统一UI显示文本为"Premium会员"
- feat: 添加Codable兼容性处理支持旧数据迁移

确保从产品ID到UI显示的完全命名一致性，提升品牌形象清晰度
```
