# 索引下载桥接函数实现任务

## TODO 用户需求
1. 暂时不实现设置UI，只是实现函数方法，这个函数可以获取 usersetting 当中的 learning Language 和 scaffoldinglanguage 调用函数下载索引
2. UI 部分我暂时还没有想好怎么实现，所以只实现可供 UI 直接调用的函数。

## AI 分析区域

### 关键发现和根本原因分析

1. **现状分析**：
   - IndexDownloadService 已完整实现，具备完整的下载功能
   - 用户设置系统已实现，但只有 `preferredLanguage` 单一语言设置
   - DIContainer 中缺少 IndexDownloadService 的依赖注入配置
   - 缺少连接用户设置和下载服务的桥接层

2. **语言设置现状调研**：
   - **当前设置界面只有一个语言选项**："单词查询语言"
   - **语言概念映射**：用户的"单词查询语言"实际上是脚手架语言(scaffoldingLanguage)
   - **隐含的学习语言**：系统默认学习语言为英语，用户学习英语单词
   - **用户选择"中文"** = scaffoldingLanguage: .chinese，learningLanguage: .english（隐含）

3. **技术问题的深层机制**：
   - 当前用户设置模型只有 `preferredLanguage: LanguageCode`，但这实际上是脚手架语言
   - IndexDownloadService 需要 `learningLanguage` 和 `scaffoldingLanguage` 两个参数
   - 需要建立语言映射策略：learningLanguage=.english，scaffoldingLanguage=用户的preferredLanguage

4. **解决方案设计思路**：
   - 创建 IndexDownloadManager 作为桥接服务，支持多语言对架构
   - 在 DIContainer 中添加 IndexDownloadService 的依赖注入
   - 设计明确的多语言对接口，为未来扩展做好准备
   - 当前实现默认语言对策略，但接口支持任意语言对组合

### 多语言对架构设计

```
用户设置层
    ↓
语言对配置管理器 (LanguagePairConfigManager)
    ↓
IndexDownloadManager (多语言对桥接层)
    ↓ (支持任意 learningLang + scaffoldingLang 组合)
IndexDownloadService (核心下载逻辑)
    ↓
API + 本地数据库更新
```

### 多语言对支持策略

**当前实现（Phase 1）**：
- 默认语言对：learningLanguage=.english, scaffoldingLanguage=用户preferredLanguage
- 用户选择"中文" → 下载英语-中文语言对索引

**未来扩展（Phase 2+）**：
- 支持多个学习语言：英语、西班牙语、法语等
- 支持多个脚手架语言：中文、英语、日语等
- 用户可配置多个语言对：英语-中文、西班牙语-中文、法语-英语等
- 接口设计：`downloadIndexForLanguagePair(learningLang: LanguageCode, scaffoldingLang: LanguageCode)`

**架构优势**：
- **明确的语言对概念**：接口明确区分学习语言和脚手架语言
- **可扩展性**：添加新语言对只需配置，无需修改核心逻辑
- **向后兼容**：当前默认策略不影响未来多语言对支持

## CML 任务清单

### 阶段一：扩展DIContainer支持IndexDownloadService
- [x] 在 `iOS/SensewordApp/DI/DIContainer.swift` 第105行后添加IndexDownloadService私有属性：`private var _indexDownloadService: IndexDownloadServiceProtocol?`
- [x] 在同一文件第156行后添加createIndexDownloadService工厂方法：接收sqliteManager、searchAPIAdapter、localIndexService参数，返回IndexDownloadService实例
- [x] 在同一文件第213行后添加indexDownloadService计算属性：提供已初始化的IndexDownloadService访问，包含fatalError保护

### 阶段二：创建多语言对配置管理器
- [x] 创建 `iOS/SensewordApp/Services/Business/LanguagePairConfigManager.swift` 文件：定义LanguagePairConfigManagerProtocol协议，支持多语言对配置管理
- [x] 在同一文件中实现getCurrentLanguagePairs()方法：返回当前用户的活跃语言对列表，当前返回默认的英语-用户偏好语言对
- [x] 在同一文件中实现getDefaultLanguagePair()方法：基于用户设置返回默认语言对(learningLanguage=.english, scaffoldingLanguage=preferredLanguage)

### 阶段三：创建IndexDownloadManager多语言对桥接服务
- [x] 创建 `iOS/SensewordApp/Services/Business/IndexDownloadManager.swift` 文件：定义IndexDownloadManagerProtocol协议，支持多语言对下载管理
- [x] 在同一文件中实现IndexDownloadManager类：依赖SettingsService、IndexDownloadService和LanguagePairConfigManager
- [x] 在同一文件中实现downloadIndexForCurrentUser()方法：获取默认语言对，调用IndexDownloadService.downloadIndexForLanguagePair
- [x] 在同一文件中实现downloadIndexForLanguagePair(learningLang, scaffoldingLang)方法：直接支持任意语言对下载，为未来扩展做准备

### 阶段四：扩展DIContainer支持多语言对服务
- [x] 在 `iOS/SensewordApp/DI/DIContainer.swift` 中添加LanguagePairConfigManager私有属性：`private var _languagePairConfigManager: LanguagePairConfigManagerProtocol?`
- [x] 在同一文件中添加createLanguagePairConfigManager工厂方法：依赖SettingsService，返回LanguagePairConfigManager实例
- [x] 在同一文件中添加IndexDownloadManager私有属性：`private var _indexDownloadManager: IndexDownloadManagerProtocol?`
- [x] 在同一文件中添加createIndexDownloadManager工厂方法：依赖SettingsService、IndexDownloadService和LanguagePairConfigManager
- [x] 在同一文件中添加相应的计算属性：提供已初始化的服务访问

### 阶段五：创建便捷的全局访问接口
- [x] 在 `iOS/SensewordApp/Services/Business/IndexDownloadManager.swift` 中添加静态便捷方法：`static func downloadIndexForCurrentUser() async throws -> Bool`，支持当前用户默认语言对下载
- [x] 在同一文件中添加静态多语言对方法：`static func downloadIndexForLanguagePair(learningLang: LanguageCode, scaffoldingLang: LanguageCode) async throws -> Bool`，支持任意语言对下载
- [x] 在同一文件中添加静态进度查询方法：`static func getCurrentDownloadProgress() -> IndexDownloadProgress?`，提供下载状态查询
- [x] 在同一文件中添加错误处理和日志记录：确保所有操作都有适当的NSLog输出和错误传播

## 实施完成状态

✅ **所有CML任务已完成** - 5个阶段共16个具体任务全部实现
✅ **编译检查通过** - 无语法错误或类型错误
✅ **架构设计实现** - 多语言对支持架构已建立

## 提交消息区域

```
feat(index-download): 实现多语言对索引下载架构

- 创建LanguagePairConfigManager支持多语言对配置管理
- 实现IndexDownloadManager多语言对桥接服务
- 扩展DIContainer支持多语言对服务依赖注入
- 提供明确的多语言对接口设计，支持未来扩展
- 当前实现默认语言对策略(英语+用户偏好语言)
- 提供静态便捷方法支持任意语言对下载

架构特点:
- 明确区分学习语言和脚手架语言概念
- 支持多语言对扩展(英语-中文、西班牙语-中文等)
- 向后兼容当前单一语言对实现

新增文件:
- iOS/SensewordApp/Services/Business/LanguagePairConfigManager.swift
- iOS/SensewordApp/Services/Business/IndexDownloadManager.swift

修改文件:
- iOS/SensewordApp/DI/DIContainer.swift

Closes: 索引下载UI调用接口需求
```