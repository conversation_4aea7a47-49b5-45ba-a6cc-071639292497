# KDD-038 精细化单词处理状态管理 - 关键帧可视化

## 🎯 系统架构概览

### 1. 整体系统架构图

```mermaid
graph TB
    subgraph "📊 SenseWord Content Factory"
        DB[(🗄️ senseword_content_v4.db)]
        WPQ[📋 word_processing_queue]
        WFP[📚 words_for_publish]
        TTS[🎵 tts_assets]
    end

    subgraph "🔧 核心处理模块"
        KWP[⚙️ KanbanWordProcessor<br/>状态管理器]
        WLE[📤 WordListExporter<br/>信息管道]
        PM[📈 ProgressMonitor<br/>进度监控]
    end

    subgraph "🤖 外部处理脚本"
        CG[✍️ Content Generation<br/>内容生成脚本]
        AI[🧠 AI Review<br/>Vertex AI批处理]
        TG[🔗 TTS ID Generation<br/>TTS标记脚本]
        AG[🎵 Audio Generation<br/>音频生成脚本]
    end

    subgraph "📁 文件输出"
        F1[📄 words_for_content.txt]
        F2[📄 words_for_ai_review.txt]
        F3[📄 words_for_tts_id.txt]
        F4[📄 words_for_audio.txt]
    end

    %% 数据库连接
    DB --> WPQ
    DB --> WFP
    DB --> TTS

    %% 核心模块连接
    KWP --> WPQ
    WLE --> WPQ
    PM --> WPQ

    %% 文件输出连接
    WLE --> F1
    WLE --> F2
    WLE --> F3
    WLE --> F4

    %% 外部脚本连接
    F1 --> CG
    F2 --> AI
    F3 --> TG
    F4 --> AG

    %% 状态回调
    CG --> KWP
    AI --> KWP
    TG --> KWP
    AG --> KWP

    %% 样式定义
    classDef database fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef processor fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef exporter fill:#E1D5E7,stroke:#000000,stroke-width:3px,color:#000000
    classDef external fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    classDef files fill:#F8CECC,stroke:#000000,stroke-width:2px,color:#000000

    class DB,WPQ,WFP,TTS database
    class KWP,PM processor
    class WLE exporter
    class CG,AI,TG,AG external
    class F1,F2,F3,F4 files
```

## 🔄 核心工作流程图

### 2. 完整处理流程时序图

```mermaid
sequenceDiagram
    participant U as 👤 用户
    participant KWP as ⚙️ KanbanWordProcessor
    participant WLE as 📤 WordListExporter
    participant DB as 🗄️ Database
    participant F as 📄 TXT文件
    participant VS as 🤖 外部脚本

    Note over U,VS: 🚀 阶段1: 单词入队
    U->>KWP: add_words_to_queue(["progressive", "innovative"])
    KWP->>DB: INSERT INTO word_processing_queue
    Note over DB: contentGenerated=FALSE<br/>contentAiReviewed=FALSE<br/>ttsIdGenerated=FALSE<br/>audioGenerated=FALSE<br/>readyForPublish=FALSE

    Note over U,VS: 📝 阶段2: 内容生成
    U->>WLE: export_pending_content_generation()
    WLE->>DB: SELECT WHERE contentGenerated=FALSE
    DB-->>WLE: ["progressive", "innovative"]
    WLE->>F: 写入 words_for_content.txt
    F-->>VS: 外部内容生成脚本读取
    VS->>VS: 生成内容并处理
    VS->>KWP: batch_update_stage(words, "contentGenerated")
    KWP->>DB: UPDATE SET contentGenerated=TRUE

    Note over U,VS: 🧠 阶段3: AI审核
    U->>WLE: export_pending_ai_review()
    WLE->>DB: SELECT WHERE contentGenerated=TRUE AND contentAiReviewed=FALSE
    DB-->>WLE: ["progressive", "innovative"]
    WLE->>F: 写入 words_for_ai_review.txt
    F-->>VS: Vertex AI批处理脚本读取
    VS->>VS: 生成JSONL并提交Vertex AI
    VS->>KWP: batch_update_stage(words, "contentAiReviewed")
    KWP->>DB: UPDATE SET contentAiReviewed=TRUE

    Note over U,VS: 🔗 阶段4: TTS ID生成
    U->>WLE: export_pending_tts_id_generation()
    WLE->>DB: SELECT WHERE contentAiReviewed=TRUE AND ttsIdGenerated=FALSE
    DB-->>WLE: ["progressive", "innovative"]
    WLE->>F: 写入 words_for_tts_id.txt
    F-->>VS: TTS标记脚本读取
    VS->>VS: 生成TTS ID并标记
    VS->>KWP: batch_update_stage(words, "ttsIdGenerated")
    KWP->>DB: UPDATE SET ttsIdGenerated=TRUE

    Note over U,VS: 🎵 阶段5: 音频生成
    U->>WLE: export_pending_audio_generation()
    WLE->>DB: SELECT WHERE ttsIdGenerated=TRUE AND audioGenerated=FALSE
    DB-->>WLE: ["progressive", "innovative"]
    WLE->>F: 写入 words_for_audio.txt
    F-->>VS: 音频生成脚本读取
    VS->>VS: 生成音频文件
    VS->>KWP: batch_update_stage(words, "audioGenerated")
    KWP->>DB: UPDATE SET audioGenerated=TRUE

    Note over U,VS: ✅ 阶段6: 发布准备
    VS->>KWP: batch_update_stage(words, "readyForPublish")
    KWP->>DB: UPDATE SET readyForPublish=TRUE
```
## 📊 数据结构转化过程

### 3. 关键数据结构生命周期

```mermaid
graph LR
    subgraph "🎯 初始状态"
        A["📝 单词: progressive<br/>contentGenerated: FALSE<br/>contentAiReviewed: FALSE<br/>ttsIdGenerated: FALSE<br/>audioGenerated: FALSE<br/>readyForPublish: FALSE"]
    end

    subgraph "✍️ 内容生成后"
        B["📝 单词: progressive<br/>contentGenerated: TRUE<br/>contentAiReviewed: FALSE<br/>ttsIdGenerated: FALSE<br/>audioGenerated: FALSE<br/>readyForPublish: FALSE"]
    end

    subgraph "🧠 AI审核后"
        C["📝 单词: progressive<br/>contentGenerated: TRUE<br/>contentAiReviewed: TRUE<br/>ttsIdGenerated: FALSE<br/>audioGenerated: FALSE<br/>readyForPublish: FALSE"]
    end

    subgraph "🔗 TTS标记后"
        D["📝 单词: progressive<br/>contentGenerated: TRUE<br/>contentAiReviewed: TRUE<br/>ttsIdGenerated: TRUE<br/>audioGenerated: FALSE<br/>readyForPublish: FALSE"]
    end

    subgraph "🎵 音频生成后"
        E["📝 单词: progressive<br/>contentGenerated: TRUE<br/>contentAiReviewed: TRUE<br/>ttsIdGenerated: TRUE<br/>audioGenerated: TRUE<br/>readyForPublish: FALSE"]
    end

    subgraph "✅ 发布就绪"
        F["📝 单词: progressive<br/>contentGenerated: TRUE<br/>contentAiReviewed: TRUE<br/>ttsIdGenerated: TRUE<br/>audioGenerated: TRUE<br/>readyForPublish: TRUE"]
    end

    A -->|内容生成脚本处理| B
    B -->|Vertex AI批处理| C
    C -->|TTS标记脚本处理| D
    D -->|音频生成脚本处理| E
    E -->|最终确认| F

    %% 样式定义
    classDef initial fill:#FFE6E6,stroke:#000000,stroke-width:2px,color:#000000
    classDef content fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef review fill:#E6FFE6,stroke:#000000,stroke-width:2px,color:#000000
    classDef tts fill:#FFF0E6,stroke:#000000,stroke-width:2px,color:#000000
    classDef audio fill:#F0E6FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef ready fill:#E6FFF0,stroke:#000000,stroke-width:3px,color:#000000

    class A initial
    class B content
    class C review
    class D tts
    class E audio
    class F ready
```

### 4. 看板式状态流转图

```mermaid
flowchart TD
    A["📝 待内容生成<br/>contentGenerated: FALSE<br/>contentAiReviewed: FALSE<br/>ttsIdGenerated: FALSE<br/>audioGenerated: FALSE<br/>readyForPublish: FALSE"]

    B["🧠 待AI审核<br/>contentGenerated: TRUE<br/>contentAiReviewed: FALSE<br/>ttsIdGenerated: FALSE<br/>audioGenerated: FALSE<br/>readyForPublish: FALSE"]

    C["🔗 待TTS标记<br/>contentGenerated: TRUE<br/>contentAiReviewed: TRUE<br/>ttsIdGenerated: FALSE<br/>audioGenerated: FALSE<br/>readyForPublish: FALSE"]

    D["🎵 待音频生成<br/>contentGenerated: TRUE<br/>contentAiReviewed: TRUE<br/>ttsIdGenerated: TRUE<br/>audioGenerated: FALSE<br/>readyForPublish: FALSE"]

    E["✅ 发布就绪<br/>contentGenerated: TRUE<br/>contentAiReviewed: TRUE<br/>ttsIdGenerated: TRUE<br/>audioGenerated: TRUE<br/>readyForPublish: TRUE"]

    A -->|内容生成完成| B
    B -->|AI审核通过| C
    C -->|TTS ID生成完成| D
    D -->|音频生成完成| E

    %% 样式定义
    classDef stage1 fill:#FFE6E6,stroke:#000000,stroke-width:2px,color:#000000
    classDef stage2 fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef stage3 fill:#E6FFE6,stroke:#000000,stroke-width:2px,color:#000000
    classDef stage4 fill:#FFF0E6,stroke:#000000,stroke-width:2px,color:#000000
    classDef stage5 fill:#E6FFF0,stroke:#000000,stroke-width:3px,color:#000000

    class A stage1
    class B stage2
    class C stage3
    class D stage4
    class E stage5
```

## 📤 文件输出流程详解

### 5. WordListExporter 工作流程

```mermaid
flowchart TD
    subgraph "🔍 状态筛选"
        A[📋 用户调用导出函数]
        B{🎯 选择筛选条件}
        C[📝 待内容生成<br/>contentGenerated=FALSE]
        D[🧠 待AI审核<br/>contentGenerated=TRUE<br/>AND contentAiReviewed=FALSE]
        E[🔗 待TTS标记<br/>contentAiReviewed=TRUE<br/>AND ttsIdGenerated=FALSE]
        F[🎵 待音频生成<br/>ttsIdGenerated=TRUE<br/>AND audioGenerated=FALSE]
    end

    subgraph "🗄️ 数据库查询"
        G[📊 执行SQL查询]
        H[📋 获取单词列表]
        I[📈 统计单词数量]
    end

    subgraph "📁 文件生成"
        J[📂 检查输出目录]
        K[📄 创建TXT文件]
        L[✍️ 写入单词列表<br/>一行一个单词]
        M[✅ 返回文件路径]
    end

    subgraph "🤖 外部脚本集成"
        N[📖 外部脚本读取文件]
        O[⚙️ 执行相应处理]
        P[🔄 状态回调更新]
    end

    A --> B
    B --> C
    B --> D
    B --> E
    B --> F

    C --> G
    D --> G
    E --> G
    F --> G

    G --> H
    H --> I
    I --> J
    J --> K
    K --> L
    L --> M

    M --> N
    N --> O
    O --> P

    %% 样式定义
    classDef filter fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef query fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef file fill:#E1D5E7,stroke:#000000,stroke-width:2px,color:#000000
    classDef external fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000

    class A,B,C,D,E,F filter
    class G,H,I query
    class J,K,L,M file
    class N,O,P external
```

### 6. 真实数据处理示例

```mermaid
graph TB
    subgraph "📊 数据库当前状态"
        DB["🗄️ word_processing_queue<br/><br/>📝 progressive: T,F,F,F,F<br/>📝 innovative: T,T,F,F,F<br/>📝 sustainable: T,T,T,F,F<br/>📝 collaborative: T,T,T,T,F<br/>📝 transformative: T,T,T,T,T"]
    end

    subgraph "🎯 筛选结果"
        F1["📄 words_for_ai_review.txt<br/><br/>progressive"]
        F2["📄 words_for_tts_id.txt<br/><br/>sustainable"]
        F3["📄 words_for_audio.txt<br/><br/>collaborative"]
        F4["📄 ready_for_publish.txt<br/><br/>transformative"]
    end

    subgraph "🤖 外部处理"
        S1["🧠 Vertex AI<br/>处理 progressive"]
        S2["🔗 TTS标记<br/>处理 sustainable"]
        S3["🎵 音频生成<br/>处理 collaborative"]
        S4["✅ 发布系统<br/>处理 transformative"]
    end

    subgraph "🔄 状态更新"
        U1["📝 progressive: T,T,F,F,F"]
        U2["📝 sustainable: T,T,T,T,F"]
        U3["📝 collaborative: T,T,T,T,T"]
        U4["📝 transformative: 已发布"]
    end

    DB --> F1
    DB --> F2
    DB --> F3
    DB --> F4

    F1 --> S1
    F2 --> S2
    F3 --> S3
    F4 --> S4

    S1 --> U1
    S2 --> U2
    S3 --> U3
    S4 --> U4

    %% 样式定义
    classDef database fill:#E8F4FD,stroke:#000000,stroke-width:3px,color:#000000
    classDef files fill:#F8CECC,stroke:#000000,stroke-width:2px,color:#000000
    classDef scripts fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    classDef updates fill:#E1D5E7,stroke:#000000,stroke-width:2px,color:#000000

    class DB database
    class F1,F2,F3,F4 files
    class S1,S2,S3,S4 scripts
    class U1,U2,U3,U4 updates
```

## 📝 详细流程说明

### 🎯 核心设计理念

**看板式状态管理**: 系统采用5个布尔字段来表示单词在处理流程中的状态，就像看板系统一样直观清晰。每个字段代表一个处理阶段的完成状态：

- `contentGenerated`: 内容已生产 ✍️
- `contentAiReviewed`: 内容AI审核完成 🧠
- `ttsIdGenerated`: TTS ID已生成 🔗
- `audioGenerated`: 音频已生产 🎵
- `readyForPublish`: 发布就绪 ✅

**信息管道设计**: 系统不进行内部批量处理，而是作为工作流程的信息管道，通过TXT文件与外部脚本进行数据交换。

### 🔄 完整工作流程详解

#### 阶段1: 单词入队 📋
```
用户调用: add_words_to_queue(["progressive", "innovative"], "en", "zh")
数据库操作: INSERT INTO word_processing_queue
初始状态: 所有布尔字段设为FALSE
```

#### 阶段2: 内容生成 ✍️
```
1. 调用: export_pending_content_generation("en", "zh", "/output")
2. 查询: SELECT word WHERE contentGenerated=FALSE
3. 输出: words_for_content.txt (一行一个单词)
4. 外部脚本读取文件并生成内容
5. 回调: batch_update_stage(words, "contentGenerated", "en", "zh")
6. 更新: UPDATE SET contentGenerated=TRUE
```

#### 阶段3: AI审核 🧠
```
1. 调用: export_pending_ai_review("en", "zh", "/output")
2. 查询: SELECT word WHERE contentGenerated=TRUE AND contentAiReviewed=FALSE
3. 输出: words_for_ai_review.txt
4. Vertex AI批处理脚本读取并处理
5. 回调: batch_update_stage(words, "contentAiReviewed", "en", "zh")
6. 更新: UPDATE SET contentAiReviewed=TRUE
```

#### 阶段4: TTS标记 🔗
```
1. 调用: export_pending_tts_id_generation("en", "zh", "/output")
2. 查询: SELECT word WHERE contentAiReviewed=TRUE AND ttsIdGenerated=FALSE
3. 输出: words_for_tts_id.txt
4. TTS标记脚本读取并生成TTS ID
5. 回调: batch_update_stage(words, "ttsIdGenerated", "en", "zh")
6. 更新: UPDATE SET ttsIdGenerated=TRUE
```

#### 阶段5: 音频生成 🎵
```
1. 调用: export_pending_audio_generation("en", "zh", "/output")
2. 查询: SELECT word WHERE ttsIdGenerated=TRUE AND audioGenerated=FALSE
3. 输出: words_for_audio.txt
4. 音频生成脚本读取并生成音频文件
5. 回调: batch_update_stage(words, "audioGenerated", "en", "zh")
6. 更新: UPDATE SET audioGenerated=TRUE
```

#### 阶段6: 发布准备 ✅
```
1. 最终确认所有处理完成
2. 回调: batch_update_stage(words, "readyForPublish", "en", "zh")
3. 更新: UPDATE SET readyForPublish=TRUE
4. 单词进入发布就绪状态
```

### 🎯 关键优势

1. **状态透明**: 通过5个布尔字段清晰展示每个单词的处理进度
2. **灵活筛选**: 可以精确查询任意状态组合的单词
3. **工作流集成**: 通过TXT文件与外部脚本无缝对接
4. **错误恢复**: 可以轻松重试特定阶段的处理
5. **进度监控**: 实时了解整体处理进度和瓶颈

### 📊 实际使用示例

假设数据库中有5个单词处于不同状态：
- `progressive`: [TRUE, FALSE, FALSE, FALSE, FALSE] - 待AI审核
- `innovative`: [TRUE, TRUE, FALSE, FALSE, FALSE] - 待TTS标记
- `sustainable`: [TRUE, TRUE, TRUE, FALSE, FALSE] - 待音频生成
- `collaborative`: [TRUE, TRUE, TRUE, TRUE, FALSE] - 待发布确认
- `transformative`: [TRUE, TRUE, TRUE, TRUE, TRUE] - 发布就绪

通过不同的导出函数，可以精确获取每个阶段待处理的单词：
- `export_pending_ai_review()` → `progressive`
- `export_pending_tts_id_generation()` → `innovative`
- `export_pending_audio_generation()` → `sustainable`
- `export_ready_for_publish()` → `collaborative`, `transformative`

这样的设计实现了真正的精细化单词处理状态管理，完全解决了原有系统只能批量处理的局限性。
```
```
```