# KDD-023 内容搜索系统关键帧可视化

## 系统架构概览

### 整体数据流架构图

```mermaid
graph TB
    %% 用户交互层
    User[👤 用户] --> SearchUI[🔍 搜索界面]

    %% 前端业务层
    SearchUI --> SearchVM[🎯 搜索ViewModel]
    SearchVM --> SearchBiz[💼 搜索业务服务]

    %% 适配器层
    SearchBiz --> WordAdapter[🔌 单词API适配器]
    SearchBiz --> IndexAdapter[📊 索引API适配器]
    SearchBiz --> LocalIndex[💾 本地索引服务]

    %% 后端服务层
    WordAdapter --> WordAPI[🌐 单词API]
    IndexAdapter --> IndexAPI[📈 索引同步API]

    %% 数据存储层
    WordAPI --> MainDB[(🗄️ 主数据库<br/>word_definitions)]
    IndexAPI --> MainDB
    LocalIndex --> LocalDB[(📱 本地SQLite<br/>search_index)]

    %% 降级策略流程
    SearchBiz --> Cache{💰 缓存检查}
    Cache -->|有完整缓存| FullContent[✨ 完整AI内容]
    Cache -->|仅有索引| BasicInfo[📝 基础信息<br/>音标+释义]
    Cache -->|无数据| NetworkError[❌ 网络提示]

    %% 样式定义
    classDef userLayer fill:#FFE4E1,stroke:#000000,stroke-width:2px,color:#000000
    classDef frontendLayer fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef adapterLayer fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    classDef backendLayer fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef dataLayer fill:#F5F5DC,stroke:#000000,stroke-width:3px,color:#000000
    classDef fallbackLayer fill:#FFE4B5,stroke:#000000,stroke-width:2px,color:#000000

    class User,SearchUI userLayer
    class SearchVM,SearchBiz frontendLayer
    class WordAdapter,IndexAdapter,LocalIndex adapterLayer
    class WordAPI,IndexAPI backendLayer
    class MainDB,LocalDB dataLayer
    class Cache,FullContent,BasicInfo,NetworkError fallbackLayer
```

## 技术方案蓝图数据结构生命周期

### 统一DIContainer依赖注入架构

```mermaid
graph TB
    %% 依赖注入容器
    DIContainer[🔧 统一DIContainer<br/>管理所有服务依赖] --> Infrastructure[🏗️ 基础设施层]
    DIContainer --> Adapters[🔌 Adapter层]
    DIContainer --> Business[💼 Business层]
    DIContainer --> ViewModels[🎯 ViewModel层]

    %% 基础设施层
    Infrastructure --> SQLiteManager[💾 SQLiteManager<br/>数据库连接管理]
    Infrastructure --> CacheService[🗄️ CacheService<br/>分层缓存策略]
    Infrastructure --> APIClient[🌐 APIClient<br/>HTTP客户端]

    %% Adapter层（迁移自AdapterContainer）
    Adapters --> WordAPIAdapter[📖 WordAPIAdapter<br/>单词API转译]
    Adapters --> SearchAPIAdapter[🔍 SearchAPIAdapter<br/>索引API转译]
    Adapters --> AuthAPIAdapter[🔐 AuthAPIAdapter<br/>认证API转译]

    %% Business层（新增）
    Business --> SearchService[🎯 SearchService<br/>搜索业务协调]
    Business --> LocalIndexService[📊 LocalIndexService<br/>本地索引管理]
    Business --> ContentService[📝 ContentService<br/>内容格式化]

    %% ViewModel层
    ViewModels --> SearchViewModel[🖥️ SearchViewModel<br/>搜索状态管理]

    %% 依赖关系
    SearchService --> WordAPIAdapter
    SearchService --> SearchAPIAdapter
    SearchService --> LocalIndexService
    SearchService --> CacheService
    LocalIndexService --> SQLiteManager
    CacheService --> SQLiteManager
    WordAPIAdapter --> APIClient
    SearchAPIAdapter --> APIClient
    SearchViewModel --> SearchService

    %% 样式定义
    classDef containerLayer fill:#FFB6C1,stroke:#000000,stroke-width:3px,color:#000000
    classDef infrastructureLayer fill:#E0E0E0,stroke:#000000,stroke-width:2px,color:#000000
    classDef adapterLayer fill:#98FB98,stroke:#000000,stroke-width:2px,color:#000000
    classDef businessLayer fill:#87CEEB,stroke:#000000,stroke-width:2px,color:#000000
    classDef viewModelLayer fill:#DDA0DD,stroke:#000000,stroke-width:2px,color:#000000

    class DIContainer containerLayer
    class Infrastructure,SQLiteManager,CacheService,APIClient infrastructureLayer
    class Adapters,WordAPIAdapter,SearchAPIAdapter,AuthAPIAdapter adapterLayer
    class Business,SearchService,LocalIndexService,ContentService businessLayer
    class ViewModels,SearchViewModel viewModelLayer
```

### 搜索数据模型生命周期变化

```mermaid
stateDiagram-v2
    [*] --> UserInput: 用户开始输入

    UserInput --> Debouncing: 防抖处理(300ms)
    Debouncing --> LocalQuery: 本地索引查询

    LocalQuery --> SuggestionGenerated: 生成SearchSuggestion[]
    SuggestionGenerated --> DisplaySuggestions: 显示搜索建议

    DisplaySuggestions --> UserSelection: 用户选择建议
    UserSelection --> CacheCheck: 检查缓存状态

    CacheCheck --> FullCache: CacheStatus.full
    CacheCheck --> IndexCache: CacheStatus.index
    CacheCheck --> NoCache: CacheStatus.none

    FullCache --> DisplayContent: 显示完整内容
    IndexCache --> DisplayBasic: 显示基础信息
    NoCache --> NetworkRequest: 发起网络请求

    NetworkRequest --> ContentGenerated: AI生成内容
    NetworkRequest --> NetworkError: 网络错误

    ContentGenerated --> UpdateCache: 更新缓存
    UpdateCache --> DisplayContent

    NetworkError --> FallbackToIndex: 降级到索引
    FallbackToIndex --> DisplayBasic

    DisplayContent --> [*]: 完成
    DisplayBasic --> [*]: 完成
```

### 缓存策略分支变化图

```mermaid
flowchart TD
    Start[🔍 搜索请求] --> CacheCheck{💾 检查缓存层级}

    %% 第一层：内存缓存
    CacheCheck -->|检查内存缓存| MemoryCache{🧠 内存缓存}
    MemoryCache -->|命中| MemoryHit[⚡ 内存命中<br/>响应时间: <10ms]
    MemoryCache -->|未命中| DiskCache{💿 磁盘缓存}

    %% 第二层：磁盘缓存
    DiskCache -->|命中| DiskHit[💾 磁盘命中<br/>响应时间: <100ms]
    DiskCache -->|未命中| LocalIndex{📊 本地索引}

    %% 第三层：本地索引
    LocalIndex -->|有索引数据| IndexHit[📝 索引命中<br/>基础信息<br/>响应时间: <100ms]
    LocalIndex -->|无索引数据| NetworkRequest[🌐 网络请求<br/>响应时间: <500ms]

    %% 网络请求结果
    NetworkRequest -->|成功| NetworkSuccess[✅ 网络成功<br/>AI生成内容]
    NetworkRequest -->|失败| NetworkFail[❌ 网络失败<br/>显示错误提示]

    %% 缓存更新流程
    NetworkSuccess --> UpdateCaches[🔄 更新缓存]
    UpdateCaches --> UpdateMemory[🧠 更新内存缓存]
    UpdateCaches --> UpdateDisk[💿 更新磁盘缓存]
    UpdateCaches --> UpdateIndex[📊 更新本地索引]

    %% 最终结果
    MemoryHit --> Result[📱 显示结果]
    DiskHit --> Result
    IndexHit --> Result
    NetworkSuccess --> Result
    NetworkFail --> ErrorDisplay[⚠️ 错误显示]

    %% 样式定义
    classDef cacheLayer fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef hitLayer fill:#90EE90,stroke:#000000,stroke-width:2px,color:#000000
    classDef networkLayer fill:#FFE4B5,stroke:#000000,stroke-width:2px,color:#000000
    classDef updateLayer fill:#DDA0DD,stroke:#000000,stroke-width:2px,color:#000000
    classDef resultLayer fill:#FFB6C1,stroke:#000000,stroke-width:3px,color:#000000

    class CacheCheck,MemoryCache,DiskCache,LocalIndex cacheLayer
    class MemoryHit,DiskHit,IndexHit hitLayer
    class NetworkRequest,NetworkSuccess,NetworkFail networkLayer
    class UpdateCaches,UpdateMemory,UpdateDisk,UpdateIndex updateLayer
    class Result,ErrorDisplay resultLayer
```

### 搜索UI状态转换图

```mermaid
stateDiagram-v2
    [*] --> Inactive: 应用启动

    Inactive --> Activating: 下拉手势开始
    Activating --> Inactive: 手势取消
    Activating --> Active: 手势达到阈值

    Active --> Searching: 用户输入文本
    Active --> Inactive: 取消搜索

    Searching --> ShowingResults: 获得搜索结果
    Searching --> Error: 搜索失败
    Searching --> Active: 清空输入

    ShowingResults --> Searching: 修改搜索词
    ShowingResults --> ContentView: 选择结果
    ShowingResults --> Inactive: 取消搜索

    Error --> Searching: 重试搜索
    Error --> Inactive: 取消搜索

    ContentView --> Inactive: 返回主界面
    ContentView --> Searching: 新搜索

    note right of Activating
        triggerScale: 1.0 → 1.2
        blurRadius: 0 → 20
    end note

    note right of ShowingResults
        suggestions: [SearchSuggestion]
        isLoading: false
    end note
```

### 数据模型关系图

```mermaid
classDiagram
    %% 搜索相关数据模型
    class SearchSuggestion {
        +String word
        +String definition
        +Double relevanceScore
        +Bool hasFullContent
        +mock() SearchSuggestion
    }

    class CacheStatus {
        <<enumeration>>
        +full(WordDefinitionResponse)
        +index(WordIndexItem)
        +none
        +hasContent Bool
        +displayWord String?
    }

    class SearchConfig {
        +Int suggestionLimit
        +TimeInterval debounceDelay
        +TimeInterval cacheTimeout
        +Int responseTimeThreshold
    }

    class SearchError {
        <<enumeration>>
        +networkUnavailable
        +indexCorrupted
        +contentGenerationFailed
        +invalidQuery
        +queryTooShort
        +databaseError(String)
        +isRecoverable Bool
    }

    class SearchState {
        <<enumeration>>
        +inactive
        +activating(CGFloat)
        +active
        +searching
        +showingResults([SearchSuggestion])
        +error(SearchError)
    }

    %% 缓存相关数据模型
    class CacheItem~T~ {
        +T value
        +Date createdAt
        +Date? expiresAt
        +Int accessCount
        +Date lastAccessedAt
        +Int size
        +isExpired Bool
        +remainingTTL TimeInterval?
        +accessed() CacheItem~T~
    }

    class CacheConfig {
        +Int memoryLimit
        +Int diskLimit
        +TimeInterval defaultTTL
        +Double cleanupThreshold
        +Bool enableDiskCache
        +String cacheDirectoryName
        +default CacheConfig
    }

    class CacheMetrics {
        +Int memoryHits
        +Int diskHits
        +Int misses
        +Int memoryUsage
        +Int diskUsage
        +Int totalItems
        +Date timestamp
        +hitRate Double
        +memoryUsageRate Double
        +diskUsageRate Double
        +empty CacheMetrics
    }

    %% 现有API模型（复用）
    class WordDefinitionResponse {
        +String word
        +WordMetadata metadata
        +WordContent content
    }

    class WordIndexItem {
        +Int syncId
        +String word
        +LanguageCode language
        +[PhoneticSymbol] phoneticSymbols
        +String coreDefinition
    }

    class LanguageCode {
        <<enumeration>>
        +english
        +chinese
        +japanese
        +...
    }

    %% 关系定义
    SearchSuggestion --> LanguageCode : uses
    CacheStatus --> WordDefinitionResponse : contains
    CacheStatus --> WordIndexItem : contains
    SearchState --> SearchSuggestion : contains
    SearchState --> SearchError : contains
    CacheItem --> CacheConfig : configured by
    CacheMetrics --> CacheConfig : measures

    %% 样式定义
    classDef searchModel fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef cacheModel fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    classDef existingModel fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000

    class SearchSuggestion,SearchConfig,SearchError,SearchState searchModel
    class CacheItem,CacheConfig,CacheMetrics cacheModel
    class WordDefinitionResponse,WordIndexItem,LanguageCode existingModel
```

### LPLC原则实现流程图

```mermaid
sequenceDiagram
    participant User as 👤 用户
    participant UI as 🖥️ SearchView
    participant VM as 🎯 SearchViewModel
    participant Service as 💼 SearchService
    participant LocalIndex as 📊 LocalIndexService
    participant Cache as 🗄️ CacheService
    participant WordAPI as 🔌 WordAPIAdapter
    participant Backend as 🌐 后端API

    %% 搜索建议阶段（本地优先）
    User->>UI: 输入 "prog"
    UI->>VM: handleTextInput("prog")
    VM->>Service: getSuggestions("prog", 10)
    Service->>LocalIndex: searchSuggestions("prog", 10)
    LocalIndex-->>Service: [SearchSuggestion] (本地索引)
    Service-->>VM: [SearchSuggestion]
    VM-->>UI: 更新建议列表
    UI-->>User: 显示搜索建议

    %% 用户选择阶段（LPLC触发点）
    User->>UI: 选择 "progressive"
    UI->>VM: selectSearchResult("progressive")
    VM->>Service: searchWord("progressive", .chinese)

    %% 缓存检查阶段
    Service->>Cache: checkCache("progressive", .chinese)
    Cache-->>Service: CacheStatus.none

    %% LPLC原则：只有在此时才生成内容
    Note over Service,Backend: 🎯 LPLC触发点：用户明确需要时才生成
    Service->>WordAPI: getWord("progressive", .chinese)
    WordAPI->>Backend: HTTP GET /api/v1/word/progressive?lang=zh
    Backend-->>WordAPI: WordDefinitionResponse (AI生成)
    WordAPI-->>Service: WordDefinitionResponse

    %% 缓存更新
    Service->>Cache: set("progressive_zh", content, 3600s)
    Cache-->>Service: 缓存成功

    %% 返回结果
    Service-->>VM: WordDefinitionResponse
    VM-->>UI: 显示完整内容
    UI-->>User: 展示AI生成的深度解析
```

## 用户搜索完整时序图

```mermaid
sequenceDiagram
    participant U as 👤 用户
    participant UI as 🔍 搜索界面
    participant VM as 🎯 ViewModel
    participant Biz as 💼 业务服务
    participant Local as 💾 本地索引
    participant WAdapter as 🔌 单词适配器
    participant API as 🌐 后端API
    participant DB as 🗄️ 数据库

    %% 1. 用户输入搜索
    U->>UI: 输入 progressive
    UI->>VM: searchWord(progressive)
    VM->>Biz: getWordContent(progressive, chinese)

    %% 2. 检查完整缓存
    Biz->>Local: checkFullCache(progressive, zh)
    Local-->>Biz: 缓存检查结果

    %% 3. 正确的降级策略分支
    alt 有完整缓存
        Local-->>Biz: 完整WordDefinition
        Biz-->>VM: ✨ 完整AI生成内容
    else 无完整缓存
        %% 4. 发起网络请求（优先获取完整内容）
        Biz->>WAdapter: getWord(progressive, chinese)
        WAdapter->>API: GET /api/v1/word/progressive?lang=zh

        alt 网络请求成功
            API->>DB: 查询单词数据
            DB-->>API: 返回完整JSON内容
            API-->>WAdapter: WordDefinitionResponse
            WAdapter-->>Biz: 解析后的完整内容

            %% 5. 更新本地缓存
            Biz->>Local: updateCache(wordData)
            Local-->>Biz: 缓存更新完成

            Biz-->>VM: ✨ 完整AI生成内容
        else 网络请求失败
            %% 6. 降级到本地索引
            Biz->>Local: checkIndexData(progressive, zh)
            Local-->>Biz: 索引数据检查结果

            alt 有索引数据
                Local-->>Biz: WordIndexItem包含音标和释义
                Biz-->>VM: 📝 基础信息(音标+释义+网络异常提示)
            else 无索引数据
                Biz-->>VM: ❌ 网络连接错误提示
            end
        end
    end

    %% 6. 界面更新
    VM-->>UI: 更新搜索结果
    UI-->>U: 显示单词内容

    %% 样式
    Note over U,DB: 三级降级策略确保离线可用性
```

## 数据结构转换流程图

```mermaid
graph LR
    %% 后端数据库原始数据
    DBData[🗄️ 数据库记录<br/>contentJson包含<br/>word, phoneticSymbols<br/>coreDefinition等字段]

    %% 后端API响应
    APIResponse[🌐 API响应<br/>WordDefinitionResponse<br/>包含完整AI生成内容]

    %% 索引同步数据
    IndexData[📊 索引同步数据<br/>WordIndexItem<br/>包含音标和核心释义]

    %% 前端适配器处理
    AdapterData[🔌 适配器数据<br/>Swift数据结构<br/>类型安全转换]

    %% 本地缓存存储
    CacheData[💾 本地缓存<br/>SQLite存储<br/>完整内容和索引信息]

    %% 业务层数据
    BusinessData[💼 业务层数据<br/>三级降级策略<br/>智能数据选择]

    %% UI显示数据
    UIData[🔍 UI显示<br/>用户界面<br/>progressive单词展示]

    %% 数据流转换
    DBData --> APIResponse
    DBData --> IndexData
    APIResponse --> AdapterData
    IndexData --> CacheData
    AdapterData --> CacheData
    CacheData --> BusinessData
    BusinessData --> UIData

    %% 样式定义
    classDef backend fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef adapter fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    classDef cache fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef business fill:#FFE4B5,stroke:#000000,stroke-width:2px,color:#000000
    classDef ui fill:#FFE4E1,stroke:#000000,stroke-width:3px,color:#000000

    class DBData,APIResponse,IndexData backend
    class AdapterData adapter
    class CacheData cache
    class BusinessData business
    class UIData ui
```

## 三级降级策略决策流程

```mermaid
flowchart TD
    Start[🔍 用户搜索 progressive] --> CheckFullCache{💾 检查完整缓存}

    %% 优先级1：有完整缓存，直接返回
    CheckFullCache -->|找到完整缓存| FullCache[✨ 优先级1: 返回完整AI内容<br/>包含音标释义例句等]
    FullCache --> ShowFull[📱 显示完整内容<br/>英式美式音标<br/>详细解释例句]

    %% 没有完整缓存，发起网络请求
    CheckFullCache -->|无完整缓存| NetworkRequest[🌐 发起网络请求<br/>获取progressive单词数据]

    %% 网络请求成功
    NetworkRequest -->|网络请求成功| NetworkSuccess[✅ 获取完整AI内容]
    NetworkSuccess --> UpdateCache[💾 更新本地缓存]
    UpdateCache --> ShowFull

    %% 网络请求失败，降级到索引数据
    NetworkRequest -->|网络请求失败| CheckIndex{📊 检查本地索引}

    %% 优先级2：有索引数据，返回基础信息
    CheckIndex -->|找到索引数据| IndexFallback[📝 优先级2: 索引降级<br/>WordIndexItem数据<br/>包含双音标和核心释义]
    IndexFallback --> ShowBasic[📱 显示基础信息<br/>英式美式音标<br/>核心释义<br/>网络异常提示]

    %% 优先级3：无任何数据
    CheckIndex -->|无索引数据| NoData[❌ 优先级3: 无任何数据]
    NoData --> ShowError[📱 显示错误提示<br/>需要网络连接<br/>建议检查网络]

    %% 样式定义
    classDef startNode fill:#E6F3FF,stroke:#000000,stroke-width:3px,color:#000000
    classDef decision fill:#FFE4B5,stroke:#000000,stroke-width:2px,color:#000000
    classDef priority1 fill:#90EE90,stroke:#000000,stroke-width:2px,color:#000000
    classDef network fill:#87CEEB,stroke:#000000,stroke-width:2px,color:#000000
    classDef priority2 fill:#FFD700,stroke:#000000,stroke-width:2px,color:#000000
    classDef priority3 fill:#FFA07A,stroke:#000000,stroke-width:2px,color:#000000
    classDef result fill:#F0F8FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef error fill:#FFB6C1,stroke:#000000,stroke-width:2px,color:#000000

    class Start startNode
    class CheckFullCache,CheckIndex decision
    class FullCache,ShowFull priority1
    class NetworkRequest,NetworkSuccess,UpdateCache network
    class IndexFallback,ShowBasic priority2
    class NoData priority3
    class ShowError error

    %% 添加说明
    Note1[💡 关键逻辑<br/>只有网络请求失败时<br/>才降级到本地索引]
    Note1 -.-> CheckIndex
```

## 关键数据结构定义

### 音标数据结构升级

```swift
// 旧版本（单一音标）
struct OldWordIndexItem: Codable {
    let syncId: Int
    let word: String
    let language: LanguageCode
    let phonetic: String  // 单一音标
    let coreDefinition: String
}

// 新版本（完整音标数组）
struct WordIndexItem: Codable {
    let syncId: Int
    let word: String
    let language: LanguageCode
    let phoneticSymbols: [PhoneticSymbol]  // 完整的BrE + NAmE音标
    let coreDefinition: String
}

struct PhoneticSymbol: Codable {
    let type: String  // "BrE" 或 "NAmE"
    let symbol: String
}
```

### 三级降级策略数据流

```swift
// 优先级1：完整AI内容
struct WordDefinitionResponse: Codable {
    let word: String
    let metadata: WordMetadata
    let content: WordContent  // 包含完整的AI生成内容
}

// 优先级2：基础信息
struct WordIndexItem: Codable {
    let syncId: Int
    let word: String
    let language: LanguageCode
    let phoneticSymbols: [PhoneticSymbol]  // 双音标
    let coreDefinition: String             // 核心释义
}

// 优先级3：错误提示
struct SearchError: Error {
    let code: String
    let message: String
    let suggestion: String
}
```

## 核心设计理念

### 🎯 三级降级策略

1. **🥇 优先级1：完整缓存优先**
   - 检查本地是否有完整的AI生成内容
   - 如果有 → 直接返回，提供最佳体验

2. **🌐 网络请求：主动获取完整内容**
   - 如果没有完整缓存 → 立即发起网络请求
   - 目标：获取完整的AI生成内容
   - 成功 → 更新缓存并显示完整内容

3. **🥈 优先级2：网络失败时的索引降级**
   - 只有当网络请求失败时，才检查本地索引
   - 如果有索引数据 → 显示基础信息（音标+核心释义）
   - 提示用户："网络异常，显示基础信息"

4. **🥉 优先级3：完全无数据**
   - 既没有缓存，网络又失败，也没有索引
   - 显示友好的错误提示

### 💡 关键设计原则

- **离线优先**: 即使网络断开，用户仍能获得基础信息
- **渐进增强**: 根据数据可用性提供不同层次的体验
- **性能优化**: 本地索引提供毫秒级响应
- **用户友好**: 清晰的状态提示，避免用户困惑
- **数据一致性**: 从后端到前端完整的数据结构映射

这套设计既保证了功能完整性，又确保了在各种网络环境下的可用性，真正实现了"永远可用"的用户体验目标。