# KDD-029: 语言切换功能 - 关键帧可视化

## 数据结构生命周期变化图

### 1. 用户设置数据结构演变

```mermaid
graph TD
    A[现有 UserSettings] --> B[扩展 UserSettings]
    
    A --> A1[autoPlayAudio: Bool]
    A --> A2[hapticFeedback: Bool]
    A --> A3[dailyNotification: Bool]
    A --> A4[phoneticPreference: PhoneticPreference]
    A --> A5[subscriptionStatus: SubscriptionStatus]
    A --> A6[userInfo: SenseWordUserInfo?]
    
    B --> B1[autoPlayAudio: Bool]
    B --> B2[hapticFeedback: Bool]
    B --> B3[dailyNotification: Bool]
    B --> B4[phoneticPreference: PhoneticPreference]
    B --> B5[subscriptionStatus: SubscriptionStatus]
    B --> B6[userInfo: SenseWordUserInfo?]
    B --> B7[preferredLanguage: LanguageCode]
    
    style B7 fill:#e1f5fe
    style B fill:#f3e5f5
```

### 2. 语言选择流程数据变化

```mermaid
sequenceDiagram
    participant User as 用户
    participant Settings as 设置页面
    participant Selection as 语言选择界面
    participant Service as SettingsService
    participant Storage as UserDefaults
    
    User->>Settings: 点击"单词查询语言"
    Settings->>Selection: 显示语言选择界面
    Note over Selection: LanguageSelectionRequest<br/>{currentLanguage: .chinese<br/>availableLanguages: [.chinese, .english, ...]}
    
    User->>Selection: 选择新语言 (.spanish)
    Selection->>Service: 更新语言设置
    Note over Service: LanguageUpdateRequest<br/>{newLanguage: .spanish<br/>userId: nil}
    
    Service->>Storage: 持久化设置
    Service->>Settings: 发布设置变更
    Note over Settings: LanguageUpdateResponse<br/>{success: true<br/>updatedSettings: UserSettings<br/>error: nil}
    
    Settings->>User: 显示更新后的语言
```

### 3. 单词查询语言参数传递链

```mermaid
graph LR
    A[用户搜索单词] --> B[SearchService.getWordContent]
    B --> C[获取用户语言偏好]
    C --> D[SettingsService.currentSettings.preferredLanguage]
    D --> E[WordAPIAdapter.getWord]
    E --> F[API请求: /api/v1/word/{word}?lang={language}]
    
    C --> C1[LanguageCode.chinese]
    C --> C2[LanguageCode.spanish]
    C --> C3[LanguageCode.english]
    
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style F fill:#fce4ec
```

### 4. 设置界面状态变化

```mermaid
stateDiagram-v2
    [*] --> SettingsLoaded
    SettingsLoaded --> LanguageDisplayed: 显示当前语言
    LanguageDisplayed --> LanguageSelectionShown: 用户点击语言设置
    LanguageSelectionShown --> LanguageSelected: 用户选择新语言
    LanguageSelected --> SettingsUpdated: 更新设置
    SettingsUpdated --> LanguageDisplayed: 刷新显示
    
    LanguageSelectionShown --> LanguageDisplayed: 用户取消选择
    
    note right of LanguageDisplayed
        显示: preferredLanguage.displayName
        例如: "中文", "Español", "English"
    end note
    
    note right of LanguageSelected
        LanguageSelectionResponse
        {selectedLanguage: .spanish
         success: true
         error: nil}
    end note
```

### 5. 数据模型关系图

```mermaid
classDiagram
    class LanguageCode {
        <<enumeration>>
        +String rawValue
        +String displayName
        +allCases: [LanguageCode]
    }
    
    class UserSettings {
        +Bool autoPlayAudio
        +Bool hapticFeedback
        +Bool dailyNotification
        +PhoneticPreference phoneticPreference
        +SubscriptionStatus subscriptionStatus
        +SenseWordUserInfo? userInfo
        +LanguageCode preferredLanguage
    }
    
    class LanguageSelectionRequest {
        +LanguageCode currentLanguage
        +[LanguageCode] availableLanguages
    }
    
    class LanguageSelectionResponse {
        +LanguageCode selectedLanguage
        +Bool success
        +String? error
    }
    
    class LanguageUpdateRequest {
        +LanguageCode newLanguage
        +String? userId
    }
    
    class LanguageUpdateResponse {
        +Bool success
        +UserSettings updatedSettings
        +String? error
    }
    
    UserSettings --> LanguageCode : preferredLanguage
    LanguageSelectionRequest --> LanguageCode : currentLanguage
    LanguageSelectionRequest --> LanguageCode : availableLanguages
    LanguageSelectionResponse --> LanguageCode : selectedLanguage
    LanguageUpdateRequest --> LanguageCode : newLanguage
    LanguageUpdateResponse --> UserSettings : updatedSettings
```

### 6. 组件交互架构图

```mermaid
graph TB
    subgraph "UI Layer"
        A[SettingsView]
        B[LanguageSelectionView]
        C[LanguageSelectionRow]
    end
    
    subgraph "ViewModel Layer"
        D[SettingsViewModel]
        E[LanguageSelectionViewModel]
    end
    
    subgraph "Service Layer"
        F[SettingsService]
        G[SearchService]
    end
    
    subgraph "Adapter Layer"
        H[WordAPIAdapter]
    end
    
    subgraph "Model Layer"
        I[UserSettings]
        J[LanguageCode]
    end
    
    A --> D
    B --> E
    C --> E
    D --> F
    E --> F
    G --> F
    G --> H
    F --> I
    I --> J
    
    A -.-> B : 显示语言选择
    B -.-> A : 返回选择结果
    
    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style F fill:#fff3e0
    style I fill:#fce4ec
```

### 7. 错误处理分支图

```mermaid
graph TD
    A[语言选择操作] --> B{验证语言代码}
    B -->|有效| C[更新用户设置]
    B -->|无效| D[返回错误: INVALID_LANGUAGE]
    
    C --> E{设置保存成功?}
    E -->|成功| F[发布设置变更]
    E -->|失败| G[返回错误: SAVE_FAILED]
    
    F --> H[更新界面显示]
    
    D --> I[显示错误提示]
    G --> I
    
    H --> J[操作完成]
    I --> K[操作失败]
    
    style D fill:#ffebee
    style G fill:#ffebee
    style I fill:#ffebee
    style K fill:#ffebee
    style F fill:#e8f5e8
    style J fill:#e8f5e8
```

## 关键数据转换点

### 转换点 1: 用户选择 → 设置更新
- **输入**: 用户在界面选择新语言
- **处理**: 验证语言代码，更新 UserSettings
- **输出**: 更新后的设置和成功/失败状态

### 转换点 2: 设置变更 → API参数
- **输入**: 用户设置中的 preferredLanguage
- **处理**: 从设置服务获取当前语言偏好
- **输出**: API调用中的 language 参数

### 转换点 3: 语言代码 → 显示文本
- **输入**: LanguageCode 枚举值
- **处理**: 使用 displayName 属性转换
- **输出**: 用户友好的语言显示名称

### 转换点 4: 设置持久化 → 应用重启恢复
- **输入**: UserSettings 对象
- **处理**: JSON编码存储到 UserDefaults
- **输出**: 应用重启后恢复的设置状态
