# KDD-031-Gemini-JSON优化 - 函数契约补间链

## 业务背景
优化Gemini处理CSV文件的脚本，解决大量单词CSV文件导致模型陷入回声循环的问题。通过添加递增编号和改用JSON格式来提高处理稳定性。

## 核心目标
1. 将批次大小从1000调整为500，减少单次处理负载
2. 为每个单词添加唯一递增编号作为锚点
3. 输出JSON格式而非CSV格式，避免格式混乱
4. 保持原有的词汇处理逻辑和字段结构

## 关键帧序列设计

### 关键帧 A：原始词汇列表
```typescript
interface OriginalWordList {
  words: string[];         // 原始单词列表
  total_count: number;     // 总单词数
  source_file: string;     // 源文件路径
}
```

### 关键帧 B：编号化词汇批次
```typescript
interface NumberedWordBatch {
  batch_number: number;    // 批次编号
  batch_size: number;      // 批次大小 (500)
  words: Array<{
    id: number;           // 唯一递增编号
    word: string;         // 单词内容
  }>;
  output_file: string;     // 输出文件路径
}
```

### 关键帧 C：Gemini处理结果
```typescript
interface GeminiProcessedResult {
  batch_number: number;
  processed_words: Array<{
    id: number;           // 对应的单词编号
    word: string;         // 原始单词
    keep: boolean;        // 是否保留
    topics: string[];     // 主题标签
    priority: number;     // 优先级 (1-5)
  }>;
  processing_status: "success" | "error";
  error_message?: string;
}
```

## 函数契约清单

### [FC-01] 创建编号化JSON批次生成器
**输入:** `OriginalWordList`  
**输出:** `NumberedWordBatch`

**核心逻辑:**
- 将批次大小设置为500
- 为每个单词生成唯一递增编号
- 输出JSON格式文件而非CSV

**函数签名:**
```python
def create_numbered_json_batch(
    words: List[str], 
    batch_num: int, 
    output_dir: str,
    batch_size: int = 500
) -> str
```

### [FC-02] 修改VocabularyBatchSplitter类
**输入:** `VocabularyBatchSplitter`配置  
**输出:** 增强的`VocabularyBatchSplitter`

**核心逻辑:**
- 添加JSON输出支持
- 调整默认批次大小为500
- 保持向后兼容性

**函数签名:**
```python
def create_batch_json(
    self, 
    words: List[str], 
    batch_num: int, 
    output_dir: str
) -> str
```

### [FC-03] 更新主脚本适配JSON工作流
**输入:** 配置参数  
**输出:** JSON批次文件

**核心逻辑:**
- 修改create_first_batch.py使用JSON格式
- 确保文件路径和命名规范正确
- 提供清晰的处理日志

### [FC-04] 创建JSON结果处理工具
**输入:** `GeminiProcessedResult`  
**输出:** 处理后的数据文件

**核心逻辑:**
- 验证Gemini返回的JSON格式
- 处理可能的数据不一致问题
- 生成最终的处理报告

**函数签名:**
```python
def process_gemini_json_result(
    result_file: str,
    output_dir: str
) -> ProcessingReport
```

## 预期的工作流程

1. **批次生成阶段:**
   - 使用优化后的splitter创建500个单词的JSON批次
   - 每个单词都有唯一ID (1, 2, 3, ...)
   - 输出格式: `batch_001_words.json`

2. **Gemini处理阶段:**
   - 向Gemini发送编号化的JSON数据
   - 模型基于ID返回处理结果
   - 避免因大量重复文本导致的回声循环

3. **结果处理阶段:**
   - 验证返回的JSON格式
   - 根据ID匹配原始单词
   - 生成最终的处理报告

## 提交计划

### Commit 1: 修改VocabularyBatchSplitter支持JSON格式
- 文件: `batch_splitter.py`
- 内容: 添加JSON输出方法，调整默认批次大小

### Commit 2: 更新create_first_batch.py适配JSON工作流
- 文件: `create_first_batch.py`
- 内容: 使用新的JSON生成方法

### Commit 3: 创建JSON结果处理工具
- 文件: `json_result_processor.py`
- 内容: 新增结果验证和处理功能

### Commit 4: 添加使用说明和示例
- 文件: `README.md`, `使用说明.md`
- 内容: 更新文档说明新的JSON工作流程

## 测试策略

1. **单元测试:** 验证JSON生成的正确性
2. **集成测试:** 确保与现有代码兼容
3. **格式验证:** 检查生成的JSON结构
4. **性能测试:** 对比CSV和JSON处理效率

## 风险评估

1. **向后兼容性:** 保持对现有CSV工作流的支持
2. **文件大小:** JSON格式可能比CSV稍大
3. **处理逻辑:** 确保Gemini能正确处理新的JSON格式

## 成功标准

1. 成功生成500个单词的JSON批次文件
2. 每个单词都有唯一的递增编号
3. 与原有功能保持完全兼容
4. 提供清晰的使用文档和示例