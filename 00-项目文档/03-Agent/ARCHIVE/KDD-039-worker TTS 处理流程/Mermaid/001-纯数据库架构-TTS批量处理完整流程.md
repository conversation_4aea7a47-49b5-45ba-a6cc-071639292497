# 纯数据库架构 - TTS批量处理完整流程

## 📋 架构概述

基于纯数据库的TTS批量处理架构，取消消息队列，使用数据库作为唯一数据源，通过定时任务驱动整个处理流程。

### 🎯 设计原则
- **单一数据源**: 所有状态都存储在数据库中
- **简化架构**: 取消队列复杂性，只依赖数据库+定时任务
- **状态驱动**: 通过数据库状态字段驱动流程转换
- **批量优化**: 利用Azure批处理API提升效率

---

## 🗄️ 数据库设计

### 表1: tts_tasks (TTS任务表)
```sql
CREATE TABLE tts_tasks (
  ttsId TEXT PRIMARY KEY,           -- 24位哈希ID: "a1b2c3d4e5f6789012345678"
  text TEXT NOT NULL,               -- 文本内容: "hello" 或 "ˈheləʊ"
  type TEXT NOT NULL,               -- 类型: "phonetic_name", "phonetic_bre", "example_sentence"
  status TEXT DEFAULT 'pending',   -- 状态: pending → batched → processing → completed/failed
  audioUrl TEXT,                    -- 音频URL: "https://audio.senseword.app/a1b2c3d4e5f6789012345678.mp3"
  batchId TEXT,                     -- 批处理ID: "batch_20240115_001"
  errorMessage TEXT,                -- 错误信息
  createdAt TEXT,                   -- 创建时间: "2024-01-15T10:00:00.000Z"
  updatedAt TEXT,                   -- 更新时间
  completedAt TEXT                  -- 完成时间
);
```

### 表2: azure_batch_jobs (Azure批处理表)
```sql
CREATE TABLE azure_batch_jobs (
  batchId TEXT PRIMARY KEY,         -- 批处理ID: "batch_20240115_001"
  taskCount INTEGER NOT NULL,       -- 任务数量: 50
  type TEXT NOT NULL,               -- TTS类型: "phonetic_name"
  status TEXT DEFAULT 'submitted', -- 状态: submitted → running → succeeded/failed
  submittedAt TEXT NOT NULL,        -- 提交时间: "2024-01-15T10:01:00.000Z"
  completedAt TEXT,                 -- 完成时间
  downloadUrl TEXT,                 -- 下载链接: "https://azure.blob.core.windows.net/..."
  errorMessage TEXT,                -- 错误信息
  createdAt TEXT,                   -- 创建时间
  updatedAt TEXT                    -- 更新时间
);
```

---

## 🔄 完整业务流程图

```mermaid
flowchart TD
    A[📱 本地Python脚本] -->|HTTP POST| B[🌐 TTS Queue Worker]
    B -->|写入数据库| C[(🗄️ tts_tasks表)]
    
    C -->|定时任务1| D[⏰ 批量提交定时器]
    D -->|查询pending任务| C
    D -->|创建批处理| E[☁️ Azure Batch API]
    E -->|返回batchId| F[(🗄️ azure_batch_jobs表)]
    F -->|更新batchId| C
    
    F -->|定时任务2| G[⏰ 状态轮询定时器]
    G -->|查询进行中批处理| F
    G -->|轮询状态| E
    E -->|返回状态| G
    G -->|更新状态| F
    
    F -->|批处理完成| H[⏰ 结果下载定时器]
    H -->|获取下载链接| E
    E -->|返回音频文件| H
    H -->|上传音频| I[📦 R2存储]
    I -->|返回URL| H
    H -->|更新audioUrl| C
    
    classDef scriptStyle fill:#E8F5E8,stroke:#000000,stroke-width:2px,color:#000000
    classDef workerStyle fill:#E8F0FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef dbStyle fill:#FFF0E8,stroke:#000000,stroke-width:3px,color:#000000
    classDef azureStyle fill:#F0F8FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef timerStyle fill:#F8F0FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef storageStyle fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    
    class A scriptStyle
    class B workerStyle
    class C,F dbStyle
    class E azureStyle
    class D,G,H timerStyle
    class I storageStyle
```

---

## ⏱️ 时序图 - 完整处理流程

```mermaid
sequenceDiagram
    participant 📱 本地脚本
    participant 🌐 HTTP端点
    participant 🗄️ 数据库
    participant ⏰ 定时任务
    participant ☁️ Azure API
    participant 📦 R2存储
    
    Note over 📱,🗄️: 阶段1: 任务提交
    📱->>🌐: POST /submit (50个TTS任务)
    🌐->>🗄️: INSERT INTO tts_tasks (status='pending')
    🌐-->>📱: 返回提交成功
    
    Note over ⏰,☁️: 阶段2: 批量提交 (每30秒)
    ⏰->>🗄️: SELECT * FROM tts_tasks WHERE status='pending' LIMIT 50
    🗄️-->>⏰: 返回50个pending任务
    ⏰->>☁️: POST /batchsynthesis (批量提交)
    ☁️-->>⏰: 返回batchId: "batch_001"
    ⏰->>🗄️: INSERT INTO azure_batch_jobs (batchId, status='submitted')
    ⏰->>🗄️: UPDATE tts_tasks SET status='batched', batchId='batch_001'
    
    Note over ⏰,☁️: 阶段3: 状态轮询 (每60秒)
    ⏰->>🗄️: SELECT * FROM azure_batch_jobs WHERE status IN ('submitted','running')
    🗄️-->>⏰: 返回进行中的批处理
    ⏰->>☁️: GET /batchsynthesis/batch_001 (查询状态)
    ☁️-->>⏰: 返回status: "Succeeded"
    ⏰->>🗄️: UPDATE azure_batch_jobs SET status='succeeded'
    ⏰->>🗄️: UPDATE tts_tasks SET status='processing' WHERE batchId='batch_001'
    
    Note over ⏰,📦: 阶段4: 结果下载
    ⏰->>☁️: GET /batchsynthesis/batch_001/files (获取下载链接)
    ☁️-->>⏰: 返回downloadUrl
    ⏰->>☁️: GET downloadUrl (下载音频文件)
    ☁️-->>⏰: 返回音频数据
    ⏰->>📦: PUT audio files (上传到R2)
    📦-->>⏰: 返回音频URLs
    ⏰->>🗄️: UPDATE tts_tasks SET status='completed', audioUrl='https://...'
```

---

## 📊 数据状态转换图

```mermaid
stateDiagram-v2
    [*] --> pending: 📱 本地脚本提交任务
    
    pending --> batched: ⏰ 定时任务1\n批量提交到Azure
    
    batched --> processing: ⏰ 定时任务2\nAzure批处理完成
    
    processing --> completed: ⏰ 定时任务3\n音频下载并上传R2
    processing --> failed: ❌ 下载或上传失败
    
    batched --> failed: ❌ Azure批处理失败
    
    completed --> [*]: ✅ 任务完成
    failed --> [*]: ❌ 任务失败
    
    classDef pendingStyle fill:#FFF3CD,stroke:#000000,stroke-width:2px,color:#000000
    classDef batchedStyle fill:#D1ECF1,stroke:#000000,stroke-width:2px,color:#000000
    classDef processingStyle fill:#D4EDDA,stroke:#000000,stroke-width:2px,color:#000000
    classDef completedStyle fill:#D1E7DD,stroke:#000000,stroke-width:3px,color:#000000
    classDef failedStyle fill:#F8D7DA,stroke:#000000,stroke-width:2px,color:#000000
    
    class pending pendingStyle
    class batched batchedStyle
    class processing processingStyle
    class completed completedStyle
    class failed failedStyle
```

---

## 🏗️ 系统架构图

```mermaid
graph TB
    subgraph "🖥️ 本地环境"
        A[📱 Python脚本<br/>01_submit_tts_tasks.py]
        B[🗄️ SQLite数据库<br/>senseword_content.db]
        A -->|读取待处理单词| B
    end

    subgraph "☁️ Cloudflare Workers"
        C[🌐 TTS Queue Worker<br/>HTTP端点接收任务]
        D[⏰ TTS Batch Worker<br/>定时任务处理器]

        subgraph "📋 定时任务"
            D1[⏰ 批量提交器<br/>每30秒执行]
            D2[⏰ 状态轮询器<br/>每60秒执行]
            D3[⏰ 结果下载器<br/>每60秒执行]
        end

        D --> D1
        D --> D2
        D --> D3
    end

    subgraph "🗄️ Cloudflare D1数据库"
        E[(📋 tts_tasks<br/>任务状态管理)]
        F[(📊 azure_batch_jobs<br/>批处理状态管理)]
        E -.->|batchId关联| F
    end

    subgraph "☁️ Azure服务"
        G[🎤 Azure TTS<br/>Batch Synthesis API]
        H[📁 Azure Blob<br/>临时音频存储]
        G --> H
    end

    subgraph "📦 Cloudflare R2"
        I[🎵 音频文件存储<br/>audio.senseword.app]
    end

    A -->|HTTP POST| C
    C -->|写入任务| E
    D1 -->|查询pending| E
    D1 -->|批量提交| G
    D1 -->|记录批处理| F
    D2 -->|查询进行中| F
    D2 -->|轮询状态| G
    D3 -->|下载音频| H
    D3 -->|上传音频| I
    D3 -->|更新URL| E

    classDef localStyle fill:#E8F5E8,stroke:#000000,stroke-width:2px,color:#000000
    classDef workerStyle fill:#E8F0FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef dbStyle fill:#FFF0E8,stroke:#000000,stroke-width:3px,color:#000000
    classDef azureStyle fill:#F0F8FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef storageStyle fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    classDef timerStyle fill:#F8F0FF,stroke:#000000,stroke-width:2px,color:#000000

    class A,B localStyle
    class C,D workerStyle
    class E,F dbStyle
    class G,H azureStyle
    class I storageStyle
    class D1,D2,D3 timerStyle
```

---

## 📝 真实数据演示

### 示例1: 单词 "hello" 的完整处理流程

#### 步骤1: 本地脚本提交任务
```json
// HTTP POST /submit
{
  "tasks": [
    {
      "ttsId": "a1b2c3d4e5f6789012345678",
      "text": "hello",
      "type": "phonetic_name"
    },
    {
      "ttsId": "b2c3d4e5f6789012345678a1",
      "text": "ˈheləʊ",
      "type": "phonetic_bre"
    }
  ]
}
```

#### 步骤2: 数据库初始状态
```sql
-- tts_tasks表
INSERT INTO tts_tasks VALUES (
  'a1b2c3d4e5f6789012345678',  -- ttsId
  'hello',                      -- text
  'phonetic_name',              -- type
  'pending',                    -- status
  NULL,                         -- audioUrl
  NULL,                         -- batchId
  NULL,                         -- errorMessage
  '2024-01-15T10:00:00.000Z',  -- createdAt
  '2024-01-15T10:00:00.000Z',  -- updatedAt
  NULL                          -- completedAt
);
```

#### 步骤3: 批量提交后状态
```sql
-- azure_batch_jobs表
INSERT INTO azure_batch_jobs VALUES (
  'batch_20240115_001',         -- batchId
  50,                           -- taskCount
  'phonetic_name',              -- type
  'submitted',                  -- status
  '2024-01-15T10:01:00.000Z',  -- submittedAt
  NULL,                         -- completedAt
  NULL,                         -- downloadUrl
  NULL,                         -- errorMessage
  '2024-01-15T10:01:00.000Z',  -- createdAt
  '2024-01-15T10:01:00.000Z'   -- updatedAt
);

-- tts_tasks表更新
UPDATE tts_tasks SET
  status = 'batched',
  batchId = 'batch_20240115_001',
  updatedAt = '2024-01-15T10:01:00.000Z'
WHERE ttsId = 'a1b2c3d4e5f6789012345678';
```

#### 步骤4: Azure处理完成后状态
```sql
-- azure_batch_jobs表更新
UPDATE azure_batch_jobs SET
  status = 'succeeded',
  completedAt = '2024-01-15T10:05:00.000Z',
  downloadUrl = 'https://azure.blob.core.windows.net/batch-results/batch_20240115_001.zip',
  updatedAt = '2024-01-15T10:05:00.000Z'
WHERE batchId = 'batch_20240115_001';

-- tts_tasks表更新
UPDATE tts_tasks SET
  status = 'processing',
  updatedAt = '2024-01-15T10:05:00.000Z'
WHERE batchId = 'batch_20240115_001';
```

#### 步骤5: 音频下载完成后最终状态
```sql
-- tts_tasks表最终状态
UPDATE tts_tasks SET
  status = 'completed',
  audioUrl = 'https://audio.senseword.app/a1b2c3d4e5f6789012345678.mp3',
  completedAt = '2024-01-15T10:06:00.000Z',
  updatedAt = '2024-01-15T10:06:00.000Z'
WHERE ttsId = 'a1b2c3d4e5f6789012345678';
```

---

## ⚙️ 定时任务详细说明

### 定时任务1: 批量提交器 (每30秒)
```mermaid
flowchart TD
    A[⏰ 定时触发] --> B[🔍 查询pending任务]
    B --> C{是否有任务?}
    C -->|否| D[📝 记录日志: 无待处理任务]
    C -->|是| E[📊 按type分组]
    E --> F[🎯 每组最多50个任务]
    F --> G[🏗️ 构建Azure批处理请求]
    G --> H[☁️ 提交到Azure Batch API]
    H --> I{提交成功?}
    I -->|是| J[💾 创建azure_batch_jobs记录]
    I -->|否| K[❌ 记录错误日志]
    J --> L[🔄 更新tts_tasks状态为batched]
    L --> M[✅ 完成本轮处理]
    K --> M
    D --> M

    classDef processStyle fill:#E8F0FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef decisionStyle fill:#FFF3CD,stroke:#000000,stroke-width:2px,color:#000000
    classDef actionStyle fill:#D1ECF1,stroke:#000000,stroke-width:2px,color:#000000
    classDef errorStyle fill:#F8D7DA,stroke:#000000,stroke-width:2px,color:#000000

    class A,B,E,F,G,H,J,L processStyle
    class C,I decisionStyle
    class D,M actionStyle
    class K errorStyle
```

### 定时任务2: 状态轮询器 (每60秒)
```mermaid
flowchart TD
    A[⏰ 定时触发] --> B[🔍 查询进行中的批处理]
    B --> C{是否有批处理?}
    C -->|否| D[📝 记录日志: 无进行中批处理]
    C -->|是| E[🔄 逐个查询Azure状态]
    E --> F[☁️ 调用Azure状态API]
    F --> G{批处理状态?}
    G -->|Running| H[📊 更新状态为running]
    G -->|Succeeded| I[✅ 更新状态为succeeded]
    G -->|Failed| J[❌ 更新状态为failed]
    H --> K[⏭️ 处理下一个批处理]
    I --> L[🔄 更新关联任务状态为processing]
    J --> M[❌ 更新关联任务状态为failed]
    L --> K
    M --> K
    K --> N{还有批处理?}
    N -->|是| E
    N -->|否| O[✅ 完成本轮轮询]
    D --> O

    classDef processStyle fill:#E8F0FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef decisionStyle fill:#FFF3CD,stroke:#000000,stroke-width:2px,color:#000000
    classDef successStyle fill:#D1E7DD,stroke:#000000,stroke-width:2px,color:#000000
    classDef errorStyle fill:#F8D7DA,stroke:#000000,stroke-width:2px,color:#000000

    class A,B,E,F,H,L,K processStyle
    class C,G,N decisionStyle
    class D,I,O successStyle
    class J,M errorStyle
```

### 定时任务3: 结果下载器 (每60秒)
```mermaid
flowchart TD
    A[⏰ 定时触发] --> B[🔍 查询succeeded批处理]
    B --> C{是否有完成的批处理?}
    C -->|否| D[📝 记录日志: 无完成批处理]
    C -->|是| E[📁 获取Azure下载链接]
    E --> F[⬇️ 下载音频文件]
    F --> G{下载成功?}
    G -->|否| H[❌ 记录下载失败]
    G -->|是| I[📦 解压音频文件]
    I --> J[🔄 逐个上传到R2]
    J --> K{上传成功?}
    K -->|否| L[❌ 记录上传失败]
    K -->|是| M[💾 更新tts_tasks音频URL]
    M --> N[✅ 更新任务状态为completed]
    N --> O[⏭️ 处理下一个文件]
    L --> P[❌ 更新任务状态为failed]
    P --> O
    H --> Q[❌ 更新批处理状态为failed]
    Q --> R[⏭️ 处理下一个批处理]
    O --> S{还有文件?}
    S -->|是| J
    S -->|否| R
    R --> T{还有批处理?}
    T -->|是| E
    T -->|否| U[✅ 完成本轮下载]
    D --> U

    classDef processStyle fill:#E8F0FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef decisionStyle fill:#FFF3CD,stroke:#000000,stroke-width:2px,color:#000000
    classDef successStyle fill:#D1E7DD,stroke:#000000,stroke-width:2px,color:#000000
    classDef errorStyle fill:#F8D7DA,stroke:#000000,stroke-width:2px,color:#000000

    class A,B,E,F,I,J,M,N,O,R processStyle
    class C,G,K,S,T decisionStyle
    class D,U successStyle
    class H,L,P,Q errorStyle
```

---

## 🎯 架构优势分析

### ✅ 相比队列架构的优势

| 方面 | 队列架构 | 纯数据库架构 | 优势 |
|------|----------|--------------|------|
| **数据一致性** | 队列+数据库双重状态 | 单一数据源 | 🎯 避免状态不一致 |
| **可观测性** | 队列状态难以查询 | 直接SQL查询 | 🔍 完全透明的状态 |
| **故障恢复** | 队列消息可能丢失 | 数据库持久化 | 💪 更强的可靠性 |
| **调试难度** | 需要查看队列+数据库 | 只需查看数据库 | 🛠️ 简化调试过程 |
| **成本** | 队列消息费用+存储费用 | 仅存储费用 | 💰 降低运营成本 |
| **扩展性** | 队列限制+数据库限制 | 仅数据库限制 | 📈 更好的扩展性 |

### 🔧 技术实现优势

1. **状态驱动**: 所有流程转换都基于数据库状态字段
2. **幂等性**: 定时任务可以安全重复执行
3. **可重试**: 失败的任务可以重置状态重新处理
4. **监控友好**: 可以直接查询数据库了解处理进度
5. **测试友好**: 可以直接修改数据库状态进行测试

### 📊 性能预估

- **处理能力**: 每批次50个任务，每30秒一批次 = 6000任务/小时
- **总处理时间**: 1,306,098个任务 ÷ 6000 ≈ 218小时 ≈ 9天
- **并发优化**: Azure批处理API支持更高并发，实际可能更快
- **存储成本**: 仅D1数据库存储，约$0.75/GB/月

---

## 🚀 实施建议

### 阶段1: 数据库准备
1. 创建两张核心表
2. 设置索引优化查询性能
3. 配置自动更新触发器

### 阶段2: Worker改造
1. 简化HTTP端点，只负责写入数据库
2. 实现三个定时任务处理器
3. 移除所有队列相关代码

### 阶段3: 测试验证
1. 小规模测试（10个任务）
2. 中等规模测试（1000个任务）
3. 全量处理验证

### 阶段4: 监控部署
1. 添加处理进度监控
2. 设置错误告警
3. 生产环境部署
