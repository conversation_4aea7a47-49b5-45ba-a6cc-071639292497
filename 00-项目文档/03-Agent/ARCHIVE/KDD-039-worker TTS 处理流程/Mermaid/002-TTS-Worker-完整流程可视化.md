# TTS Worker 完整流程可视化文档

## 📋 概述

本文档通过多种可视化图表详细展示TTS Worker的完整工作流程，包括任务提交、批处理、ZIP解压缩、文件映射等核心环节。

---

## 🏗️ 系统架构图

```mermaid
graph TB
    subgraph "🖥️ 本地环境"
        A[📝 Python脚本<br/>01_submit_tts_tasks.py]
        B[🗄️ 本地数据库<br/>tts_assets表]
    end
    
    subgraph "☁️ Cloudflare Workers"
        C[🌐 TTS Worker<br/>HTTP端点]
        D[⏰ 定时任务<br/>批处理器]
        E[🗃️ D1数据库<br/>tts_tasks表]
    end
    
    subgraph "🔵 Azure TTS"
        F[🎤 批处理API<br/>2024-04-01]
        G[📦 ZIP结果<br/>summary.json + WAV]
    end
    
    subgraph "💾 Cloudflare R2"
        H[🎵 音频存储<br/>audio.senseword.app]
    end
    
    A -->|批量提交任务| C
    C -->|存储任务| E
    B -->|状态同步| A
    D -->|查询pending任务| E
    D -->|创建批处理| F
    F -->|返回ZIP下载链接| D
    D -->|下载并解压| G
    D -->|上传音频文件| H
    D -->|更新任务状态| E
    
    classDef localStyle fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef workerStyle fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef azureStyle fill:#E1D5E7,stroke:#000000,stroke-width:2px,color:#000000
    classDef r2Style fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    
    class A,B localStyle
    class C,D,E workerStyle
    class F,G azureStyle
    class H r2Style
```

---

## 🔄 完整流程图

```mermaid
flowchart TD
    Start(["🚀 开始"]) --> A["📊 Python脚本查询<br/>pending状态的TTS任务"]
    A --> B["📦 按batch-size分批<br/>默认100个任务/批"]
    B --> C["🌐 HTTP POST /submit<br/>提交到Worker"]

    C --> D{"✅ 提交成功?"}
    D -->|是| E["💾 存储到D1数据库<br/>status='processing'"]
    D -->|否| F["❌ 记录错误<br/>继续下一批"]

    E --> G["⏰ 定时任务触发<br/>每1分钟"]
    G --> H["🔍 查询processing任务<br/>ORDER BY type, createdAt"]
    H --> I["📋 按类型分组<br/>groupTasksByType()"]

    I --> J["🎤 为每个类型创建<br/>Azure批处理任务"]
    J --> K["📝 PUT /batchsyntheses/id<br/>提交到Azure API"]

    K --> L{"🔄 批处理状态?"}
    L -->|Running| M["⏳ 继续轮询"]
    L -->|Succeeded| N["📥 下载ZIP文件"]
    L -->|Failed| O["❌ 标记失败"]

    N --> P["📂 JSZip解压缩"]
    P --> Q["📄 解析summary.json<br/>建立content映射"]
    Q --> R["🎵 提取WAV文件<br/>建立ttsId映射"]
    R --> S["☁️ 上传到R2存储"]
    S --> T["✅ 更新任务状态<br/>completed + audioUrl"]
    
    T --> End(["🏁 完成"])
    F --> End
    O --> End
    M --> L
    
    classDef startEnd fill:#FFE6CC,stroke:#000000,stroke-width:3px,color:#000000
    classDef process fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef decision fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef error fill:#FFCCCC,stroke:#000000,stroke-width:2px,color:#000000
    classDef success fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    
    class Start,End startEnd
    class A,B,C,E,G,H,I,J,K,N,P,Q,R,S process
    class D,L decision
    class F,O error
    class T success
```

---

## ⏱️ 时序图：完整交互流程

```mermaid
sequenceDiagram
    participant P as 📝 Python脚本
    participant W as 🌐 TTS Worker
    participant D as 🗃️ D1数据库
    participant A as 🔵 Azure TTS
    participant R as 💾 R2存储
    
    Note over P,R: 🚀 任务提交阶段
    P->>W: POST /submit (100个任务)
    W->>D: INSERT INTO tts_tasks (status='processing')
    W->>P: 返回成功响应
    
    Note over P,R: ⏰ 定时批处理阶段
    loop 每1分钟
        W->>D: SELECT * WHERE status='processing' ORDER BY type
        D->>W: 返回分组任务列表
        
        loop 每个类型分组
            W->>A: PUT /batchsyntheses/{id} (创建批处理)
            A->>W: 返回批处理ID
            W->>D: UPDATE status='batched', batchId='{id}'
        end
    end
    
    Note over P,R: 🔄 状态轮询阶段
    loop 每1分钟
        W->>A: GET /batchsyntheses/{id} (查询状态)
        A->>W: 返回状态信息
        
        alt 状态为Succeeded
            W->>A: 下载ZIP文件
            A->>W: 返回ZIP数据 (156KB)
            W->>W: JSZip解压缩
            W->>W: 解析summary.json
            W->>W: 建立content→ttsId映射
            
            loop 每个音频文件
                W->>R: PUT /{ttsId}.wav (上传音频)
                R->>W: 返回上传成功
                W->>D: UPDATE status='completed', audioUrl
            end
        end
    end
```

---

## 📊 数据结构转换流程

### 1. Python脚本 → Worker提交

```mermaid
graph LR
    subgraph "📝 Python脚本数据"
        A1["ttsId: same123456789012345678a1<br/>text: apple pronunciation<br/>type: phonetic_name<br/>status: pending"]
        A2["ttsId: same123456789012345678b2<br/>text: banana pronunciation<br/>type: phonetic_name<br/>status: pending"]
        A3["ttsId: same123456789012345678c3<br/>text: cherry pronunciation<br/>type: phonetic_name<br/>status: pending"]
    end

    subgraph "🌐 HTTP请求体"
        B["📦 POST /submit<br/>tasks: 3个任务对象<br/>包含ttsId, text, type字段"]
    end

    subgraph "🗃️ D1数据库存储"
        C1["ttsId: same123456789012345678a1<br/>text: apple pronunciation<br/>type: phonetic_name<br/>status: processing<br/>createdAt: 2025-01-15 10:01:00"]
        C2["ttsId: same123456789012345678b2<br/>text: banana pronunciation<br/>type: phonetic_name<br/>status: processing<br/>createdAt: 2025-01-15 10:01:01"]
        C3["ttsId: same123456789012345678c3<br/>text: cherry pronunciation<br/>type: phonetic_name<br/>status: processing<br/>createdAt: 2025-01-15 10:01:02"]
    end
    
    A1 --> B
    A2 --> B
    A3 --> B
    B --> C1
    B --> C2
    B --> C3
    
    classDef pythonStyle fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef httpStyle fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef dbStyle fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    
    class A1,A2,A3 pythonStyle
    class B httpStyle
    class C1,C2,C3 dbStyle
```

### 2. 批处理分组 → Azure API

```mermaid
graph TD
    subgraph "🗃️ D1数据库查询结果"
        D1["ORDER BY type, createdAt ASC<br/>LIMIT 50"]
        D2["apple pronunciation - phonetic_name<br/>banana pronunciation - phonetic_name<br/>cherry pronunciation - phonetic_name<br/>hello world - example_sentence<br/>goodbye - phonetic_bre"]
    end

    subgraph "📋 按类型分组"
        G1["🎵 phonetic_name组<br/>- apple pronunciation<br/>- banana pronunciation<br/>- cherry pronunciation"]
        G2["📝 example_sentence组<br/>- hello world"]
        G3["🇬🇧 phonetic_bre组<br/>- goodbye"]
    end

    subgraph "🔵 Azure批处理请求"
        A1["PUT /batchsyntheses/batch-001<br/>inputKind: PlainText<br/>3个inputs内容<br/>voice: en-US-AndrewNeural"]
        A2["PUT /batchsyntheses/batch-002<br/>voice: en-US-AndrewNeural"]
        A3["PUT /batchsyntheses/batch-003<br/>voice: en-GB-MiaNeural"]
    end

    D1 --> D2
    D2 --> G1
    D2 --> G2
    D2 --> G3
    G1 --> A1
    G2 --> A2
    G3 --> A3

    classDef dbStyle fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    classDef groupStyle fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef azureStyle fill:#E1D5E7,stroke:#000000,stroke-width:2px,color:#000000

    class D1,D2 dbStyle
    class G1,G2,G3 groupStyle
    class A1,A2,A3 azureStyle
```

### 3. Azure ZIP响应 → 文件映射

```mermaid
graph TB
    subgraph "📦 Azure ZIP文件内容"
        Z1["📄 summary.json<br/>包含3个results<br/>每个有contents和audioFileName"]
        Z2["🎵 0001.wav (71,444 bytes)"]
        Z3["🎵 0002.wav (75,644 bytes)"]
        Z4["🎵 0003.wav (74,444 bytes)"]
    end

    subgraph "🔄 映射建立过程"
        M1["📋 Content映射<br/>apple pronunciation → 0001.wav<br/>banana pronunciation → 0002.wav<br/>cherry pronunciation → 0003.wav"]
        M2["🆔 TtsId映射<br/>apple pronunciation → same123456789012345678a1<br/>banana pronunciation → same123456789012345678b2<br/>cherry pronunciation → same123456789012345678c3"]
    end

    subgraph "☁️ R2存储结果"
        R1["🎵 same123456789012345678a1.wav<br/>https://audio.senseword.app/same123456789012345678a1.wav"]
        R2["🎵 same123456789012345678b2.wav<br/>https://audio.senseword.app/same123456789012345678b2.wav"]
        R3["🎵 same123456789012345678c3.wav<br/>https://audio.senseword.app/same123456789012345678c3.wav"]
    end

    Z1 --> M1
    Z2 --> M1
    Z3 --> M1
    Z4 --> M1
    M1 --> M2
    M2 --> R1
    M2 --> R2
    M2 --> R3

    classDef zipStyle fill:#E1D5E7,stroke:#000000,stroke-width:2px,color:#000000
    classDef mapStyle fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef r2Style fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000

    class Z1,Z2,Z3,Z4 zipStyle
    class M1,M2 mapStyle
    class R1,R2,R3 r2Style
```

---

## 🔧 关键技术实现细节

### ZIP解压缩和映射逻辑

```mermaid
flowchart TD
    A["📥 下载ZIP文件<br/>156,641 bytes"] --> B["📂 JSZip.loadAsync()"]
    B --> C{"📄 找到summary.json?"}
    C -->|是| D["📊 JSON.parse(summaryText)"]
    C -->|否| E["❌ 返回错误"]

    D --> F["🔄 遍历results数组"]
    F --> G["📋 建立content→audioFileName映射"]
    G --> H["🎵 提取对应的WAV文件"]
    H --> I["🆔 通过content匹配ttsId"]
    I --> J["☁️ 上传到R2存储"]
    J --> K["✅ 更新数据库状态"]

    classDef processStyle fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef decisionStyle fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef errorStyle fill:#FFCCCC,stroke:#000000,stroke-width:2px,color:#000000
    classDef successStyle fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000

    class A,B,D,F,G,H,I,J processStyle
    class C decisionStyle
    class E errorStyle
    class K successStyle
```

### 状态管理流程

```mermaid
stateDiagram-v2
    [*] --> pending : Python脚本提交
    pending --> processing : Worker接收成功
    processing --> batched : 创建Azure批处理
    batched --> processing : Azure批处理中
    processing --> completed : 处理成功
    processing --> failed : 处理失败
    failed --> processing : 重试
    completed --> [*]

    note right of pending : 📝 本地tts_assets表状态
    note right of processing : 🗃️ D1数据库状态
    note right of batched : 🔵 Azure批处理状态
    note right of completed : ✅ 最终完成状态
    note right of failed : ❌ 错误状态，可重试
```

---

## 📈 性能和容量分析

### 批处理效率对比

```mermaid
graph LR
    subgraph "❌ 无批处理方案"
        A1["1000个任务"] --> A2["1000次API调用"]
        A2 --> A3["处理时间: ~50分钟"]
        A3 --> A4["成本: 高"]
    end

    subgraph "✅ 批处理方案"
        B1["1000个任务"] --> B2["按类型分组"]
        B2 --> B3["~10个批处理"]
        B3 --> B4["处理时间: ~5分钟"]
        B4 --> B5["成本: 低"]
    end

    classDef inefficientStyle fill:#FFCCCC,stroke:#000000,stroke-width:2px,color:#000000
    classDef efficientStyle fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000

    class A1,A2,A3,A4 inefficientStyle
    class B1,B2,B3,B4,B5 efficientStyle
```

---

## 🛡️ 错误处理和重试机制

```mermaid
flowchart TD
    A["🚀 任务开始"] --> B{"📡 网络请求"}
    B -->|成功| C["✅ 正常处理"]
    B -->|失败| D["❌ 记录错误"]

    D --> E{"🔄 重试次数 < 3?"}
    E -->|是| F["⏳ 等待重试"]
    E -->|否| G["❌ 标记永久失败"]

    F --> B
    C --> H["✅ 完成"]
    G --> I["📧 通知管理员"]

    classDef normalStyle fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef successStyle fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    classDef errorStyle fill:#FFCCCC,stroke:#000000,stroke-width:2px,color:#000000
    classDef retryStyle fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000

    class A,B normalStyle
    class C,H successStyle
    class D,G,I errorStyle
    class E,F retryStyle
```

---

## 📝 总结

TTS Worker通过精心设计的批处理架构，实现了：

1. **高效处理**: 按类型分组避免冲突，提高处理效率
2. **可靠映射**: 通过summary.json精确匹配音频文件和任务ID
3. **容错机制**: 多层错误处理和重试机制
4. **状态管理**: 完整的任务状态跟踪和同步
5. **资源优化**: 合理的批处理大小和频率控制

这个架构成功解决了大规模TTS任务处理的核心挑战，为SenseWord应用提供了稳定可靠的音频生成服务。
