# 005 - Azure TTS批处理速率限制与GitHub Actions优化方案

## 📋 概述

本文档详细分析Azure TTS批处理API的真实速率限制，澄清Request与Task的计算方式，并提供基于GitHub Actions的高频触发优化方案，以突破Cloudflare Workers 1分钟Cron限制。

---

## 🔍 Azure TTS批处理速率限制深度解析

### 📊 **官方限制规格**

根据Azure官方文档 (2025-03-10更新)：

| 服务类型 | Free (F0) | Standard (S0) | 说明 |
|---------|-----------|---------------|------|
| **实时TTS** | 20 transactions/60s | 200 TPS (默认) | 可调整至1000 TPS |
| **批处理TTS** | 不可用 | **100 requests/10s** | **600 requests/min** |
| **批处理负载** | N/A | **最多10,000个text inputs/request** | 单次API调用限制 |

### 🎯 **关键概念澄清：Request vs Task**

```mermaid
graph TD
    subgraph "🔑 核心概念区分"
        A[1个 API Request] --> B[包含多个 Text Inputs]
        B --> C[Input 1: apple pronunciation]
        B --> D[Input 2: banana pronunciation]
        B --> E[Input 3: cherry pronunciation]
        B --> F[... 最多10,000个inputs]
    end
    
    subgraph "📊 限制计算"
        G[100 requests/10s] --> H[≠ 100个任务/10s]
        G --> I[= 100次API调用/10s]
        I --> J[每次调用可包含10,000个任务]
        J --> K[理论峰值: 1,000,000任务/10s]
    end
    
    classDef conceptStyle fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef limitStyle fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    
    class A,B,C,D,E,F conceptStyle
    class G,H,I,J,K limitStyle
```

### 💡 **实际API调用示例**

#### 正确理解：1个Request包含多个Tasks
```json
// 这是 1个 Request，包含10个Tasks
{
  "inputKind": "SSML",
  "inputs": [
    {"content": "<speak><voice name='en-US-JennyNeural'>apple</voice></speak>"},
    {"content": "<speak><voice name='en-US-JennyNeural'>banana</voice></speak>"},
    {"content": "<speak><voice name='en-US-JennyNeural'>cherry</voice></speak>"},
    {"content": "<speak><voice name='en-US-JennyNeural'>date</voice></speak>"},
    {"content": "<speak><voice name='en-US-JennyNeural'>elderberry</voice></speak>"},
    {"content": "<speak><voice name='en-US-JennyNeural'>fig</voice></speak>"},
    {"content": "<speak><voice name='en-US-JennyNeural'>grape</voice></speak>"},
    {"content": "<speak><voice name='en-US-JennyNeural'>honeydew</voice></speak>"},
    {"content": "<speak><voice name='en-US-JennyNeural'>kiwi</voice></speak>"},
    {"content": "<speak><voice name='en-US-JennyNeural'>lemon</voice></speak>"}
  ],
  "properties": {
    "outputFormat": "riff-24khz-16bit-mono-pcm"
  }
}
```

#### 错误理解：10个Tasks = 10个Requests ❌
```typescript
// 这是错误的理解方式
for (let i = 0; i < 10; i++) {
  // 每次循环都是1个独立的Request
  await submitSingleTaskBatch(tasks[i]); // 浪费了API配额
}
```

---

## 📈 吞吐量计算与优化空间

### 🔢 **理论最大吞吐量**

```mermaid
xychart-beta
    title "Azure TTS批处理吞吐量对比"
    x-axis [当前使用, 保守优化, 激进优化, 理论峰值]
    y-axis "任务数/分钟" 0 --> 600000
    bar [50, 10000, 60000, 600000]
```

#### 计算公式：
- **理论峰值**: 600 requests/min × 10,000 inputs/request = **6,000,000 tasks/min**
- **实际合理**: 600 requests/min × 100 inputs/request = **60,000 tasks/min**
- **保守优化**: 10 requests/min × 1,000 inputs/request = **10,000 tasks/min**
- **当前使用**: 1 request/min × 50 inputs/request = **50 tasks/min**

### 📊 **当前效率分析**

| 指标 | 当前状态 | 限制上限 | 利用率 | 优化空间 |
|------|----------|----------|--------|----------|
| **API调用频率** | 1 request/min | 600 requests/min | 0.17% | **600x** |
| **批处理大小** | 50 inputs/request | 10,000 inputs/request | 0.5% | **200x** |
| **综合吞吐量** | 50 tasks/min | 6,000,000 tasks/min | 0.0008% | **120,000x** |

### 🚨 **效率问题根因**

```mermaid
flowchart TD
    A[当前TTS Worker] --> B{Cloudflare Cron限制}
    B --> C[最小间隔: 1分钟]
    C --> D[频率: 1 request/min]
    D --> E[批处理大小: 50 tasks]
    E --> F[总吞吐量: 50 tasks/min]
    F --> G[🚨 利用率: 0.0008%]
    
    H[Azure TTS能力] --> I[600 requests/min]
    I --> J[10,000 inputs/request]
    J --> K[理论峰值: 6M tasks/min]
    
    classDef problemStyle fill:#FFCCCC,stroke:#000000,stroke-width:2px,color:#000000
    classDef capacityStyle fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    
    class A,B,C,D,E,F,G problemStyle
    class H,I,J,K capacityStyle
```
