# 需求："纯数据库架构TTS批量处理" 的函数契约补间链 (V3.0 - 纯数据库版)

## 0. 依赖关系与影响分析

- **[已删除]** `cloudflare/workers/tts-queue/`: 整个文件夹已删除，HTTP端点功能合并到tts worker
- **[重构]** `cloudflare/workers/tts/src/index.ts`: 完全重构为HTTP端点+纯数据库驱动的定时任务处理器
- **[已清理]** 所有队列相关配置和代码：队列已删除，使用数据库作为唯一数据源
- **[新增]** `cloudflare/d1/tts-db/migrations/0001_pure_database_schema.sql`: 重新设计的两张核心表
- **[重构]** `wrangler.toml`: 移除所有队列配置，保留定时任务和D1数据库绑定
- **[重用]** Azure TTS Batch Synthesis API集成：保持现有的批处理API调用逻辑

## 1. 项目文件结构概览 (Project File Structure Overview)

```
project-root/
├── cloudflare/
│   ├── d1/
│   │   └── tts-db/                                # [新增] TTS专用数据库文件夹
│   │       ├── migrations/
│   │       │   └── 0001_pure_database_schema.sql # [新增] 纯数据库架构的两张核心表
│   │       └── TTS_DATABASE_INFO.md              # [新增] TTS数据库配置信息
│   └── workers/
│       └── tts/                                   # [重构] 统一TTS处理器（HTTP端点+定时任务）
│           ├── src/
│           │   └── index.ts                       # [重构] HTTP端点+纯数据库驱动的三个定时任务
│           ├── wrangler.toml                      # [重构] 移除所有队列配置，保留定时任务和D1绑定
│           └── package.json                       # [保持] 依赖配置
├── senseword-content-factory/
│   └── workflow/
│       └── 08-音频生成/
│           └── scripts/
│               ├── 01_submit_tts_tasks.py         # [修改] 调用HTTP端点而非直接投递队列
│               ├── config.json                    # [保持] 配置文件
│               └── README.md                      # [更新] 纯数据库架构说明
└── 0-KDD - 关键帧驱动开发/
    └── 02-KDD/
        └── KDD-039-worker TTS 处理流程/
            ├── 01-函数契约补间链.md               # [重写] 本文档
            ├── 04-进度日志.md                     # [更新] 记录架构重构
            └── Mermaid/
                └── 纯数据库架构-TTS批量处理完整流程.md  # [已创建] 可视化文档
```

## 2. 分支策略建议

- **建议的特性分支名称**: `feature/tts/pure-database-architecture`
- **建议的 git worktree 文件路径**: `/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/03-senseword-tts-pure-db`
- **基础分支**: `main`
- **分支创建模拟命令行**:
    ```bash
    # 概念性命令，用于记录和指导
    # git checkout main
    # git pull origin main
    # git worktree add /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/03-senseword-tts-pure-db -b feature/tts/pure-database-architecture main
    ```

## 3. Commit 规划概要 (Commit Plan Summary & Status)

- [x] feat(d1): create TTS database with pure database schema and two core tables
- [x] refactor(tts-worker): merge HTTP endpoint and implement pure database-driven scheduled tasks
- [x] refactor(wrangler): remove all queue configurations, add D1 binding and scheduled tasks
- [x] refactor(scripts): update Python script to use unified TTS worker HTTP endpoint
- [x] test(integration): verify pure database architecture with small dataset
- [x] docs(architecture): update documentation for pure database approach
- [x] perf(optimization): optimize database queries and indexing

## 4. 函数契约补间链 (Function Contract Tweening Chain)

### 数据流概览

纯数据库架构的TTS批量处理流程包含以下关键数据转换阶段：

```
本地SQLite数据 → Python脚本处理 → HTTP端点写入 → D1数据库存储 → 定时任务1批量提交 → Azure批处理API → 定时任务2状态轮询 → 定时任务3结果下载 → R2存储 → D1状态更新
```

### 核心数据库表结构

#### 表1: tts_tasks (TTS任务表)
```sql
CREATE TABLE tts_tasks (
  ttsId TEXT PRIMARY KEY,           -- 24位哈希ID
  text TEXT NOT NULL,               -- 要合成的文本内容
  type TEXT NOT NULL,               -- TTS类型：phonetic_name, phonetic_bre, example_sentence, phrase_breakdown
  status TEXT DEFAULT 'pending',   -- 任务状态：pending → batched → processing → completed/failed
  audioUrl TEXT,                    -- 音频文件URL（完成后填入）
  batchId TEXT,                     -- 所属的Azure批处理ID（batched后填入）
  errorMessage TEXT,                -- 错误信息（失败时填入）
  createdAt TEXT DEFAULT (datetime('now')),    -- 任务创建时间
  updatedAt TEXT DEFAULT (datetime('now')),    -- 任务更新时间
  completedAt TEXT                  -- 任务完成时间
);
```

#### 表2: azure_batch_jobs (Azure批处理表)
```sql
CREATE TABLE azure_batch_jobs (
  batchId TEXT PRIMARY KEY,         -- Azure批处理ID
  taskCount INTEGER NOT NULL,       -- 包含的任务数量
  type TEXT NOT NULL,               -- TTS类型（同一批次相同类型）
  status TEXT DEFAULT 'submitted', -- 批处理状态：submitted → running → succeeded/failed
  submittedAt TEXT NOT NULL,        -- 提交到Azure的时间
  completedAt TEXT,                 -- Azure处理完成时间
  downloadUrl TEXT,                 -- Azure返回的下载链接
  errorMessage TEXT,                -- 错误信息（失败时）
  createdAt TEXT DEFAULT (datetime('now')),    -- 记录创建时间
  updatedAt TEXT DEFAULT (datetime('now'))     -- 记录更新时间
);
```

---

### [FC-01]: 本地数据库查询与TTS任务提取

- **职责**: 从本地SQLite数据库查询待处理的单词，解析ttsHashList，提取详细的TTS资产信息
- **函数签名**: `get_pending_tts_tasks(limit: int) -> List[TTSTaskData]`
- **所在文件**: `senseword-content-factory/workflow/08-音频生成/scripts/01_submit_tts_tasks.py`

>>>>> 输入 (Input): 查询参数

```python
# 函数调用参数
limit: int = 1000  # 每次处理的最大单词数量
```

**中间数据结构**: 本地SQLite查询结果

```python
# 从本地数据库查询的原始记录
class WordRecord:
    word: str                    # 单词文本，如 "hello"
    language: str               # 语言代码，如 "en"
    contentJson: str            # JSON字符串，包含完整的单词内容
    ttsHashList: str            # JSON字符串，包含TTS资产哈希列表
    ttsStatus: str              # TTS状态，如 "word_audio_ready"

# 示例原始记录
{
    "word": "hello",
    "language": "en",
    "contentJson": "{\"phonetics\":[{\"text\":\"hello\",\"audio\":\"\",\"sourceUrl\":\"\",\"license\":{\"name\":\"\",\"url\":\"\"}},{\"text\":\"ˈheləʊ\",\"audio\":\"\",\"sourceUrl\":\"\",\"license\":{\"name\":\"\",\"url\":\"\"}}],...}",
    "ttsHashList": "{\"phonetic_name\":\"a1b2c3d4e5f6789012345678\",\"phonetic_bre\":\"b2c3d4e5f6789012345678a1\",\"example_sentences\":[\"c3d4e5f6789012345678a1b2\"],\"phrase_breakdown\":[\"d4e5f6789012345678a1b2c3\"]}",
    "ttsStatus": "word_audio_ready"
}
```

<<<<< 输出 (Output): List[TTSTaskData]

解析后的TTS任务数据列表，每个任务对应一个需要生成的音频资产。

```python
# 解析后的TTS任务数据
class TTSTaskData:
    ttsId: str                  # 24位哈希ID
    text: str                   # 要合成的文本内容
    type: str                   # TTS类型
    voice: str                  # 语音模型（根据type确定）

# 示例输出：单词"hello"解析出的4个TTS任务
[
    {
        "ttsId": "a1b2c3d4e5f6789012345678",
        "text": "hello",
        "type": "phonetic_name",
        "voice": "en-US-AndrewNeural"
    },
    {
        "ttsId": "b2c3d4e5f6789012345678a1",
        "text": "ˈheləʊ",
        "type": "phonetic_bre",
        "voice": "en-GB-MiaNeural"
    },
    {
        "ttsId": "c3d4e5f6789012345678a1b2",
        "text": "The word 'hello' is a common greeting.",
        "type": "example_sentence",
        "voice": "en-US-AndrewNeural"
    },
    {
        "ttsId": "d4e5f6789012345678a1b2c3",
        "text": "hel-lo",
        "type": "phrase_breakdown",
        "voice": "en-US-AndrewNeural"
    }
]
```

**核心转换逻辑**:
1. 查询本地SQLite数据库中ttsStatus为特定状态的单词记录
2. 解析每个单词的contentJson，提取音标、例句、短语分解等文本内容
3. 解析ttsHashList，获取每个TTS资产的24位哈希ID
4. 根据TTS类型确定对应的语音模型（英式/美式）
5. 组装成标准化的TTS任务数据结构

---

### [FC-02]: HTTP端点批量任务提交

- **职责**: 通过HTTP端点接收批量TTS任务，验证格式后写入D1数据库
- **函数签名**: `handle_submit_tasks(request: Request, env: Env) -> Response`
- **所在文件**: `cloudflare/workers/tts/src/index.ts`

>>>>> 输入 (Input): HTTP请求体

```typescript
// HTTP POST /submit 请求体
interface SubmitTTSRequest {
  tasks: TTSTaskMessage[];      // 批量TTS任务数组
}

interface TTSTaskMessage {
  ttsId: string;                // 24位哈希ID
  text: string;                 // 要合成的文本内容
  type: string;                 // TTS类型
  voice: string;                // 语音模型（已废弃，Worker会根据type自动选择）
}

// 示例请求体
{
  "tasks": [
    {
      "ttsId": "a1b2c3d4e5f6789012345678",
      "text": "hello",
      "type": "phonetic_name",
      "voice": "en-US-AndrewNeural"
    },
    {
      "ttsId": "b2c3d4e5f6789012345678a1",
      "text": "ˈheləʊ",
      "type": "phonetic_bre",
      "voice": "en-GB-MiaNeural"
    }
  ]
}
```

**中间数据结构**: 数据库写入操作

```sql
-- 批量插入到tts_tasks表
INSERT INTO tts_tasks (ttsId, text, type, status, createdAt, updatedAt) VALUES
('a1b2c3d4e5f6789012345678', 'hello', 'phonetic_name', 'pending', '2024-01-15T10:00:00.000Z', '2024-01-15T10:00:00.000Z'),
('b2c3d4e5f6789012345678a1', 'ˈheləʊ', 'phonetic_bre', 'pending', '2024-01-15T10:00:00.000Z', '2024-01-15T10:00:00.000Z');
```

<<<<< 输出 (Output): HTTP响应

```typescript
// 成功响应 (HTTP 200)
interface SubmitResponse {
  success: boolean;             // true
  message: string;              // "Submitted 2 tasks, 0 failed"
  submitted: number;            // 成功提交的任务数量
  failed: number;               // 失败的任务数量
  errors?: string[];            // 错误详情（如果有）
}

// 示例成功响应
{
  "success": true,
  "message": "Submitted 2 tasks, 0 failed",
  "submitted": 2,
  "failed": 0
}

// 失败响应 (HTTP 400/500)
{
  "success": false,
  "message": "Submitted 1 tasks, 1 failed",
  "submitted": 1,
  "failed": 1,
  "errors": ["Invalid task format: ttsId must be 24 characters"]
}
```

**核心转换逻辑**:
1. 解析HTTP请求体，提取TTS任务数组
2. 逐个验证任务格式（ttsId长度、type有效性、text非空）
3. 将有效任务批量写入D1数据库的tts_tasks表，状态设为'pending'
4. 统计成功和失败数量，返回详细的提交结果

---

### [FC-03]: 定时任务1 - 批量提交器

- **职责**: 定时查询pending状态的TTS任务，按类型分组后批量提交到Azure TTS批处理API
- **函数签名**: `scheduled_batch_submitter(event: ScheduledEvent, env: Env) -> void`
- **所在文件**: `cloudflare/workers/tts/src/index.ts`
- **触发频率**: 每30秒执行一次

>>>>> 输入 (Input): 数据库查询结果

```sql
-- 查询pending状态的任务，按类型分组
SELECT ttsId, text, type, createdAt
FROM tts_tasks
WHERE status = 'pending'
ORDER BY type, createdAt ASC
LIMIT 50;
```

```typescript
// 查询结果数据结构
interface PendingTask {
  ttsId: string;                // 24位哈希ID
  text: string;                 // 要合成的文本内容
  type: string;                 // TTS类型
  createdAt: string;            // 创建时间
}

// 示例查询结果（50个任务，按类型分组）
[
  {
    "ttsId": "a1b2c3d4e5f6789012345678",
    "text": "hello",
    "type": "phonetic_name",
    "createdAt": "2024-01-15T10:00:00.000Z"
  },
  {
    "ttsId": "e5f6789012345678a1b2c3d4",
    "text": "world",
    "type": "phonetic_name",
    "createdAt": "2024-01-15T10:00:01.000Z"
  },
  // ... 更多phonetic_name类型任务
  {
    "ttsId": "b2c3d4e5f6789012345678a1",
    "text": "ˈheləʊ",
    "type": "phonetic_bre",
    "createdAt": "2024-01-15T10:00:02.000Z"
  }
  // ... 更多phonetic_bre类型任务
]
```

**中间数据结构**: Azure批处理API请求

```typescript
// 发送到Azure的批处理请求
interface AzureBatchRequest {
  displayName: string;          // 批处理任务名称
  description: string;          // 描述信息
  textType: 'PlainText';        // 文本类型
  inputs: Array<{               // 输入文本数组
    text: string;
  }>;
  properties: {
    outputFormat: string;       // 输出格式
    wordBoundaryEnabled: boolean;
    sentenceBoundaryEnabled: boolean;
  };
  synthesisConfig: {
    voice: string;              // 语音模型
  };
}

// 示例：phonetic_name类型的批处理请求
{
  "displayName": "SenseWord-Batch-phonetic_name-20240115-001",
  "description": "SenseWord TTS batch processing for phonetic_name",
  "textType": "PlainText",
  "inputs": [
    { "text": "hello" },
    { "text": "world" },
    // ... 更多同类型文本
  ],
  "properties": {
    "outputFormat": "riff-24khz-16bit-mono-pcm",
    "wordBoundaryEnabled": false,
    "sentenceBoundaryEnabled": false
  },
  "synthesisConfig": {
    "voice": "en-US-AndrewNeural"  // 美式语音
  }
}
```

<<<<< 输出 (Output): 数据库状态更新

```sql
-- 1. 创建Azure批处理记录
INSERT INTO azure_batch_jobs (
  batchId, taskCount, type, status, submittedAt, createdAt, updatedAt
) VALUES (
  'batch_20240115_001',         -- Azure返回的批处理ID
  25,                           -- 包含的任务数量
  'phonetic_name',              -- TTS类型
  'submitted',                  -- 初始状态
  '2024-01-15T10:01:00.000Z',  -- 提交时间
  '2024-01-15T10:01:00.000Z',  -- 创建时间
  '2024-01-15T10:01:00.000Z'   -- 更新时间
);

-- 2. 更新相关TTS任务状态
UPDATE tts_tasks SET
  status = 'batched',
  batchId = 'batch_20240115_001',
  updatedAt = '2024-01-15T10:01:00.000Z'
WHERE ttsId IN (
  'a1b2c3d4e5f6789012345678',
  'e5f6789012345678a1b2c3d4',
  -- ... 更多任务ID
);
```

```typescript
// 函数执行结果（日志输出）
interface BatchSubmissionResult {
  processedTypes: string[];     // 处理的TTS类型
  totalTasks: number;           // 总任务数
  successfulBatches: number;    // 成功创建的批处理数
  failedBatches: number;        // 失败的批处理数
  batchIds: string[];           // 创建的批处理ID列表
}

// 示例执行结果
{
  "processedTypes": ["phonetic_name", "phonetic_bre"],
  "totalTasks": 50,
  "successfulBatches": 2,
  "failedBatches": 0,
  "batchIds": ["batch_20240115_001", "batch_20240115_002"]
}
```

**核心转换逻辑**:
1. 查询数据库中status='pending'的TTS任务，最多50个
2. 按type字段分组（phonetic_name, phonetic_bre, example_sentence, phrase_breakdown）
3. 为每个类型构建Azure批处理请求，根据类型选择对应的语音模型
4. 调用Azure TTS Batch Synthesis API提交批处理任务
5. 将Azure返回的batchId记录到azure_batch_jobs表
6. 更新相关tts_tasks的状态为'batched'，并关联batchId

---

### [FC-04]: 定时任务2 - 状态轮询器

- **职责**: 定时查询进行中的Azure批处理任务，轮询其状态并更新数据库
- **函数签名**: `scheduled_status_poller(event: ScheduledEvent, env: Env) -> void`
- **所在文件**: `cloudflare/workers/tts/src/index.ts`
- **触发频率**: 每60秒执行一次

>>>>> 输入 (Input): 数据库查询结果

```sql
-- 查询进行中的批处理任务
SELECT batchId, taskCount, type, submittedAt
FROM azure_batch_jobs
WHERE status IN ('submitted', 'running')
ORDER BY submittedAt ASC
LIMIT 10;
```

```typescript
// 查询结果数据结构
interface PendingBatch {
  batchId: string;              // Azure批处理ID
  taskCount: number;            // 包含的任务数量
  type: string;                 // TTS类型
  submittedAt: string;          // 提交时间
}

// 示例查询结果
[
  {
    "batchId": "batch_20240115_001",
    "taskCount": 25,
    "type": "phonetic_name",
    "submittedAt": "2024-01-15T10:01:00.000Z"
  },
  {
    "batchId": "batch_20240115_002",
    "taskCount": 20,
    "type": "phonetic_bre",
    "submittedAt": "2024-01-15T10:01:30.000Z"
  }
]
```

**中间数据结构**: Azure状态查询响应

```typescript
// Azure批处理状态API响应
interface AzureBatchStatus {
  id: string;                   // 批处理ID
  status: 'NotStarted' | 'Running' | 'Succeeded' | 'Failed';
  createdDateTime: string;      // 创建时间
  lastActionDateTime: string;   // 最后操作时间
  outputs?: {                   // 输出信息（成功时）
    result: string;             // 结果URL
  };
  error?: {                     // 错误信息（失败时）
    code: string;
    message: string;
  };
}

// 示例Azure响应（成功）
{
  "id": "batch_20240115_001",
  "status": "Succeeded",
  "createdDateTime": "2024-01-15T10:01:00.000Z",
  "lastActionDateTime": "2024-01-15T10:05:00.000Z",
  "outputs": {
    "result": "https://azure.blob.core.windows.net/batch-results/batch_20240115_001.zip"
  }
}

// 示例Azure响应（进行中）
{
  "id": "batch_20240115_002",
  "status": "Running",
  "createdDateTime": "2024-01-15T10:01:30.000Z",
  "lastActionDateTime": "2024-01-15T10:03:00.000Z"
}
```

<<<<< 输出 (Output): 数据库状态更新

```sql
-- 情况1: 批处理成功完成
UPDATE azure_batch_jobs SET
  status = 'succeeded',
  completedAt = '2024-01-15T10:05:00.000Z',
  downloadUrl = 'https://azure.blob.core.windows.net/batch-results/batch_20240115_001.zip',
  updatedAt = '2024-01-15T10:05:00.000Z'
WHERE batchId = 'batch_20240115_001';

-- 同时更新相关TTS任务状态
UPDATE tts_tasks SET
  status = 'processing',
  updatedAt = '2024-01-15T10:05:00.000Z'
WHERE batchId = 'batch_20240115_001';

-- 情况2: 批处理仍在进行中
UPDATE azure_batch_jobs SET
  status = 'running',
  updatedAt = '2024-01-15T10:03:00.000Z'
WHERE batchId = 'batch_20240115_002';

-- 情况3: 批处理失败
UPDATE azure_batch_jobs SET
  status = 'failed',
  completedAt = '2024-01-15T10:04:00.000Z',
  errorMessage = 'Azure TTS service error: Invalid voice configuration',
  updatedAt = '2024-01-15T10:04:00.000Z'
WHERE batchId = 'batch_20240115_003';

-- 同时更新相关TTS任务状态为失败
UPDATE tts_tasks SET
  status = 'failed',
  errorMessage = 'Azure批处理失败',
  updatedAt = '2024-01-15T10:04:00.000Z'
WHERE batchId = 'batch_20240115_003';
```

```typescript
// 函数执行结果（日志输出）
interface StatusPollingResult {
  polledBatches: number;        // 轮询的批处理数量
  succeededBatches: number;     // 成功完成的批处理数
  runningBatches: number;       // 仍在进行的批处理数
  failedBatches: number;        // 失败的批处理数
  readyForDownload: string[];   // 准备下载的批处理ID列表
}

// 示例执行结果
{
  "polledBatches": 3,
  "succeededBatches": 1,
  "runningBatches": 1,
  "failedBatches": 1,
  "readyForDownload": ["batch_20240115_001"]
}
```

**核心转换逻辑**:
1. 查询数据库中status为'submitted'或'running'的批处理任务
2. 逐个调用Azure TTS状态查询API获取最新状态
3. 根据Azure返回的状态更新azure_batch_jobs表：
   - 'Succeeded' → 更新状态为'succeeded'，记录downloadUrl
   - 'Running' → 更新状态为'running'
   - 'Failed' → 更新状态为'failed'，记录错误信息
4. 同步更新相关tts_tasks的状态：
   - 批处理成功 → 任务状态改为'processing'
   - 批处理失败 → 任务状态改为'failed'

---

### [FC-05]: 定时任务3 - 结果下载器

- **职责**: 定时查询已完成的Azure批处理任务，下载音频文件并上传到R2存储
- **函数签名**: `scheduled_result_downloader(event: ScheduledEvent, env: Env) -> void`
- **所在文件**: `cloudflare/workers/tts/src/index.ts`
- **触发频率**: 每60秒执行一次

>>>>> 输入 (Input): 数据库查询结果

```sql
-- 查询已完成但未下载的批处理任务
SELECT batchId, taskCount, type, downloadUrl, submittedAt
FROM azure_batch_jobs
WHERE status = 'succeeded' AND downloadUrl IS NOT NULL
ORDER BY submittedAt ASC
LIMIT 5;
```

```typescript
// 查询结果数据结构
interface CompletedBatch {
  batchId: string;              // Azure批处理ID
  taskCount: number;            // 包含的任务数量
  type: string;                 // TTS类型
  downloadUrl: string;          // Azure提供的下载链接
  submittedAt: string;          // 提交时间
}

// 示例查询结果
[
  {
    "batchId": "batch_20240115_001",
    "taskCount": 25,
    "type": "phonetic_name",
    "downloadUrl": "https://azure.blob.core.windows.net/batch-results/batch_20240115_001.zip",
    "submittedAt": "2024-01-15T10:01:00.000Z"
  }
]
```

**中间数据结构**: 下载和解压的音频文件

```typescript
// 从Azure下载的ZIP文件内容
interface DownloadedBatch {
  batchId: string;              // 批处理ID
  audioFiles: AudioFile[];      // 解压后的音频文件数组
}

interface AudioFile {
  index: number;                // 文件索引（对应任务顺序）
  audioBuffer: ArrayBuffer;     // 音频文件二进制数据
  format: string;               // 音频格式，如 "audio/wav"
}

// 示例下载结果
{
  "batchId": "batch_20240115_001",
  "audioFiles": [
    {
      "index": 0,
      "audioBuffer": ArrayBuffer(245760),  // 音频二进制数据
      "format": "audio/wav"
    },
    {
      "index": 1,
      "audioBuffer": ArrayBuffer(198432),
      "format": "audio/wav"
    }
    // ... 更多音频文件
  ]
}
```

**关联TTS任务信息**:

```sql
-- 获取批处理中的TTS任务详情（按创建时间排序，确保索引对应）
SELECT ttsId, text, type, createdAt
FROM tts_tasks
WHERE batchId = 'batch_20240115_001' AND status = 'processing'
ORDER BY createdAt ASC;
```

```typescript
// TTS任务与音频文件的对应关系
interface TaskAudioMapping {
  ttsId: string;                // 24位哈希ID
  text: string;                 // 原始文本
  type: string;                 // TTS类型
  audioBuffer: ArrayBuffer;     // 对应的音频数据
  audioIndex: number;           // 在批处理中的索引
}

// 示例对应关系
[
  {
    "ttsId": "a1b2c3d4e5f6789012345678",
    "text": "hello",
    "type": "phonetic_name",
    "audioBuffer": ArrayBuffer(245760),
    "audioIndex": 0
  },
  {
    "ttsId": "e5f6789012345678a1b2c3d4",
    "text": "world",
    "type": "phonetic_name",
    "audioBuffer": ArrayBuffer(198432),
    "audioIndex": 1
  }
]
```

<<<<< 输出 (Output): 最终完成状态

```sql
-- 1. 更新TTS任务为完成状态（成功）
UPDATE tts_tasks SET
  status = 'completed',
  audioUrl = 'https://audio.senseword.app/a1b2c3d4e5f6789012345678.mp3',
  completedAt = '2024-01-15T10:06:00.000Z',
  updatedAt = '2024-01-15T10:06:00.000Z'
WHERE ttsId = 'a1b2c3d4e5f6789012345678';

UPDATE tts_tasks SET
  status = 'completed',
  audioUrl = 'https://audio.senseword.app/e5f6789012345678a1b2c3d4.mp3',
  completedAt = '2024-01-15T10:06:00.000Z',
  updatedAt = '2024-01-15T10:06:00.000Z'
WHERE ttsId = 'e5f6789012345678a1b2c3d4';

-- 2. 更新TTS任务为失败状态（如果上传失败）
UPDATE tts_tasks SET
  status = 'failed',
  errorMessage = 'R2上传失败: Network timeout',
  updatedAt = '2024-01-15T10:06:00.000Z'
WHERE ttsId = 'f6789012345678a1b2c3d4e5';

-- 3. 标记批处理任务为已处理（可选，用于避免重复处理）
UPDATE azure_batch_jobs SET
  status = 'processed',
  updatedAt = '2024-01-15T10:06:00.000Z'
WHERE batchId = 'batch_20240115_001';
```

```typescript
// R2存储的音频文件信息
interface R2AudioFile {
  key: string;                  // R2存储键名
  url: string;                  // 公开访问URL
  contentType: string;          // MIME类型
  size: number;                 // 文件大小（字节）
}

// 示例R2存储结果
[
  {
    "key": "a1b2c3d4e5f6789012345678.mp3",
    "url": "https://audio.senseword.app/a1b2c3d4e5f6789012345678.mp3",
    "contentType": "audio/mpeg",
    "size": 245760
  },
  {
    "key": "e5f6789012345678a1b2c3d4.mp3",
    "url": "https://audio.senseword.app/e5f6789012345678a1b2c3d4.mp3",
    "contentType": "audio/mpeg",
    "size": 198432
  }
]
```

```typescript
// 函数执行结果（日志输出）
interface DownloadResult {
  processedBatches: number;     // 处理的批处理数量
  totalTasks: number;           // 总任务数
  completedTasks: number;       // 成功完成的任务数
  failedTasks: number;          // 失败的任务数
  uploadedFiles: number;        // 成功上传的文件数
  totalSize: number;            // 总文件大小（字节）
}

// 示例执行结果
{
  "processedBatches": 1,
  "totalTasks": 25,
  "completedTasks": 24,
  "failedTasks": 1,
  "uploadedFiles": 24,
  "totalSize": 5242880
}
```

**核心转换逻辑**:
1. 查询数据库中status='succeeded'且有downloadUrl的批处理任务
2. 从Azure下载ZIP格式的批处理结果文件
3. 解压ZIP文件，提取所有音频文件
4. 查询批处理中的TTS任务详情，建立音频文件与任务的对应关系
5. 逐个将音频文件上传到R2存储，生成公开访问URL
6. 更新tts_tasks表：成功上传的任务状态改为'completed'，记录audioUrl
7. 处理失败的任务状态改为'failed'，记录错误信息
8. 可选：标记批处理任务为'processed'避免重复处理

## 5. AI Agent 需要了解的文件上下文

<context_files>
cloudflare/d1/tts-db/migrations/0001_pure_database_schema.sql
cloudflare/d1/tts-db/TTS_DATABASE_INFO.md
cloudflare/workers/tts/src/index.ts
cloudflare/workers/tts/wrangler.toml
cloudflare/workers/tts/package.json
senseword-content-factory/workflow/08-音频生成/scripts/01_submit_tts_tasks.py
senseword-content-factory/workflow/08-音频生成/scripts/config.json
senseword-content-factory/workflow/08-音频生成/scripts/README.md
0-KDD - 关键帧驱动开发/02-KDD/KDD-039-worker TTS 处理流程/Mermaid/纯数据库架构-TTS批量处理完整流程.md
</context_files>

## 6. 核心业务流程伪代码

```typescript
// ================================
// 主流程：纯数据库驱动的TTS批量处理
// ================================

// 步骤1: 本地脚本提交任务
async function submitTTSTasks(): Promise<void> {
    // [FC-01] 从本地SQLite查询待处理单词
    const words = await queryPendingWords(limit: 1000);

    // 解析每个单词的TTS资产信息
    const ttsTasks = [];
    for (const word of words) {
        const contentJson = JSON.parse(word.contentJson);
        const ttsHashList = JSON.parse(word.ttsHashList);

        // 提取各类型TTS任务
        ttsTasks.push(...extractTTSTasks(contentJson, ttsHashList));
    }

    // [FC-02] 通过HTTP端点批量提交
    const response = await fetch('https://tts-queue.senseword.app/submit', {
        method: 'POST',
        body: JSON.stringify({ tasks: ttsTasks })
    });

    console.log(`提交完成: ${response.submitted} 成功, ${response.failed} 失败`);
}

// 步骤2: 定时任务1 - 批量提交器 (每30秒)
async function scheduledBatchSubmitter(env: Env): Promise<void> {
    // [FC-03] 查询pending状态的任务
    const pendingTasks = await env.TTS_DB.prepare(`
        SELECT ttsId, text, type, createdAt
        FROM tts_tasks
        WHERE status = 'pending'
        ORDER BY type, createdAt ASC
        LIMIT 50
    `).all();

    if (pendingTasks.results.length === 0) {
        console.log('没有待处理的TTS任务');
        return;
    }

    // 按类型分组
    const groupedTasks = groupByType(pendingTasks.results);

    // 为每个类型创建Azure批处理
    for (const [type, tasks] of Object.entries(groupedTasks)) {
        const azureRequest = buildAzureBatchRequest(tasks, type);

        // 提交到Azure批处理API
        const batchId = await submitToAzure(azureRequest, env);

        if (batchId) {
            // 记录批处理任务
            await env.TTS_DB.prepare(`
                INSERT INTO azure_batch_jobs (batchId, taskCount, type, status, submittedAt)
                VALUES (?, ?, ?, 'submitted', ?)
            `).bind(batchId, tasks.length, type, new Date().toISOString()).run();

            // 更新TTS任务状态
            const taskIds = tasks.map(t => t.ttsId);
            await env.TTS_DB.prepare(`
                UPDATE tts_tasks SET status = 'batched', batchId = ?
                WHERE ttsId IN (${taskIds.map(() => '?').join(',')})
            `).bind(batchId, ...taskIds).run();

            console.log(`批处理创建成功: ${batchId} (${tasks.length} 个任务)`);
        }
    }
}

// 步骤3: 定时任务2 - 状态轮询器 (每60秒)
async function scheduledStatusPoller(env: Env): Promise<void> {
    // [FC-04] 查询进行中的批处理
    const pendingBatches = await env.TTS_DB.prepare(`
        SELECT batchId, taskCount, type, submittedAt
        FROM azure_batch_jobs
        WHERE status IN ('submitted', 'running')
        ORDER BY submittedAt ASC
        LIMIT 10
    `).all();

    for (const batch of pendingBatches.results) {
        // 查询Azure批处理状态
        const azureStatus = await queryAzureStatus(batch.batchId, env);

        if (azureStatus.status === 'Succeeded') {
            // 更新批处理状态
            await env.TTS_DB.prepare(`
                UPDATE azure_batch_jobs SET
                status = 'succeeded',
                completedAt = ?,
                downloadUrl = ?
                WHERE batchId = ?
            `).bind(
                new Date().toISOString(),
                azureStatus.outputs.result,
                batch.batchId
            ).run();

            // 更新TTS任务状态
            await env.TTS_DB.prepare(`
                UPDATE tts_tasks SET status = 'processing'
                WHERE batchId = ?
            `).bind(batch.batchId).run();

            console.log(`批处理完成: ${batch.batchId}`);

        } else if (azureStatus.status === 'Failed') {
            // 处理失败情况
            await handleBatchFailure(batch.batchId, azureStatus.error, env);
        }
    }
}

// 步骤4: 定时任务3 - 结果下载器 (每60秒)
async function scheduledResultDownloader(env: Env): Promise<void> {
    // [FC-05] 查询已完成的批处理
    const completedBatches = await env.TTS_DB.prepare(`
        SELECT batchId, taskCount, type, downloadUrl
        FROM azure_batch_jobs
        WHERE status = 'succeeded' AND downloadUrl IS NOT NULL
        ORDER BY submittedAt ASC
        LIMIT 5
    `).all();

    for (const batch of completedBatches.results) {
        try {
            // 下载Azure批处理结果
            const audioFiles = await downloadBatchResults(batch.downloadUrl);

            // 获取批处理中的TTS任务
            const batchTasks = await env.TTS_DB.prepare(`
                SELECT ttsId, text, type, createdAt
                FROM tts_tasks
                WHERE batchId = ? AND status = 'processing'
                ORDER BY createdAt ASC
            `).bind(batch.batchId).all();

            // 逐个处理音频文件
            for (let i = 0; i < audioFiles.length; i++) {
                const task = batchTasks.results[i];
                const audioBuffer = audioFiles[i];

                try {
                    // 上传到R2
                    const audioUrl = await uploadToR2(task.ttsId, audioBuffer, env);

                    // 更新任务状态
                    await env.TTS_DB.prepare(`
                        UPDATE tts_tasks SET
                        status = 'completed',
                        audioUrl = ?,
                        completedAt = ?
                        WHERE ttsId = ?
                    `).bind(audioUrl, new Date().toISOString(), task.ttsId).run();

                    console.log(`TTS任务完成: ${task.ttsId} -> ${audioUrl}`);

                } catch (error) {
                    // 记录失败
                    await env.TTS_DB.prepare(`
                        UPDATE tts_tasks SET
                        status = 'failed',
                        errorMessage = ?
                        WHERE ttsId = ?
                    `).bind(error.message, task.ttsId).run();
                }
            }

            // 标记批处理为已处理
            await env.TTS_DB.prepare(`
                UPDATE azure_batch_jobs SET status = 'processed'
                WHERE batchId = ?
            `).bind(batch.batchId).run();

        } catch (error) {
            console.error(`批处理下载失败: ${batch.batchId}`, error);
        }
    }
}

// ================================
// 辅助函数
// ================================

function groupByType(tasks: any[]): Record<string, any[]> {
    const groups: Record<string, any[]> = {};
    for (const task of tasks) {
        if (!groups[task.type]) groups[task.type] = [];
        groups[task.type].push(task);
    }
    return groups;
}

function buildAzureBatchRequest(tasks: any[], type: string): any {
    const voice = type === 'phonetic_bre' ? 'en-GB-MiaNeural' : 'en-US-AndrewNeural';

    return {
        displayName: `SenseWord-Batch-${type}-${Date.now()}`,
        description: `SenseWord TTS batch processing for ${type}`,
        textType: 'PlainText',
        inputs: tasks.map(task => ({ text: task.text })),
        properties: {
            outputFormat: 'riff-24khz-16bit-mono-pcm',
            wordBoundaryEnabled: false,
            sentenceBoundaryEnabled: false
        },
        synthesisConfig: { voice }
    };
}

async function submitToAzure(request: any, env: Env): Promise<string | null> {
    const url = `https://${env.AZURE_TTS_REGION}.customvoice.api.speech.microsoft.com/api/texttospeech/v3.0/batchsynthesis`;

    const response = await fetch(url, {
        method: 'POST',
        headers: {
            'Ocp-Apim-Subscription-Key': env.AZURE_TTS_KEY,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    });

    if (response.ok) {
        const result = await response.json();
        return result.id;
    }

    return null;
}

async function queryAzureStatus(batchId: string, env: Env): Promise<any> {
    const url = `https://${env.AZURE_TTS_REGION}.customvoice.api.speech.microsoft.com/api/texttospeech/v3.0/batchsynthesis/${batchId}`;

    const response = await fetch(url, {
        headers: {
            'Ocp-Apim-Subscription-Key': env.AZURE_TTS_KEY
        }
    });

    return await response.json();
}

async function downloadBatchResults(downloadUrl: string): Promise<ArrayBuffer[]> {
    const response = await fetch(downloadUrl);
    const zipBuffer = await response.arrayBuffer();

    // 解压ZIP文件，提取音频文件
    // 这里简化处理，实际需要ZIP解压逻辑
    return [zipBuffer]; // 简化返回
}

async function uploadToR2(ttsId: string, audioBuffer: ArrayBuffer, env: Env): Promise<string> {
    const key = `${ttsId}.mp3`;

    await env.AUDIO_BUCKET.put(key, audioBuffer, {
        httpMetadata: { contentType: 'audio/mpeg' }
    });

    return `https://audio.senseword.app/${key}`;
}
```