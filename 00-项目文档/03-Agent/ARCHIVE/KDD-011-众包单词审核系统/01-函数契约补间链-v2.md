# 需求："众包单词审核系统" 的函数契约补间链 (V2.0 - 真正的奥卡姆剃刀版)

## 🔍 奥卡姆剃刀分析

### 核心需求重新提炼
用户反馈的核心目的：**清除低质量内容，让系统重新生成更好的内容**

### V2.0 简化原则
1. **删除而非修改**: 直接删除低分记录，而非在查询时检查
2. **复用现有流程**: 用户请求不存在的记录时，自然触发现有的AI生成流程
3. **零架构变更**: 完全不修改现有的查询、生成、保存逻辑

### 评分: 10.0/10 - 完美奥卡姆剃刀
- **优势**: 极致简化，零架构变更，完美复用现有逻辑
- **实现**: 仅需一个定期清理任务，其他逻辑完全不变

---

## 0. 依赖关系与影响分析

- **[重用]** 现有单词查询API: 完全不变
- **[重用]** 现有AI生成服务: 完全不变
- **[重用]** 现有保存逻辑: 完全不变
- **[新增]** 定期清理任务: 独立的Cron Worker
- **[无影响]** 所有其他模块: 此功能完全独立

## 1. 项目文件结构概览 (Project File Structure Overview)

```
project-root
├── cloudflare/
│   └── workers/
│       ├── api/                                     # [无修改] 现有API完全不变
│       └── word-quality-cleaner/                    # [新增] 独立的清理Worker
│           ├── src/
│           │   ├── index.ts                         # [新增] 清理任务主逻辑
│           │   └── services/
│           │       └── cleaner.service.ts           # [新增] 清理服务
│           ├── wrangler.toml                        # [新增] Worker配置
│           └── package.json                         # [新增] 依赖配置
└── 测试文件
    └── tests/
        └── integration/
            └── word-quality-cleaner.test.ts         # [新增] 清理任务测试
```

## 2. 分支策略建议

- 建议的特性分支名称: `feature/word-quality/cleaner-worker`
- 建议的 git worktree 文件路径：/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/05-word-quality-cleaner
- 基础分支: `dev`
- 分支创建模拟命令行:
    ```bash
    # 概念性命令，用于记录和指导
    # git checkout dev
    # git pull origin dev
    # git worktree add /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/05-word-quality-cleaner -b feature/word-quality/cleaner-worker dev
    ```

## 3. Commit 规划概要 (Commit Plan Summary & Status)

- [ ] feat(word-quality): 创建独立的单词质量清理Worker
- [ ] feat(cron): 配置每日定时清理低分单词任务
- [ ] test(cleaner): 实现清理功能补间测试用例

## 4. 函数契约补间链 (Function Contract Tweening Chain)

### [FC-01]: 低分单词清理服务

- 职责: 查询并删除feedbackScore <= -3的单词记录
- 函数签名: `cleanLowScoreWords(db: D1Database): Promise<CleanupResult>`
- 所在文件: `cloudflare/workers/word-quality-cleaner/src/services/cleaner.service.ts`

>>>>> 输入 (Input): 数据库连接

```typescript
interface CleanupInput {
  db: D1Database;
  scoreThreshold: number;  // 默认 -3
}
```

<<<<< 输出 (Output): 清理结果统计

```typescript
interface CleanupResult {
  totalScanned: number;      // 扫描的总记录数
  deletedCount: number;      // 删除的记录数
  deletedWords: Array<{      // 被删除的单词列表
    word: string;
    language: string;
    feedbackScore: number;
  }>;
  executionTime: number;     // 执行时间（毫秒）
  timestamp: string;         // 执行时间戳
}
```

---

### [FC-02]: 定时清理任务调度器

- 职责: 每日定时触发清理任务，记录执行结果
- 函数签名: `handleScheduledCleanup(env: Env): Promise<Response>`
- 所在文件: `cloudflare/workers/word-quality-cleaner/src/index.ts`

>>>>> 输入 (Input): Cloudflare环境

```typescript
interface ScheduledEvent {
  type: 'scheduled';
  cron: string;              // "0 2 * * *" (每日凌晨2点)
  scheduledTime: number;     // Unix时间戳
}
```

<<<<< 输出 (Output): 执行响应

```typescript
interface ScheduledResponse {
  success: boolean;
  result: CleanupResult;
  message: string;
}
```

## 5. 实现细节 (Implementation Details)

### FC-01 实现逻辑

```typescript
export async function cleanLowScoreWords(
  db: D1Database,
  scoreThreshold: number = -3
): Promise<CleanupResult> {

  const startTime = Date.now();
  console.log(`[Cleaner] 开始清理低分单词，阈值: ${scoreThreshold}`);

  try {
    // 1. 查询低分单词
    const lowScoreWords = await db.prepare(`
      SELECT word, language, feedbackScore
      FROM word_definitions
      WHERE feedbackScore <= ?
    `).bind(scoreThreshold).all();

    console.log(`[Cleaner] 发现 ${lowScoreWords.results.length} 个低分单词`);

    // 2. 批量删除
    const deletePromises = lowScoreWords.results.map(record =>
      db.prepare(`
        DELETE FROM word_definitions
        WHERE word = ? AND language = ?
      `).bind(record.word, record.language).run()
    );

    await Promise.all(deletePromises);

    // 3. 统计结果
    const result: CleanupResult = {
      totalScanned: lowScoreWords.results.length,
      deletedCount: lowScoreWords.results.length,
      deletedWords: lowScoreWords.results,
      executionTime: Date.now() - startTime,
      timestamp: new Date().toISOString()
    };

    console.log(`[Cleaner] 清理完成，删除 ${result.deletedCount} 个记录`);
    return result;

  } catch (error) {
    console.error(`[Cleaner] 清理失败:`, error);
    throw error;
  }
}
```

### FC-02 实现逻辑

```typescript
export default {
  // 定时任务处理
  async scheduled(event: ScheduledEvent, env: Env): Promise<void> {
    console.log(`[Scheduled] 开始执行定时清理任务`);

    try {
      const result = await cleanLowScoreWords(env.DB);

      console.log(`[Scheduled] 清理任务完成:`, result);

      // 可选：发送通知或记录到监控系统
      if (result.deletedCount > 0) {
        console.log(`[Scheduled] 清理了 ${result.deletedCount} 个低分单词`);
      }

    } catch (error) {
      console.error(`[Scheduled] 定时清理任务失败:`, error);
    }
  },

  // 手动触发接口（用于测试）
  async fetch(request: Request, env: Env): Promise<Response> {
    if (request.method === 'POST' && new URL(request.url).pathname === '/trigger') {
      try {
        const result = await cleanLowScoreWords(env.DB);

        const response: ScheduledResponse = {
          success: true,
          result: result,
          message: `成功清理 ${result.deletedCount} 个低分单词`
        };

        return new Response(JSON.stringify(response), {
          headers: { 'Content-Type': 'application/json' }
        });

      } catch (error) {
        return new Response(JSON.stringify({
          success: false,
          error: error.message
        }), { status: 500 });
      }
    }

    return new Response('Word Quality Cleaner Worker', { status: 200 });
  }
};
```

## 6. Cron配置 (wrangler.toml)

```toml
name = "word-quality-cleaner"
main = "src/index.ts"
compatibility_date = "2024-01-01"

[triggers]
crons = ["0 2 * * *"]  # 每日凌晨2点执行

[[d1_databases]]
binding = "DB"
database_name = "senseword-db"
database_id = "your-database-id"

[vars]
SCORE_THRESHOLD = "-3"
```

## 7. 工作流程说明

### 完整流程
1. **每日凌晨2点**: Cron触发清理任务
2. **查询低分记录**: 找出feedbackScore <= -3的单词
3. **批量删除**: 从数据库中删除这些记录
4. **用户请求**: 用户查询被删除的单词时
5. **自然重新生成**: 现有API发现记录不存在，自动调用AI生成
6. **保存新内容**: 使用现有逻辑保存，初始分数为0

### 优势分析
- ✅ **零架构变更**: 现有API、AI服务、保存逻辑完全不变
- ✅ **自然触发**: 删除后的重新生成完全依赖现有流程
- ✅ **独立部署**: 清理Worker独立部署，不影响主服务
- ✅ **可控执行**: 可手动触发，便于测试和调试
- ✅ **完整日志**: 详细记录清理过程和结果

## 8. 测试策略

### 补间测试用例

```typescript
describe('单词质量清理系统', () => {

  test('清理低分单词记录', async () => {
    // 设置: 创建分数为-5的单词记录
    // 执行: 调用清理服务
    // 验证: 记录被删除，返回正确统计
  });

  test('保留正常分数单词', async () => {
    // 设置: 创建分数为2的单词记录
    // 执行: 调用清理服务
    // 验证: 记录保持不变
  });

  test('用户请求被删除单词触发重新生成', async () => {
    // 设置: 删除某个单词记录
    // 执行: 用户查询该单词
    // 验证: 触发AI生成，保存新记录
  });

  test('定时任务正常执行', async () => {
    // 设置: 模拟scheduled事件
    // 执行: 触发定时任务
    // 验证: 清理任务正常执行
  });

  test('手动触发接口', async () => {
    // 设置: 发送POST请求到/trigger
    // 执行: 调用手动触发接口
    // 验证: 返回清理结果
  });

});
```

### 变体测试场景

1. **边界值测试**: 分数 -4, -3, -2 的不同处理
2. **批量删除测试**: 大量低分单词的处理性能
3. **数据库异常**: 删除失败、连接中断等情况
4. **并发测试**: 清理任务与用户查询的并发情况

## 9. 监控与日志

### 关键指标
- `cleanup_triggered_count`: 清理任务触发次数
- `words_deleted_daily`: 每日删除的单词数量
- `cleanup_execution_time`: 清理任务执行时间
- `regeneration_after_cleanup`: 清理后的重新生成次数

### 日志格式
```
[Cleaner] 开始清理低分单词，阈值: -3
[Cleaner] 发现 12 个低分单词
[Cleaner] 清理完成，删除 12 个记录
[Scheduled] 清理了 12 个低分单词
```

## 10. 部署配置

### 环境变量
```bash
# 开发环境
SCORE_THRESHOLD=-3
DATABASE_ID=dev-database-id

# 生产环境
SCORE_THRESHOLD=-3
DATABASE_ID=prod-database-id
```

### 部署命令
```bash
# 部署到开发环境
wrangler deploy --env development

# 部署到生产环境
wrangler deploy --env production
```

---

## 📋 总结

这个V2.0版本真正体现了奥卡姆剃刀原则：

1. **极致简化**: 仅需一个独立的清理Worker
2. **零架构变更**: 现有所有逻辑完全不变
3. **自然流程**: 删除→用户请求→自动重新生成
4. **完美解耦**: 清理逻辑与主服务完全独立

**评分: 10.0/10** - 这才是真正的"如无必要，勿增实体"！

### 核心优势
- **用户无感知**: 用户体验完全不变
- **系统自愈**: 低质量内容自动被清理和重新生成
- **运维简单**: 独立Worker，独立部署，独立监控
- **成本最低**: 最小的代码变更，最大的价值实现