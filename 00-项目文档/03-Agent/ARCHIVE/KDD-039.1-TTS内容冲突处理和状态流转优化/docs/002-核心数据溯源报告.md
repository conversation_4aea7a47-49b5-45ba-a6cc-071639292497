# 核心数据溯源报告 002：TTS任务数据结构完整生命周期追踪

## 1. 溯源目标

本次溯源的目标是 **TTS任务数据结构** 在整个SenseWord TTS系统中的完整生命周期，从本地数据库的原始数据到Worker处理完成的全流程数据转换和状态流转。

## 2. 溯源路径

### 2.1 数据起源：本地数据库层 (Local Database Layer)

**单一真相来源**: `senseword-content-factory` 本地SQLite数据库

#### 核心表结构：
1. **words_for_publish表** - 单词发布状态管理
   - `word`: 单词文本 (如 "hello")
   - `ttsStatus`: 单词级别状态 ("pending" → "submitted")
   - `ttsHashList`: JSON字符串，包含该单词所有TTS任务的哈希ID
   - `contentJson`: 完整的单词内容JSON，包含音标、例句等

2. **tts_assets表** - TTS资产详细信息
   - `ttsId`: 24位哈希ID (如 "a1b2c3d4e5f6789012345678")
   - `textToSpeak`: 实际TTS文本内容
   - `ttsType`: TTS类型 ("phonetic_name", "phonetic_bre", "example_sentence", "phrase_breakdown")
   - `status`: 任务级别状态 ("pending", "failed", "completed")
   - `originalText`: 原始文本
   - `audioUrl`: 生成的音频URL (完成后填充)

### 2.2 数据提取：Python脚本层 (Python Script Layer)

**文件**: `senseword-content-factory/workflow/08-音频生成/scripts/01_submit_tts_tasks.py`

#### 数据转换过程：
```python
# 步骤1: 查询待处理单词 (优先处理包含failed任务的单词)
words_query = """
SELECT word, ttsStatus, ttsHashList
FROM words_for_publish
WHERE ttsStatus = 'pending'
   OR word IN (SELECT DISTINCT originalText FROM tts_assets WHERE status = 'failed')
ORDER BY
  CASE WHEN word IN (SELECT DISTINCT originalText FROM tts_assets WHERE status = 'failed')
  THEN 0 ELSE 1 END,  -- 优先处理包含failed TTS任务的单词
  word
LIMIT ?
"""

# 步骤2: 对每个单词查询其TTS任务详情
tasks_query = """
SELECT ttsId, textToSpeak, ttsType, status
FROM tts_assets
WHERE originalText = ?
  AND status IN ('pending', 'failed')
"""
```

#### 关键数据结构转换：
- `WordWithTasks` → `SubmitWordTTSRequest` → HTTP请求体
- 每个单词约25个TTS任务，按单词为单位批量提交

### 2.3 数据传输：HTTP API层 (HTTP API Layer)

**端点**: `POST https://tts.senseword.app/submit`

#### 请求格式：
```typescript
interface SubmitWordTTSRequest {
  word: string;                      // 单词名称
  tasks: Array<{
    ttsId: string;                   // 24位哈希ID
    text: string;                    // 文本内容
    type: TTSType;                   // TTS类型
  }>;
}
```

#### 响应格式：
```typescript
interface SubmitWordTTSResponse {
  success: boolean;                  // 是否成功入库
  word: string;                      // 处理的单词
  received: number;                  // 接收的任务数
  inserted: number;                  // 成功入库的任务数
  failed_tasks: string[];            // 失败的ttsId列表
  timestamp: string;                 // 处理时间戳
}
```

### 2.4 数据入库：Worker HTTP端点层 (Worker HTTP Endpoint Layer)

**文件**: `cloudflare/workers/tts/src/index.ts`

#### 核心处理逻辑：
```typescript
// FC-03: Worker简化HTTP端点处理器
async function handleWordTTSSubmit(request: Request, env: Env): Promise<Response> {
    // 1. 解析请求数据
    const requestData = await request.json() as SubmitWordTTSRequest;
    
    // 2. 转换任务格式
    const tasks = requestData.tasks.map(task => ({
      ttsId: task.ttsId,
      text: task.text,
      type: task.type
    }));
    
    // 3. 批量入库到D1数据库
    const insertResult = await batchInsertTasks(tasks, env);
    
    // 4. 返回入库结果
    return new Response(JSON.stringify({
        success: true,
        word: requestData.word,
        received: tasks.length,
        inserted: insertResult.inserted,
        failed_tasks: insertResult.failed,
        timestamp: new Date().toISOString()
    }));
}
```

### 2.5 数据存储：Cloudflare D1数据库层 (D1 Database Layer)

**文件**: `cloudflare/d1/tts-db/migrations/0002_remove_batch_processing.sql`

#### 核心表结构：
```sql
CREATE TABLE tts_tasks (
  ttsId TEXT PRIMARY KEY,                    -- 24位哈希ID
  text TEXT NOT NULL,                        -- 文本内容
  type TEXT NOT NULL,                        -- TTS类型
  status TEXT DEFAULT 'pending',            -- 状态: pending → processing → completed/failed
  audioUrl TEXT,                             -- 音频URL
  errorMessage TEXT,                         -- 错误信息
  createdAt TEXT DEFAULT (datetime('now')), -- 创建时间
  updatedAt TEXT DEFAULT (datetime('now')), -- 更新时间
  completedAt TEXT                          -- 完成时间
);
```

#### 数据入库过程：
```typescript
// FC-04: 批量任务入库
const insertSQL = `
  INSERT OR IGNORE INTO tts_tasks 
  (ttsId, text, type, status, createdAt, updatedAt) 
  VALUES (?, ?, ?, 'pending', datetime('now'), datetime('now'))
`;
```

### 2.6 数据处理：实时TTS处理层 (Realtime TTS Processing Layer)

**文件**: `cloudflare/workers/tts/src/services/realtime-tts.service.ts`

#### 处理流程：
1. **定时任务触发** (每分钟执行)
2. **查询待处理任务**：
   ```typescript
   const query = `
     SELECT ttsId, text, type 
     FROM tts_tasks 
     WHERE status = 'pending' 
     ORDER BY createdAt ASC 
     LIMIT 50
   `;
   ```

3. **状态更新为processing**：
   ```typescript
   await updateTaskStatus(task.ttsId, 'processing', {}, env);
   ```

4. **调用Azure TTS API**：
   ```typescript
   const audioBuffer = await callAzureRealtimeTTSWithRetry(
     task.text, 
     task.type, 
     env
   );
   ```

5. **上传音频到R2存储**：
   ```typescript
   const audioUrl = await uploadAudioToR2(task.ttsId, audioBuffer, env);
   ```

6. **更新最终状态**：
   ```typescript
   await updateTaskStatus(task.ttsId, 'completed', {
     audioUrl,
     completedAt: new Date().toISOString()
   }, env);
   ```

### 2.7 数据完成：音频资产层 (Audio Asset Layer)

**最终输出**:
- **R2存储**: `https://audio.senseword.app/{ttsId}.wav`
- **数据库记录**: `status = 'completed'`, `audioUrl` 已填充
- **音频格式**: 24khz-16bit-mono-pcm WAV格式

## 3. 单一真相来源 (Single Source of Truth)

### 3.1 数据定义的SSOT
- **本地数据库** (`senseword-content-factory`): 单词内容和TTS任务定义的权威来源
- **tts_assets表**: TTS任务详细信息的权威来源

### 3.2 状态管理的SSOT
- **Cloudflare D1数据库** (`tts_tasks表`): TTS任务处理状态的权威来源
- **单词状态与任务状态解耦**: 单词状态在本地数据库，任务状态在云端数据库

### 3.3 音频资产的SSOT
- **Cloudflare R2存储**: 生成音频文件的权威存储
- **D1数据库audioUrl字段**: 音频访问链接的权威记录

## 4. 问题与发现

### 4.1 数据一致性问题
1. **双数据库架构**: 本地SQLite和云端D1数据库可能出现状态不同步
2. **任务重复提交**: `INSERT OR IGNORE` 机制避免了重复，但可能掩盖逻辑问题
3. **失败任务处理**: failed任务的重试依赖查询优先级，缺乏主动重试机制

### 4.2 状态流转复杂性
1. **单词状态简化**: 只有 `pending → submitted` 的单向流转，过于简化
2. **任务状态独立**: 个别任务失败不影响单词状态，可能导致不完整的音频集合
3. **最终一致性依赖**: 依赖优先级查询实现最终一致性，缺乏强一致性保证

### 4.3 数据溯源困难
1. **跨系统追踪**: 数据在本地数据库、HTTP传输、云端数据库、外部API之间流转，追踪困难
2. **错误定位**: 失败任务的根因分析需要跨多个系统和日志
3. **性能监控**: 缺乏端到端的数据处理性能监控

## 5. 优化建议

### 5.1 数据一致性优化
1. **引入同步机制**: 在Python脚本中添加状态同步逻辑，确保本地和云端状态一致
2. **幂等性增强**: 在ttsId基础上增加内容哈希验证，确保相同内容不重复处理
3. **主动重试机制**: 实现基于时间窗口的失败任务主动重试，而非依赖查询优先级

### 5.2 状态管理优化
1. **单词状态细化**: 增加 `processing`, `partial_completed`, `completed` 等中间状态
2. **完整性检查**: 定期检查单词的所有TTS任务是否完成，更新单词级别状态
3. **状态回滚机制**: 当任务失败率过高时，支持单词状态回滚到pending

### 5.3 监控和可观测性优化
1. **端到端追踪**: 实现基于ttsId的完整链路追踪，从提交到完成
2. **性能指标**: 添加各阶段处理时间、成功率、错误率等关键指标
3. **告警机制**: 当失败率超过阈值或处理延迟过高时主动告警

### 5.4 架构简化建议
1. **统一数据库**: 考虑将本地状态管理迁移到云端，减少双数据库复杂性
2. **事件驱动**: 引入事件驱动架构，通过事件流实现各组件间的松耦合
3. **批处理优化**: 在保持实时处理的基础上，优化批量操作的效率和可靠性

---

## 总结

TTS任务数据结构在SenseWord系统中经历了从本地数据库定义、Python脚本提取、HTTP传输、Worker处理、D1存储、实时处理到最终音频生成的完整生命周期。虽然当前架构实现了基本的功能需求，但在数据一致性、状态管理和可观测性方面仍有优化空间。通过实施上述建议，可以构建一个更加健壮、可维护和高性能的TTS处理系统。
