# 06 - realtime-tts.service.ts 函数签名级可视化文档

## 📋 文档概述

本文档通过**函数签名 + 数据结构**的深入可视化方式，提供相当于直接阅读源代码的详细信息。包含完整的类型定义、函数签名、数据流转和错误处理机制。

---

## 🏗️ 文件整体函数关系图

### 所有函数的调用关系与数据流

```mermaid
graph TB
    %% 定义样式
    classDef exportFunc fill:#FFE4E1,stroke:#000000,stroke-width:3px,color:#000000
    classDef internalFunc fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef externalFunc fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef dataType fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    classDef utilFunc fill:#F5F5DC,stroke:#000000,stroke-width:2px,color:#000000

    %% 输入数据类型
    INPUT_SINGLE["TTSTaskInput<br/>{<br/>  ttsId: string,<br/>  text: string,<br/>  type: TTSType<br/>}"]
    
    INPUT_BATCH["TTSTaskInput[]<br/>// 批量任务数组"]
    
    AUDIO_DATA["ArrayBuffer<br/>// WAV音频数据<br/>// 24khz-16bit-mono<br/>// 20-100KB"]

    %% 导出函数 (对外API)
    PROCESS_SINGLE["🎵 processRealtimeTTS()<br/>async (task: TTSTaskInput, env: Env)<br/>→ Promise&lt;TTSProcessingResult&gt;<br/><br/>// FC-05: 核心TTS处理函数<br/>// 完整的单任务处理流程"]
    
    PROCESS_BATCH["📦 processBatchRealtimeTTS()<br/>async (tasks: TTSTaskInput[], env: Env,<br/>       maxConcurrency: number = 50)<br/>→ Promise&lt;TTSProcessingResult[]&gt;<br/><br/>// 批量处理 + 并发控制"]
    
    UPLOAD_R2["📤 uploadAudioToR2()<br/>async (ttsId: string, audioBuffer: ArrayBuffer,<br/>       env: Env)<br/>→ Promise&lt;string&gt;<br/><br/>// FC-07: R2存储上传服务"]
    
    VALIDATE_R2["✅ validateR2Connection()<br/>async (env: Env)<br/>→ Promise&lt;boolean&gt;<br/><br/>// R2连接健康检查"]
    
    CLEANUP_TASKS["🧹 cleanupStaleProcessingTasks()<br/>async (env: Env, timeoutMinutes: number = 30)<br/>→ Promise&lt;number&gt;<br/><br/>// 清理超时任务"]

    %% 内部函数
    PERFORM_UPLOAD["🔄 performR2Upload()<br/>async (params: R2UploadParams)<br/>→ Promise&lt;string&gt;<br/><br/>// 内部实现函数<br/>// 执行具体的R2上传操作"]

    %% 外部依赖函数
    VALIDATE_TEXT["🔍 validateTTSText()<br/>(text: string) → boolean<br/><br/>// from azure-tts.util<br/>// 验证TTS文本有效性"]
    
    AZURE_CALL["☁️ callAzureRealtimeTTSWithRetry()<br/>async (text: string, type: TTSType, env: Env)<br/>→ Promise&lt;ArrayBuffer&gt;<br/><br/>// from azure-tts.util<br/>// Azure TTS API调用"]
    
    UPDATE_STATUS["📊 updateTaskStatus()<br/>async (ttsId: string, status: TaskStatus,<br/>       result: TaskUpdateData, env: Env)<br/>→ Promise&lt;boolean&gt;<br/><br/>// from task-manager.service<br/>// 数据库状态更新"]
    
    RATE_LIMITER["⚡ getGlobalRateLimiter()<br/>(maxConcurrency: number, tps: number)<br/>→ RateLimiter<br/><br/>// from rate-limiter.service<br/>// 🎯 真正的任务执行调度者<br/>// rateLimiter.execute(() => processRealtimeTTS())"]

    %% 输出数据类型
    OUTPUT_SINGLE["TTSProcessingResult<br/>{<br/>  ttsId: string,<br/>  success: boolean,<br/>  status: 'completed'|'failed',<br/>  audioUrl?: string,<br/>  errorMessage?: string,<br/>  processingTime: number,<br/>  azureCallTime: number,<br/>  r2UploadTime: number,<br/>  dbUpdateTime: number<br/>}"]
    
    OUTPUT_BATCH["TTSProcessingResult[]<br/>// 批量处理结果数组"]
    
    AUDIO_URL["string<br/>// CDN URL<br/>// 'https://audio.senseword.app/<br/>//  {ttsId}.wav'"]

    %% 主要数据流 - 修正：体现限流器调用任务执行
    INPUT_SINGLE --> PROCESS_SINGLE
    INPUT_BATCH --> PROCESS_BATCH

    PROCESS_SINGLE --> VALIDATE_TEXT
    PROCESS_SINGLE --> UPDATE_STATUS
    PROCESS_SINGLE --> AZURE_CALL
    PROCESS_SINGLE --> UPLOAD_R2
    PROCESS_SINGLE --> OUTPUT_SINGLE

    %% 🎯 关键修正：限流器调用任务执行
    PROCESS_BATCH --> RATE_LIMITER
    RATE_LIMITER -->|"rateLimiter.execute(() => processRealtimeTTS())"| PROCESS_SINGLE
    PROCESS_SINGLE --> OUTPUT_BATCH
    
    AZURE_CALL --> AUDIO_DATA
    AUDIO_DATA --> UPLOAD_R2
    UPLOAD_R2 --> PERFORM_UPLOAD
    PERFORM_UPLOAD --> AUDIO_URL
    
    VALIDATE_R2 -.->|"健康检查"| UPLOAD_R2
    CLEANUP_TASKS -.->|"维护任务"| UPDATE_STATUS

    %% 应用样式
    class PROCESS_SINGLE,PROCESS_BATCH,UPLOAD_R2,VALIDATE_R2,CLEANUP_TASKS exportFunc
    class PERFORM_UPLOAD internalFunc
    class VALIDATE_TEXT,AZURE_CALL,UPDATE_STATUS,RATE_LIMITER externalFunc
    class INPUT_SINGLE,INPUT_BATCH,OUTPUT_SINGLE,OUTPUT_BATCH,AUDIO_DATA,AUDIO_URL dataType
```

### 🎯 函数分类说明

#### 🔴 **导出函数** (红色边框) - 对外API
- **processRealtimeTTS()**: 核心单任务处理，🎯 **被限流器调用执行**
- **processBatchRealtimeTTS()**: 批量处理协调器，提交任务给限流器执行
- **uploadAudioToR2()**: R2存储上传服务
- **validateR2Connection()**: R2连接健康检查
- **cleanupStaleProcessingTasks()**: 清理超时任务

#### 🔵 **内部函数** (蓝色边框) - 内部实现
- **performR2Upload()**: R2上传的具体实现逻辑

#### 🟡 **外部依赖** (黄色边框) - 跨模块调用
- **validateTTSText()**: 来自azure-tts.util，文本有效性验证
- **callAzureRealtimeTTSWithRetry()**: 来自azure-tts.util，Azure TTS API调用
- **updateTaskStatus()**: 来自task-manager.service，数据库状态更新
- **getGlobalRateLimiter()**: 来自rate-limiter.service，🎯 **真正的任务执行调度者**

---

## 🔄 核心函数详细执行流程

### processRealtimeTTS() 完整执行步骤

```mermaid
flowchart TD
    %% 定义样式
    classDef startEnd fill:#FFE4E1,stroke:#000000,stroke-width:3px,color:#000000
    classDef process fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef decision fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef external fill:#F5F5DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef data fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    classDef error fill:#FFB6C1,stroke:#000000,stroke-width:2px,color:#000000

    START(["🎵 processRealtimeTTS()<br/>输入: TTSTaskInput, Env<br/>输出: Promise&lt;TTSProcessingResult&gt;"])
    
    %% 初始化阶段
    INIT["📊 初始化性能计时器<br/>startTime = Date.now()<br/>azureCallTime = 0<br/>r2UploadTime = 0<br/>dbUpdateTime = 0<br/><br/>初始化结果对象<br/>result: TTSProcessingResult = {<br/>  ttsId: task.ttsId,<br/>  success: false,<br/>  status: 'failed',<br/>  processingTime: 0,<br/>  azureCallTime: 0,<br/>  r2UploadTime: 0,<br/>  dbUpdateTime: 0<br/>}"]
    
    %% 验证阶段
    VALIDATE{"🔍 validateTTSText(task.text)<br/>→ boolean<br/><br/>检查文本是否有效<br/>• 非空检查<br/>• 长度限制<br/>• 特殊字符过滤"}
    
    VALIDATE_ERROR["❌ 验证失败<br/>throw new Error(<br/>  `无效的TTS文本: ${task.text}`<br/>)"]
    
    %% 状态更新1
    UPDATE_PROCESSING["📝 updateTaskStatus()<br/>参数: (task.ttsId, 'processing', {}, env)<br/>返回: Promise&lt;boolean&gt;<br/><br/>SQL: UPDATE tts_tasks<br/>SET status = 'processing',<br/>    updatedAt = datetime('now')<br/>WHERE ttsId = ?"]
    
    %% Azure TTS调用
    AZURE_CALL["☁️ callAzureRealtimeTTSWithRetry()<br/>参数: (task.text, task.type, env)<br/>返回: Promise&lt;ArrayBuffer&gt;<br/><br/>• 根据type选择语音<br/>• 构建SSML格式<br/>• HTTP POST到Azure TTS<br/>• 重试机制(最多3次)"]
    
    AUDIO_BUFFER["🎵 ArrayBuffer<br/>音频数据<br/>• 格式: WAV<br/>• 采样率: 24kHz<br/>• 位深: 16bit<br/>• 声道: mono<br/>• 大小: 20-100KB"]
    
    %% R2上传
    R2_UPLOAD["📤 uploadAudioToR2()<br/>参数: (task.ttsId, audioBuffer, env)<br/>返回: Promise&lt;string&gt;<br/><br/>• 文件名: {ttsId}.wav<br/>• Content-Type: audio/wav<br/>• Cache-Control: 1年缓存"]
    
    AUDIO_URL["🔗 string<br/>CDN URL<br/>'https://audio.senseword.app/<br/>{ttsId}.wav'"]
    
    %% 状态更新2
    UPDATE_COMPLETED["✅ updateTaskStatus()<br/>参数: (task.ttsId, 'completed', {<br/>  audioUrl: string,<br/>  completedAt: ISO时间戳<br/>}, env)<br/><br/>SQL: UPDATE tts_tasks<br/>SET status = 'completed',<br/>    audioUrl = ?,<br/>    completedAt = ?,<br/>    updatedAt = datetime('now')<br/>WHERE ttsId = ?"]
    
    UPDATE_CHECK{"📊 数据库更新<br/>是否成功?<br/><br/>检查返回值<br/>updateSuccess === true"}
    
    UPDATE_ERROR["❌ 数据库更新失败<br/>throw new Error(<br/>  '数据库状态更新失败'<br/>)"]
    
    %% 成功结果构建
    SUCCESS_RESULT["🎉 构建成功结果<br/>result.success = true<br/>result.status = 'completed'<br/>result.audioUrl = audioUrl<br/>result.azureCallTime = azureCallTime<br/>result.r2UploadTime = r2UploadTime<br/>result.dbUpdateTime = dbUpdateTime<br/>result.processingTime = Date.now() - startTime"]
    
    SUCCESS_END(["✅ 返回成功结果<br/>TTSProcessingResult"])
    
    %% 错误处理
    CATCH_ERROR["⚠️ catch (error)<br/>errorMessage = error instanceof Error<br/>  ? error.message : '未知错误'<br/><br/>记录错误日志"]
    
    UPDATE_FAILED["📝 updateTaskStatus()<br/>参数: (task.ttsId, 'failed', {<br/>  errorMessage: string,<br/>  completedAt: ISO时间戳<br/>}, env)<br/><br/>try-catch包装<br/>防止二次错误"]
    
    FAILED_RESULT["💔 构建失败结果<br/>result.success = false<br/>result.status = 'failed'<br/>result.errorMessage = errorMessage<br/>result.azureCallTime = azureCallTime<br/>result.r2UploadTime = r2UploadTime<br/>result.dbUpdateTime = dbUpdateTime<br/>result.processingTime = Date.now() - startTime"]
    
    FAILED_END(["❌ 返回失败结果<br/>TTSProcessingResult"])

    %% 主流程
    START --> INIT
    INIT --> VALIDATE
    VALIDATE -->|"true"| UPDATE_PROCESSING
    VALIDATE -->|"false"| VALIDATE_ERROR
    
    UPDATE_PROCESSING --> AZURE_CALL
    AZURE_CALL --> AUDIO_BUFFER
    AUDIO_BUFFER --> R2_UPLOAD
    R2_UPLOAD --> AUDIO_URL
    AUDIO_URL --> UPDATE_COMPLETED
    
    UPDATE_COMPLETED --> UPDATE_CHECK
    UPDATE_CHECK -->|"true"| SUCCESS_RESULT
    UPDATE_CHECK -->|"false"| UPDATE_ERROR
    
    SUCCESS_RESULT --> SUCCESS_END
    
    %% 错误处理流程
    VALIDATE_ERROR --> CATCH_ERROR
    UPDATE_ERROR --> CATCH_ERROR
    AZURE_CALL -.->|"timeout/error"| CATCH_ERROR
    R2_UPLOAD -.->|"upload failed"| CATCH_ERROR
    
    CATCH_ERROR --> UPDATE_FAILED
    UPDATE_FAILED --> FAILED_RESULT
    FAILED_RESULT --> FAILED_END

    %% 应用样式
    class START,SUCCESS_END,FAILED_END startEnd
    class INIT,SUCCESS_RESULT,CATCH_ERROR,UPDATE_FAILED,FAILED_RESULT process
    class VALIDATE,UPDATE_CHECK decision
    class UPDATE_PROCESSING,AZURE_CALL,R2_UPLOAD,UPDATE_COMPLETED,UPDATE_FAILED external
    class AUDIO_BUFFER,AUDIO_URL data
    class VALIDATE_ERROR,UPDATE_ERROR error
```

---

## 📊 数据结构转换链

### 完整的数据类型定义与转换关系

```mermaid
graph LR
    %% 定义样式
    classDef inputType fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef outputType fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    classDef intermediateType fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef configType fill:#F5F5DC,stroke:#000000,stroke-width:2px,color:#000000

    %% 输入数据类型
    subgraph INPUT["📥 输入数据类型"]
        TTSTaskInput["TTSTaskInput<br/>{<br/>  ttsId: string        // 24位哈希ID<br/>  text: string         // TTS文本内容<br/>  type: TTSType        // TTS类型<br/>}<br/><br/>TTSType = 'phonetic_name'<br/>        | 'phonetic_bre'<br/>        | 'phonetic_ipa'<br/>        | 'example_sentence'<br/>        | 'phrase_breakdown'"]
        
        EnvType["Env<br/>{<br/>  TTS_DB: D1Database           // 任务数据库<br/>  AUDIO_BUCKET: R2Bucket       // 音频存储<br/>  AZURE_TTS_KEY?: string       // Azure密钥<br/>  AZURE_TTS_KEYS?: string      // 多密钥<br/>  AZURE_TTS_REGION: string     // Azure区域<br/>  VOICE_BRE: string            // 英式语音<br/>  VOICE_NAME: string           // 美式语音<br/>}"]
    end

    %% 中间数据类型
    subgraph INTERMEDIATE["🔄 中间数据类型"]
        AzureParams["AzureTTSParams<br/>{<br/>  text: string         // 文本内容<br/>  type: TTSType        // 决定语音选择<br/>  azureKey: string     // API密钥<br/>  azureRegion: string  // 区域<br/>}"]
        
        AudioBuffer["ArrayBuffer<br/>// WAV格式音频数据<br/>// 大小: 20-100KB<br/>// 格式: 24khz-16bit-mono-pcm<br/>// 时长: 0.5-5秒"]
        
        R2Params["R2UploadParams<br/>{<br/>  ttsId: string        // 文件名<br/>  audioBuffer: ArrayBuffer  // 音频数据<br/>  r2Bucket: R2Bucket   // 存储桶<br/>}"]
        
        UpdateParams["TaskUpdateParams<br/>{<br/>  ttsId: string        // 任务ID<br/>  status: TaskStatus   // 新状态<br/>  result: TaskUpdateData    // 更新数据<br/>  db: D1Database       // 数据库连接<br/>}"]
    end

    %% 输出数据类型
    subgraph OUTPUT["📤 输出数据类型"]
        TTSResult["TTSProcessingResult<br/>{<br/>  ttsId: string        // 任务ID<br/>  success: boolean     // 是否成功<br/>  status: 'completed'|'failed'  // 状态<br/>  audioUrl?: string    // 音频URL<br/>  errorMessage?: string // 错误信息<br/>  processingTime: number    // 总耗时<br/>  azureCallTime: number     // Azure耗时<br/>  r2UploadTime: number      // R2耗时<br/>  dbUpdateTime: number      // DB耗时<br/>}"]
        
        AudioURL["string<br/>// CDN访问URL<br/>// 'https://audio.senseword.app/<br/>//  {ttsId}.wav'"]
        
        UpdateResult["boolean<br/>// 数据库更新是否成功"]
    end

    %% 配置类型
    subgraph CONFIG["⚙️ 配置类型"]
        VoiceMapping["VOICE_MAPPING<br/>{<br/>  'phonetic_bre': 'en-GB-MiaNeural',<br/>  'phonetic_name': 'en-US-AndrewNeural',<br/>  'phonetic_ipa': 'en-US-AndrewNeural',<br/>  'example_sentence': 'en-US-AndrewNeural',<br/>  'phrase_breakdown': 'en-US-AndrewNeural'<br/>}"]
        
        TTSConfig["TTS_CONFIG<br/>{<br/>  MAX_CONCURRENT_REQUESTS: 50,<br/>  REQUEST_TIMEOUT: 30000,<br/>  RETRY_ATTEMPTS: 3,<br/>  RETRY_DELAY: 1000,<br/>  AUDIO_FORMAT: 'riff-24khz-16bit-mono-pcm',<br/>  CACHE_CONTROL: 'public, max-age=31536000'<br/>}"]
    end

    %% 数据转换流程
    TTSTaskInput --> AzureParams
    EnvType --> AzureParams
    AzureParams --> AudioBuffer
    AudioBuffer --> R2Params
    R2Params --> AudioURL
    TTSResult --> UpdateParams
    UpdateParams --> UpdateResult
    
    %% 配置影响
    VoiceMapping -.-> AzureParams
    TTSConfig -.-> R2Params

    %% 应用样式
    class TTSTaskInput,EnvType inputType
    class TTSResult,AudioURL,UpdateResult outputType
    class AzureParams,AudioBuffer,R2Params,UpdateParams intermediateType
    class VoiceMapping,TTSConfig configType
```

---

## 🔗 与其他文件的接口关系

### 跨模块函数调用与依赖关系

```mermaid
graph TB
    %% 定义样式
    classDef currentFile fill:#FFE4E1,stroke:#000000,stroke-width:3px,color:#000000
    classDef externalFile fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef typeFile fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    classDef function fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000

    %% 当前文件
    subgraph CURRENT["📄 realtime-tts.service.ts"]
        PROCESS_FUNC["processRealtimeTTS()<br/>核心TTS处理"]
        BATCH_FUNC["processBatchRealtimeTTS()<br/>批量处理"]
        UPLOAD_FUNC["uploadAudioToR2()<br/>R2上传"]
        VALIDATE_FUNC["validateR2Connection()<br/>连接验证"]
        CLEANUP_FUNC["cleanupStaleProcessingTasks()<br/>清理任务"]
    end

    %% 外部文件依赖
    subgraph AZURE_UTIL["📄 azure-tts.util.ts"]
        VALIDATE_TEXT_FUNC["validateTTSText()<br/>(text: string) → boolean<br/><br/>验证TTS文本有效性"]
        
        AZURE_CALL_FUNC["callAzureRealtimeTTSWithRetry()<br/>(text: string, type: TTSType, env: Env)<br/>→ Promise&lt;ArrayBuffer&gt;<br/><br/>• 语音选择逻辑<br/>• SSML构建<br/>• HTTP调用Azure TTS<br/>• 重试机制"]
    end

    subgraph TASK_MANAGER["📄 task-manager.service.ts"]
        UPDATE_STATUS_FUNC["updateTaskStatus()<br/>(ttsId: string, status: TaskStatus,<br/> result: TaskUpdateData, env: Env)<br/>→ Promise&lt;boolean&gt;<br/><br/>• SQL UPDATE操作<br/>• 状态转换验证<br/>• 错误处理"]
    end

    subgraph RATE_LIMITER["📄 rate-limiter.service.ts"]
        RATE_LIMITER_FUNC["getGlobalRateLimiter()<br/>(maxConcurrency: number, tps: number)<br/>→ RateLimiter<br/><br/>• 并发控制<br/>• TPS限制<br/>• 队列管理"]
    end

    subgraph TYPES["📄 realtime-tts-types.ts"]
        TYPE_DEFS["类型定义<br/>• TTSTaskInput<br/>• TTSProcessingResult<br/>• R2UploadParams<br/>• TaskUpdateParams<br/>• Env<br/>• TTSType<br/>• TaskStatus"]
        
        CONFIG_DEFS["配置常量<br/>• VOICE_MAPPING<br/>• TTS_CONFIG"]
    end

    %% 调用关系
    PROCESS_FUNC --> VALIDATE_TEXT_FUNC
    PROCESS_FUNC --> UPDATE_STATUS_FUNC
    PROCESS_FUNC --> AZURE_CALL_FUNC
    PROCESS_FUNC --> UPLOAD_FUNC
    
    BATCH_FUNC --> RATE_LIMITER_FUNC
    BATCH_FUNC --> PROCESS_FUNC
    
    VALIDATE_FUNC --> TYPES
    CLEANUP_FUNC --> UPDATE_STATUS_FUNC
    
    %% 类型依赖
    CURRENT --> TYPE_DEFS
    AZURE_UTIL --> TYPE_DEFS
    TASK_MANAGER --> TYPE_DEFS
    RATE_LIMITER --> TYPE_DEFS
    
    UPLOAD_FUNC --> CONFIG_DEFS

    %% 应用样式
    class PROCESS_FUNC,BATCH_FUNC,UPLOAD_FUNC,VALIDATE_FUNC,CLEANUP_FUNC currentFile
    class VALIDATE_TEXT_FUNC,AZURE_CALL_FUNC,UPDATE_STATUS_FUNC,RATE_LIMITER_FUNC function
    class AZURE_UTIL,TASK_MANAGER,RATE_LIMITER externalFile
    class TYPES,TYPE_DEFS,CONFIG_DEFS typeFile
```

---

## 💡 关键设计特点总结

### 🎯 **函数设计原则**
1. **单一职责**: 每个函数都有明确的单一职责
2. **完整性能监控**: 分段计时，详细记录每个环节的耗时
3. **统一错误处理**: 所有错误都转换为标准的TTSProcessingResult
4. **幂等性设计**: 支持重试，避免重复处理

### 🔄 **数据流设计**
1. **类型安全**: 完整的TypeScript类型定义
2. **数据转换链**: 清晰的输入→中间→输出数据流
3. **配置驱动**: 通过配置常量控制行为
4. **环境隔离**: 通过Env接口注入依赖

### 🛡️ **错误处理策略**
1. **分层错误处理**: 每个函数都有独立的错误处理
2. **状态一致性**: 确保数据库状态与实际处理结果一致
3. **详细错误信息**: 包含具体的错误原因和上下文
4. **防御性编程**: 对外部依赖的调用都有错误保护

---

## 📋 批量处理函数详细分析

### processBatchRealtimeTTS() 并发控制机制

```mermaid
sequenceDiagram
    participant CALLER as 📞 调用者
    participant BATCH as 📦 processBatchRealtimeTTS
    participant LIMITER as ⚡ RateLimiter
    participant SINGLE as 🎵 processRealtimeTTS
    participant AZURE as ☁️ Azure TTS
    participant R2 as 📁 R2 Storage

    Note over CALLER,R2: 批量处理流程 (maxConcurrency=50, TPS=50)

    CALLER->>BATCH: tasks: TTSTaskInput[]<br/>env: Env<br/>maxConcurrency: 50

    BATCH->>BATCH: 验证输入<br/>if (tasks.length === 0) return []

    BATCH->>LIMITER: getGlobalRateLimiter(50, 50)<br/>创建限流器

    Note over BATCH: Promise.allSettled 确保所有任务都被处理

    loop 每个任务 (并发执行)
        BATCH->>LIMITER: rateLimiter.execute(() => <br/>  processRealtimeTTS(task, env))

        Note over LIMITER: 🎯 限流器调用任务执行
        LIMITER->>SINGLE: await taskFunction()<br/>限流器控制执行时机
        SINGLE->>AZURE: Azure TTS调用
        SINGLE->>R2: R2上传
        SINGLE-->>LIMITER: TTSProcessingResult
        LIMITER-->>BATCH: 处理结果
    end

    BATCH->>BATCH: 处理 Promise.allSettled 结果<br/>• fulfilled → 直接使用结果<br/>• rejected → 创建失败结果

    BATCH->>BATCH: 统计结果<br/>successCount = results.filter(r => r.success).length<br/>failedCount = results.length - successCount

    BATCH-->>CALLER: TTSProcessingResult[]<br/>包含所有任务的处理结果
```

### 🎯 批量处理的关键特性

#### **1. 并发控制策略**
```typescript
// 限流器配置
const rateLimiter = getGlobalRateLimiter(maxConcurrency, 50); // 50 TPS

// Promise.allSettled 确保错误隔离
const settledResults = await Promise.allSettled(
  tasks.map(task =>
    rateLimiter.execute(() => processRealtimeTTS(task, env))
  )
);
```

#### **2. 错误隔离机制**
```typescript
// 单任务失败不影响其他任务
if (settledResult.status === 'fulfilled') {
  results.push(settledResult.value);
} else {
  // 创建标准化的失败结果
  const failedResult: TTSProcessingResult = {
    ttsId: originalTask.ttsId,
    success: false,
    status: 'failed',
    errorMessage: settledResult.reason?.message || '未知错误',
    processingTime: 0,
    azureCallTime: 0,
    r2UploadTime: 0,
    dbUpdateTime: 0
  };
  results.push(failedResult);
}
```

---

## 🔧 工具函数详细分析

### validateR2Connection() 健康检查

```typescript
// 函数签名
async function validateR2Connection(env: Env): Promise<boolean>

// 实现逻辑
try {
  // 尝试列出存储桶内容（限制1个对象）
  const objects = await env.AUDIO_BUCKET.list({ limit: 1 });
  console.log(`[R2 Validation] ✅ R2连接正常, 对象数量: ${objects.objects.length}`);
  return true;
} catch (error) {
  console.error(`[R2 Validation] ❌ R2连接失败:`, error);
  return false;
}
```

### cleanupStaleProcessingTasks() 清理机制

```typescript
// 函数签名
async function cleanupStaleProcessingTasks(
  env: Env,
  timeoutMinutes: number = 30
): Promise<number>

// SQL查询
const query = `
  UPDATE tts_tasks
  SET status = 'failed',
      errorMessage = 'Processing timeout',
      updatedAt = datetime('now')
  WHERE status = 'processing'
    AND updatedAt < ?
`;

// 执行逻辑
const timeoutThreshold = new Date(Date.now() - timeoutMinutes * 60 * 1000).toISOString();
const result = await env.TTS_DB.prepare(query).bind(timeoutThreshold).run();
const cleanedCount = result.meta?.changes || 0;
```

---

## 📊 性能监控与日志系统

### 详细的性能计时机制

```typescript
// processRealtimeTTS() 中的性能监控
const startTime = Date.now();
let azureCallTime = 0;
let r2UploadTime = 0;
let dbUpdateTime = 0;

// Azure调用计时
const azureStart = Date.now();
const audioBuffer = await callAzureRealtimeTTSWithRetry(task.text, task.type, env);
azureCallTime = Date.now() - azureStart;

// R2上传计时
const r2Start = Date.now();
const audioUrl = await uploadAudioToR2(task.ttsId, audioBuffer, env);
r2UploadTime = Date.now() - r2Start;

// 数据库更新计时
const dbUpdateStart = Date.now();
await updateTaskStatus(task.ttsId, 'processing', {}, env);
dbUpdateTime += Date.now() - dbUpdateStart;

// 总处理时间
result.processingTime = Date.now() - startTime;
```

### 完整的日志记录系统

```typescript
// 开始处理日志
console.log(`[Realtime TTS] 🎵 开始处理任务: ${task.ttsId} (${task.type})`);

// Azure调用日志
console.log(`[Realtime TTS] 🎤 调用Azure TTS: ${task.text.substring(0, 50)}...`);
console.log(`[Realtime TTS] ✅ Azure TTS完成: ${azureCallTime}ms, 音频大小: ${audioBuffer.byteLength} bytes`);

// R2上传日志
console.log(`[Realtime TTS] 📦 上传音频到R2存储`);
console.log(`[Realtime TTS] ✅ R2上传完成: ${r2UploadTime}ms, URL: ${audioUrl}`);

// 完成日志
console.log(`[Realtime TTS] ✅ 任务处理完成: ${task.ttsId} (${result.processingTime}ms)`);

// 错误日志
console.error(`[Realtime TTS] ❌ 任务处理失败: ${task.ttsId} - ${errorMessage}`);
```

---

## 🎯 总结：源代码级别的理解

通过这个函数签名级可视化文档，你可以获得相当于直接阅读源代码的深入理解：

### 📋 **完整的函数信息**
- ✅ 精确的函数签名和参数类型
- ✅ 详细的返回值类型定义
- ✅ 完整的数据结构字段说明
- ✅ 具体的实现逻辑和算法

### 🔄 **清晰的执行流程**
- ✅ 步骤级别的执行顺序
- ✅ 条件判断和分支逻辑
- ✅ 错误处理和异常路径
- ✅ 性能监控和日志记录

### 🏗️ **系统级别的架构**
- ✅ 模块间的依赖关系
- ✅ 数据在系统中的流转
- ✅ 配置和常量的使用
- ✅ 外部系统的集成点

### 💡 **设计思想的体现**
- ✅ 单一职责和模块化设计
- ✅ 错误隔离和防御性编程
- ✅ 性能监控和可观测性
- ✅ 类型安全和数据一致性

---

## 🎯 关键机制澄清：限流器调用任务执行

### 限流器作为任务执行调度者的核心机制

```mermaid
sequenceDiagram
    participant BATCH as 📦 processBatchRealtimeTTS
    participant LIMITER as ⚡ RateLimiter
    participant WRAPPER as 🎁 任务包装器
    participant PROCESS as 🎵 processRealtimeTTS

    Note over BATCH,PROCESS: 限流器调用任务执行的关键机制

    BATCH->>LIMITER: rateLimiter.execute(() => processRealtimeTTS(task, env))
    Note over LIMITER: 限流器接收包装函数，控制执行时机

    LIMITER->>LIMITER: 检查槽位可用性<br/>检查TPS限制<br/>决定执行时机

    LIMITER->>WRAPPER: 🎯 await taskFunction()<br/>限流器调用包装函数
    WRAPPER->>PROCESS: await processRealtimeTTS(task, env)<br/>包装函数调用真正的业务逻辑

    Note over PROCESS: processRealtimeTTS 被限流器间接调用
    PROCESS->>PROCESS: 执行TTS业务逻辑<br/>• Azure TTS调用<br/>• R2存储上传<br/>• 数据库状态更新

    PROCESS-->>WRAPPER: 返回TTSProcessingResult
    WRAPPER-->>LIMITER: 返回处理结果
    LIMITER-->>BATCH: 返回最终结果
```

### 调用关系的精确理解

```mermaid
graph TB
    %% 定义样式
    classDef caller fill:#FFE4E1,stroke:#000000,stroke-width:3px,color:#000000
    classDef controller fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef wrapper fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef business fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000

    BATCH["📦 processBatchRealtimeTTS()<br/><br/>• 批量任务协调者<br/>• 提交包装函数给限流器<br/>• 收集所有处理结果"]

    LIMITER["⚡ RateLimiter.execute()<br/><br/>• 🎯 真正的任务调用者<br/>• 控制执行时机<br/>• await taskFunction()"]

    WRAPPER["🎁 () => processRealtimeTTS(task, env)<br/><br/>• 任务包装函数<br/>• 绑定参数<br/>• 错误边界"]

    PROCESS["🎵 processRealtimeTTS(task, env)<br/><br/>• 被调用的业务逻辑<br/>• 核心TTS处理<br/>• 返回处理结果"]

    BATCH -->|"提交包装函数"| LIMITER
    LIMITER -->|"await taskFunction()"| WRAPPER
    WRAPPER -->|"await processRealtimeTTS()"| PROCESS

    %% 应用样式
    class BATCH caller
    class LIMITER controller
    class WRAPPER wrapper
    class PROCESS business
```

**关键理解**:
- ✅ **限流器是真正的任务调用者**: `rateLimiter.execute()` 内部调用 `await taskFunction()`
- ✅ **processRealtimeTTS 是被调用者**: 被包装为函数后由限流器控制执行
- ✅ **调用链路**: `batch → limiter → wrapper → processRealtimeTTS`
- ✅ **执行控制**: 限流器决定何时、如何、调用多少个任务
- ✅ **职责分离**: 批量协调 vs 执行控制 vs 业务逻辑的清晰分离

这种可视化方法让你能够快速掌握复杂代码文件的核心逻辑，同时保持了源代码级别的准确性和完整性。
