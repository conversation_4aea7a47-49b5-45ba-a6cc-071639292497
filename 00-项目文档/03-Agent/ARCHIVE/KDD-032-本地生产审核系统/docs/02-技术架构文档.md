# SenseWord ContentHub - 技术架构文档

## 🏗️ 系统架构概览

### 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    SenseWord ContentHub                     │
├─────────────────────────────────────────────────────────────┤
│  Frontend (React + TypeScript)                             │
│  ├── Dashboard (统计概览)                                    │
│  ├── Words Management (单词管理)                            │
│  └── Content Review (内容审核)                              │
├─────────────────────────────────────────────────────────────┤
│  Backend API (Node.js + Fastify)                           │
│  ├── Definitions Service                                   │
│  ├── ExampleSentences Service                              │
│  └── Stats Service                                         │
├─────────────────────────────────────────────────────────────┤
│  Data Layer (SQLite)                                       │
│  ├── definitions (62,815 records)                          │
│  ├── example_sentences                                     │
│  └── words_for_publish                                     │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 技术栈选择

### 前端技术栈
- **框架**: React 18 + TypeScript
- **路由**: TanStack Router
- **UI组件**: shadcn/ui + Tailwind CSS
- **状态管理**: React Hooks + Context
- **构建工具**: Vite
- **开发工具**: ESLint + Prettier

### 后端技术栈
- **运行时**: Node.js 20+
- **框架**: Fastify (高性能HTTP框架)
- **语言**: TypeScript
- **数据库**: SQLite (本地化部署)
- **API文档**: Swagger/OpenAPI
- **开发工具**: tsx (TypeScript执行器)

### 数据库设计
- **类型**: SQLite (文件数据库)
- **位置**: `/senseword-content-factory/01-EN/SQLite/senseword_content.db`
- **特点**: 无服务器、零配置、本地化

## 📁 项目文件结构

### 前端项目结构
```
ContentHub-Dashboard/
├── src/
│   ├── components/          # 可复用组件
│   │   ├── ui/             # shadcn/ui组件
│   │   ├── layout/         # 布局组件
│   │   └── word-detail-dialog.tsx
│   ├── routes/             # 页面路由
│   │   ├── _authenticated/ # 需要认证的页面
│   │   │   ├── dashboard/  # 统计面板
│   │   │   └── words/      # 单词管理
│   │   └── __root.tsx      # 根路由
│   ├── hooks/              # 自定义Hooks
│   ├── lib/                # 工具库
│   │   ├── api.ts          # API客户端
│   │   └── utils.ts        # 工具函数
│   └── styles/             # 样式文件
├── public/                 # 静态资源
└── package.json           # 依赖配置
```

### 后端项目结构
```
ContentHub-API/
├── src/
│   ├── dao/                # 数据访问层
│   │   ├── definitionsDao.ts
│   │   ├── exampleSentencesDao.ts
│   │   └── statsDao.ts
│   ├── services/           # 业务逻辑层
│   │   ├── definitionsService.ts
│   │   └── exampleSentencesService.ts
│   ├── routes/             # 路由层
│   │   └── definitions.ts
│   ├── types/              # 类型定义
│   │   └── index.ts
│   ├── database/           # 数据库连接
│   │   └── connection.ts
│   └── server.ts           # 服务器入口
├── docs/                   # API文档
└── package.json           # 依赖配置
```

## 🗄️ 数据库架构

### 核心数据表

#### 1. definitions 表
```sql
CREATE TABLE definitions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    word TEXT NOT NULL,
    learningLanguage TEXT NOT NULL DEFAULT 'en',
    scaffoldingLanguage TEXT NOT NULL DEFAULT 'zh',
    definitionJson TEXT,
    frequency TEXT CHECK(frequency IN ('High', 'Medium', 'Low', 'Rare')),
    priorityScore REAL,
    auditStatus TEXT CHECK(auditStatus IN ('pending_review', 'approved', 'rejected', 'in_translation')) DEFAULT 'pending_review',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. example_sentences 表
```sql
CREATE TABLE example_sentences (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    word TEXT NOT NULL,
    learningLanguage TEXT NOT NULL DEFAULT 'en',
    scaffoldingLanguage TEXT NOT NULL DEFAULT 'zh',
    examplesJson TEXT,
    auditStatus TEXT CHECK(auditStatus IN ('pending_review', 'approved', 'rejected', 'in_translation')) DEFAULT 'pending_review',
    ttsStatus TEXT CHECK(ttsStatus IN ('pending', 'processing', 'completed', 'failed', 'skipped')) DEFAULT 'pending',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 3. words_for_publish 表
```sql
CREATE TABLE words_for_publish (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    word TEXT NOT NULL,
    learningLanguage TEXT NOT NULL DEFAULT 'en',
    scaffoldingLanguage TEXT NOT NULL DEFAULT 'zh',
    publishStatus TEXT CHECK(publishStatus IN ('pending_upload', 'uploading', 'published', 'failed', 'outdated')) DEFAULT 'pending_upload',
    publishedAt DATETIME,
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 数据关系
- **一对一关系**: definitions ↔ example_sentences (通过word字段关联)
- **一对一关系**: definitions ↔ words_for_publish (通过word字段关联)
- **索引优化**: 在word, auditStatus, frequency字段上建立索引

## 🔧 API设计

### RESTful API规范
- **基础路径**: `/api/v1`
- **认证方式**: 暂无 (本地部署)
- **数据格式**: JSON
- **错误处理**: 统一错误响应格式

### 核心API端点

#### 单词定义管理
```typescript
GET    /api/v1/definitions              // 获取单词列表
GET    /api/v1/definitions/:id          // 获取单个单词
PATCH  /api/v1/definitions/:id/audit-status  // 更新审核状态
GET    /api/v1/definitions/stats        // 获取统计信息
GET    /api/v1/definitions/search       // 搜索单词
```

#### 例句管理
```typescript
GET    /api/v1/definitions/:word/examples              // 获取例句
PATCH  /api/v1/definitions/:word/examples/audit-status // 更新例句审核状态
GET    /api/v1/definitions/tts-stats                   // 获取TTS统计
```

#### 发布管理
```typescript
GET    /api/v1/definitions/publish-stats  // 获取发布统计
```

### API响应格式
```typescript
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
```

## 🎨 前端架构设计

### 组件架构
- **原子组件**: Button, Input, Badge等基础UI组件
- **分子组件**: SearchBar, StatCard等功能组件
- **有机体组件**: WordTable, ReviewPanel等复合组件
- **模板组件**: PageLayout, AuthenticatedLayout等布局组件
- **页面组件**: Dashboard, WordsPage等完整页面

### 状态管理策略
- **本地状态**: useState, useReducer
- **服务器状态**: 自定义hooks (useDefinitions, useDefinitionActions)
- **全局状态**: React Context (用户信息、主题等)
- **缓存策略**: 内存缓存 + 乐观更新

### 路由设计
```typescript
// 路由结构
/                           // 根路径，重定向到dashboard
├── /dashboard              // 统计面板
├── /words                  // 单词管理
└── /tts                    // TTS管理 (预留)
```

## 🔄 数据流设计

### 请求流程
```
用户操作 → 组件事件 → API调用 → 后端处理 → 数据库操作 → 响应返回 → 状态更新 → UI重渲染
```

### 审核工作流
```
1. 用户选择单词 → 2. 加载详情数据 → 3. 显示Tab界面 → 4. 用户审核操作 → 5. 提交审核结果 → 6. 更新数据库 → 7. 刷新列表状态
```

## 🚀 性能优化

### 前端优化
- **代码分割**: 路由级别的懒加载
- **虚拟滚动**: 大列表性能优化
- **防抖节流**: 搜索和筛选操作
- **缓存策略**: API响应缓存
- **Bundle优化**: Tree shaking, 压缩

### 后端优化
- **数据库索引**: 关键字段索引优化
- **查询优化**: 分页查询, 字段选择
- **连接池**: SQLite连接管理
- **缓存层**: 内存缓存热点数据
- **并发控制**: 请求限流和队列

### 数据库优化
- **索引策略**: word, auditStatus, frequency字段
- **查询优化**: 避免全表扫描
- **数据分页**: 大数据集分页加载
- **定期维护**: VACUUM, ANALYZE操作

## 🔒 安全考虑

### 数据安全
- **本地部署**: 数据不离开本地环境
- **文件权限**: 数据库文件访问控制
- **备份策略**: 定期数据备份
- **操作日志**: 完整的操作审计

### 应用安全
- **输入验证**: 前后端双重验证
- **SQL注入防护**: 参数化查询
- **XSS防护**: 输出转义
- **CSRF防护**: Token验证

## 📊 监控和日志

### 应用监控
- **性能监控**: 响应时间、内存使用
- **错误监控**: 异常捕获和报告
- **用户行为**: 操作统计和分析
- **系统健康**: 服务状态检查

### 日志策略
- **结构化日志**: JSON格式日志
- **日志级别**: ERROR, WARN, INFO, DEBUG
- **日志轮转**: 按大小和时间轮转
- **日志分析**: 关键指标提取

## 🔄 部署和运维

### 开发环境
- **前端**: `npm run dev` (Vite开发服务器)
- **后端**: `npm run dev` (tsx watch模式)
- **数据库**: 本地SQLite文件

### 生产环境
- **前端**: 静态文件部署 (nginx)
- **后端**: PM2进程管理
- **数据库**: SQLite文件 + 定期备份
- **反向代理**: nginx配置

### 运维脚本
- **启动脚本**: 一键启动前后端服务
- **备份脚本**: 自动数据库备份
- **监控脚本**: 系统健康检查
- **更新脚本**: 应用版本更新

---

**文档版本**: v1.0  
**最后更新**: 2025-01-07  
**技术负责人**: AI开发团队  
**架构状态**: 已实现并稳定运行
