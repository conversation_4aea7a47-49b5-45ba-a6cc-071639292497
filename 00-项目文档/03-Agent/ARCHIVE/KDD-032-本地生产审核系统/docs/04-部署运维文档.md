# SenseWord ContentHub - 部署运维文档

## 🚀 快速启动指南

### 环境要求
- **Node.js**: 20.0.0 或更高版本
- **npm**: 9.0.0 或更高版本
- **操作系统**: macOS 10.15+, Windows 10+, Linux
- **内存**: 最低 4GB RAM
- **存储**: 最低 2GB 可用空间

### 一键启动 (开发环境)
```bash
# 1. 启动后端服务
cd SenseWord-ContentHub/ContentHub-API
npm install
npm run dev

# 2. 启动前端服务 (新终端)
cd SenseWord-ContentHub/ContentHub-Dashboard
npm install
npm run dev
```

### 访问地址
- **前端**: http://localhost:5173
- **后端API**: http://localhost:3000
- **API文档**: http://localhost:3000/docs

## 📁 项目目录结构

```
SenseWord-ContentHub/
├── ContentHub-API/          # 后端API服务
│   ├── src/                 # 源代码
│   ├── package.json         # 依赖配置
│   └── tsconfig.json        # TypeScript配置
├── ContentHub-Dashboard/    # 前端界面
│   ├── src/                 # 源代码
│   ├── public/              # 静态资源
│   ├── package.json         # 依赖配置
│   └── vite.config.ts       # Vite配置
└── docs/                    # 项目文档
```

## 🔧 开发环境配置

### 后端开发环境
```bash
cd ContentHub-API

# 安装依赖
npm install

# 开发模式启动 (热重载)
npm run dev

# 构建生产版本
npm run build

# 启动生产版本
npm start

# 代码检查
npm run lint

# 类型检查
npm run type-check
```

### 前端开发环境
```bash
cd ContentHub-Dashboard

# 安装依赖
npm install

# 开发模式启动 (热重载)
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview

# 代码检查
npm run lint

# 类型检查
npm run type-check
```

## 🏭 生产环境部署

### 方案一: 本地部署 (推荐)

#### 1. 准备工作
```bash
# 创建部署目录
mkdir -p /opt/senseword-contenthub
cd /opt/senseword-contenthub

# 克隆项目代码
git clone <repository-url> .

# 或者复制项目文件
cp -r /path/to/SenseWord-ContentHub/* .
```

#### 2. 后端部署
```bash
cd ContentHub-API

# 安装生产依赖
npm ci --only=production

# 构建项目
npm run build

# 安装PM2进程管理器
npm install -g pm2

# 启动服务
pm2 start ecosystem.config.js

# 设置开机自启
pm2 startup
pm2 save
```

#### 3. 前端部署
```bash
cd ContentHub-Dashboard

# 安装依赖
npm ci

# 构建生产版本
npm run build

# 部署到nginx
sudo cp -r dist/* /var/www/senseword-contenthub/
```

#### 4. Nginx配置
```nginx
# /etc/nginx/sites-available/senseword-contenthub
server {
    listen 80;
    server_name localhost;
    
    # 前端静态文件
    location / {
        root /var/www/senseword-contenthub;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 方案二: Docker部署

#### 1. 后端Dockerfile
```dockerfile
# ContentHub-API/Dockerfile
FROM node:20-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

#### 2. 前端Dockerfile
```dockerfile
# ContentHub-Dashboard/Dockerfile
FROM node:20-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
```

#### 3. Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  api:
    build: ./ContentHub-API
    ports:
      - "3000:3000"
    volumes:
      - ./data:/app/data
    environment:
      - NODE_ENV=production
    restart: unless-stopped

  dashboard:
    build: ./ContentHub-Dashboard
    ports:
      - "80:80"
    depends_on:
      - api
    restart: unless-stopped
```

## 📊 监控和日志

### PM2进程监控
```bash
# 查看进程状态
pm2 status

# 查看日志
pm2 logs

# 查看特定应用日志
pm2 logs senseword-api

# 重启应用
pm2 restart senseword-api

# 停止应用
pm2 stop senseword-api

# 删除应用
pm2 delete senseword-api
```

### 系统监控
```bash
# 查看系统资源使用
pm2 monit

# 查看详细信息
pm2 show senseword-api

# 查看进程列表
pm2 list
```

### 日志管理
```bash
# 日志轮转配置
pm2 install pm2-logrotate

# 设置日志保留天数
pm2 set pm2-logrotate:retain 7

# 设置日志文件大小限制
pm2 set pm2-logrotate:max_size 10M
```

## 🔒 数据备份和恢复

### 数据库备份
```bash
#!/bin/bash
# backup.sh - 数据库备份脚本

DB_PATH="/path/to/senseword_content.db"
BACKUP_DIR="/opt/backups/senseword"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
cp $DB_PATH $BACKUP_DIR/senseword_content_$DATE.db

# 压缩备份文件
gzip $BACKUP_DIR/senseword_content_$DATE.db

# 删除7天前的备份
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete

echo "备份完成: senseword_content_$DATE.db.gz"
```

### 自动备份设置
```bash
# 添加到crontab
crontab -e

# 每天凌晨2点自动备份
0 2 * * * /opt/scripts/backup.sh
```

### 数据恢复
```bash
# 停止服务
pm2 stop senseword-api

# 恢复数据库
gunzip /opt/backups/senseword/senseword_content_20250107_020000.db.gz
cp /opt/backups/senseword/senseword_content_20250107_020000.db /path/to/senseword_content.db

# 重启服务
pm2 start senseword-api
```

## 🔧 维护操作

### 应用更新
```bash
#!/bin/bash
# update.sh - 应用更新脚本

# 备份当前版本
cp -r /opt/senseword-contenthub /opt/senseword-contenthub.backup

# 拉取最新代码
cd /opt/senseword-contenthub
git pull origin main

# 更新后端
cd ContentHub-API
npm ci --only=production
npm run build
pm2 restart senseword-api

# 更新前端
cd ../ContentHub-Dashboard
npm ci
npm run build
sudo cp -r dist/* /var/www/senseword-contenthub/

echo "更新完成"
```

### 性能优化
```bash
# 数据库优化
sqlite3 /path/to/senseword_content.db "VACUUM;"
sqlite3 /path/to/senseword_content.db "ANALYZE;"

# 清理日志
pm2 flush

# 重启服务
pm2 restart all
```

### 健康检查
```bash
#!/bin/bash
# health-check.sh - 健康检查脚本

# 检查API服务
API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/v1/definitions/stats)
if [ $API_STATUS -eq 200 ]; then
    echo "API服务正常"
else
    echo "API服务异常，状态码: $API_STATUS"
    pm2 restart senseword-api
fi

# 检查前端服务
FRONTEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/)
if [ $FRONTEND_STATUS -eq 200 ]; then
    echo "前端服务正常"
else
    echo "前端服务异常，状态码: $FRONTEND_STATUS"
    sudo systemctl restart nginx
fi

# 检查数据库
if [ -f "/path/to/senseword_content.db" ]; then
    echo "数据库文件存在"
else
    echo "数据库文件不存在，请检查!"
fi
```

## 🚨 故障排除

### 常见问题

#### 1. 端口占用
```bash
# 查看端口占用
lsof -i :3000
lsof -i :5173

# 杀死占用进程
kill -9 <PID>
```

#### 2. 权限问题
```bash
# 修改文件权限
chmod +x /opt/scripts/*.sh
chown -R www-data:www-data /var/www/senseword-contenthub
```

#### 3. 内存不足
```bash
# 查看内存使用
free -h
pm2 monit

# 重启服务释放内存
pm2 restart all
```

#### 4. 数据库锁定
```bash
# 检查数据库连接
lsof /path/to/senseword_content.db

# 重启API服务
pm2 restart senseword-api
```

### 日志分析
```bash
# 查看错误日志
pm2 logs senseword-api --err

# 查看nginx错误日志
sudo tail -f /var/log/nginx/error.log

# 查看系统日志
journalctl -u nginx -f
```

## 📞 技术支持

### 联系方式
- **技术负责人**: AI开发团队
- **文档维护**: 项目文档团队
- **紧急联系**: 通过项目管理系统

### 支持时间
- **工作日**: 9:00 - 18:00
- **紧急情况**: 24/7 (通过紧急联系方式)

---

**文档版本**: v1.0  
**最后更新**: 2025-01-07  
**维护状态**: 活跃维护中  
**下次审查**: 2025-02-07
