# SenseWord ContentHub - 用户使用手册

## 📖 系统概述

### 产品介绍
SenseWord ContentHub 是一个专为英语学习内容管理设计的本地化审核系统，帮助内容团队高效管理62,815个单词的定义、例句和音频内容。

### 主要功能
- **内容管理**: 单词定义和例句的查看、编辑和管理
- **审核工作流**: 完整的内容审核流程，支持批准、拒绝和评论
- **数据统计**: 实时的内容生产和审核进度统计
- **搜索筛选**: 强大的搜索和筛选功能，快速定位目标内容

### 用户角色
- **内容审核员**: 负责单词定义和例句的质量审核
- **内容管理员**: 负责整体内容策略和进度监控
- **系统管理员**: 负责系统维护和用户管理

## 🚀 快速入门

### 系统访问
1. 打开浏览器，访问: `http://localhost:5173`
2. 系统会自动加载主界面
3. 无需登录，直接开始使用

### 界面布局
```
┌─────────────────────────────────────────────────────────────┐
│  SenseWord ContentHub                            [用户头像]  │
├─────────────────────────────────────────────────────────────┤
│ 侧边栏导航    │              主内容区域                    │
│ ├ 📊 统计面板  │                                           │
│ ├ 📚 单词管理  │                                           │
│ └ 🔊 TTS管理   │                                           │
└─────────────────────────────────────────────────────────────┘
```

## 📊 统计面板使用

### 功能概述
统计面板提供系统整体数据概览，帮助了解内容生产和审核进度。

### 主要指标
1. **总体统计**
   - 单词总数: 62,815个
   - 待审核数量
   - 已批准数量
   - 已拒绝数量

2. **频率分布**
   - High频率: 14,324个
   - Medium频率: 29,000个
   - Low频率: 14,117个
   - Rare频率: 5,374个

3. **TTS进度**
   - 已完成TTS: 显示已生成音频的数量
   - 待处理: 显示等待TTS处理的数量

4. **发布状态**
   - 已发布: 显示已发布到生产环境的数量
   - 待发布: 显示准备发布的数量

### 操作说明
- **数据刷新**: 页面会自动刷新数据，也可手动刷新浏览器
- **趋势分析**: 观察各项指标的变化趋势，识别工作重点

## 📚 单词管理功能

### 单词列表查看

#### 1. 基础列表
- **访问路径**: 点击侧边栏"单词管理"
- **显示内容**: 单词、频率、优先级、审核状态、操作按钮
- **分页浏览**: 每页显示20个单词，支持翻页

#### 2. 搜索功能
```
搜索框位置: 页面顶部
支持搜索: 单词名称 (支持模糊匹配)
搜索示例: 输入 "Amy" 查找包含Amy的单词
```

#### 3. 筛选功能
- **频率筛选**: High / Medium / Low / Rare
- **状态筛选**: 待审核 / 已批准 / 已拒绝 / 翻译中
- **组合筛选**: 可同时使用多个筛选条件

### 单词详情查看

#### 1. 打开详情
- **操作方式**: 点击单词行的"👁 详情"按钮
- **界面特点**: 弹出对话框，宽度为屏幕的65%

#### 2. Tab式内容组织
```
┌─────────────────────────────────────────────────────────────┐
│  Amy [High] [⭐8.5] [pending_review]                        │
├─────────────────────────────────────────────────────────────┤
│  [定义内容] [例句内容] [内容审核]                            │
├─────────────────────────────────────────────────────────────┤
│                     内容显示区域                             │
│                   (支持滚动查看)                             │
└─────────────────────────────────────────────────────────────┘
```

#### 3. 定义内容Tab
- **显示内容**: 格式化的JSON定义内容
- **内容特点**: 包含词性、定义、用法说明等
- **滚动支持**: 长内容支持独立滚动

#### 4. 例句内容Tab
- **显示内容**: 格式化的JSON例句内容
- **内容特点**: 包含例句、翻译、发音等
- **加载状态**: 显示加载进度和错误信息

## ✅ 审核工作流

### 审核界面使用

#### 1. 进入审核模式
- **方式一**: 在单词详情对话框中点击"内容审核"Tab
- **方式二**: 直接在列表中使用快捷审核按钮

#### 2. 分别审核功能
审核Tab分为两个独立区域：

##### 定义内容审核
```
┌─────────────────────────────────────────┐
│  定义内容审核                [当前状态]  │
├─────────────────────────────────────────┤
│  [✓ 批准定义] [✗ 拒绝定义]              │
│                                         │
│  审核评论:                              │
│  ┌─────────────────────────────────────┐ │
│  │ 请输入对定义内容的审核意见...        │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  [提交定义审核]                         │
└─────────────────────────────────────────┘
```

##### 例句内容审核
```
┌─────────────────────────────────────────┐
│  例句内容审核                [当前状态]  │
├─────────────────────────────────────────┤
│  [✓ 批准例句] [✗ 拒绝例句]              │
│                                         │
│  审核评论:                              │
│  ┌─────────────────────────────────────┐ │
│  │ 请输入对例句内容的审核意见...        │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  [提交例句审核]                         │
└─────────────────────────────────────────┘
```

### 审核操作步骤

#### 1. 内容审核流程
1. **查看内容**: 在"定义内容"和"例句内容"Tab中查看具体内容
2. **切换到审核**: 点击"内容审核"Tab
3. **选择决策**: 点击"批准"或"拒绝"按钮
4. **添加评论**: 在评论框中输入审核意见 (可选)
5. **提交审核**: 点击"提交审核"按钮

#### 2. 快捷审核 (列表页面)
- **批准**: 直接点击"✓ 批准"按钮
- **拒绝**: 直接点击"✗ 拒绝"按钮
- **查看详情**: 点击"👁 详情"按钮进入详细审核

#### 3. 审核状态说明
- **pending_review**: 待审核 (黄色标签)
- **approved**: 已批准 (绿色标签)
- **rejected**: 已拒绝 (红色标签)
- **in_translation**: 翻译中 (蓝色标签)

### 审核质量标准

#### 1. 定义内容审核标准
- **准确性**: 定义是否准确反映单词含义
- **完整性**: 是否包含主要词性和用法
- **语言质量**: 中文翻译是否自然流畅
- **格式规范**: JSON格式是否正确

#### 2. 例句内容审核标准
- **相关性**: 例句是否恰当使用了目标单词
- **自然性**: 例句是否符合英语表达习惯
- **翻译质量**: 中文翻译是否准确自然
- **教学价值**: 是否有助于学习者理解单词用法

## 🔍 高效使用技巧

### 1. 快捷操作
- **键盘导航**: 使用Tab键在界面元素间快速切换
- **批量筛选**: 先筛选出目标内容，再逐个审核
- **状态跟踪**: 利用状态筛选查看审核进度

### 2. 工作流程建议
1. **每日开始**: 查看统计面板了解整体进度
2. **优先处理**: 先处理High频率的单词
3. **批量审核**: 使用筛选功能批量处理相似内容
4. **质量检查**: 定期回顾已审核内容的质量

### 3. 常见问题处理
- **内容加载慢**: 刷新页面或检查网络连接
- **审核失败**: 重试操作或联系技术支持
- **界面异常**: 清除浏览器缓存后重新访问

## 📋 最佳实践

### 1. 审核效率提升
- **专注时段**: 选择注意力集中的时间进行审核
- **分类处理**: 按频率或主题分类处理单词
- **休息间隔**: 每审核50-100个单词后适当休息

### 2. 质量保证
- **双重检查**: 重要内容进行二次确认
- **标准统一**: 团队成员保持审核标准一致
- **问题记录**: 记录常见问题和解决方案

### 3. 团队协作
- **进度同步**: 定期同步审核进度和问题
- **经验分享**: 分享审核经验和技巧
- **质量讨论**: 对有争议的内容进行团队讨论

## 🆘 常见问题解答

### Q1: 如何快速找到特定单词？
**A**: 使用页面顶部的搜索框，输入单词名称进行搜索。

### Q2: 审核后如何撤销操作？
**A**: 目前系统不支持撤销，如需修改请重新进行审核操作。

### Q3: 为什么有些例句显示"暂无数据"？
**A**: 部分单词可能还没有生成例句内容，这是正常现象。

### Q4: 如何提高审核效率？
**A**: 建议使用筛选功能批量处理相同类型的内容，并熟练使用快捷审核按钮。

### Q5: 系统支持多人同时使用吗？
**A**: 是的，系统支持多用户同时操作，数据会实时同步。

## 📞 技术支持

### 联系方式
- **技术支持**: 通过项目管理系统提交工单
- **使用问题**: 联系内容团队负责人
- **系统故障**: 联系技术运维团队

### 支持时间
- **工作日**: 9:00 - 18:00
- **响应时间**: 一般问题24小时内响应，紧急问题2小时内响应

---

**手册版本**: v1.0  
**最后更新**: 2025-01-07  
**适用版本**: SenseWord ContentHub v1.0  
**下次更新**: 根据用户反馈和功能更新进行修订
