# KDD-035 技术方案蓝图：文本计算音频资产请求ID系统 - 数据库迁移方案

## 0. 依赖关系与影响分析
- [重用] `words_for_publish` 表：现有表结构将被扩展，新增TTS相关字段
- [新增] `tts_assets` 表：全新的TTS资产管理表，实现智能复用机制
- [修改] 现有contentJson结构：从v1.0的纯文本模式升级到v4.0的文本哈希映射模式
- [影响] 55,703条现有单词记录需要完整迁移和TTS ID绑定

## 1. 项目文件结构概览 (Project File Structure Overview)
```
senseword-content-factory/01-EN/SQLite/workflows/05-例句资产 TTS /scripts/
├── 02_create_v4_database_structure.py    # [新增] 创建v4.0数据库表结构
├── 03_migrate_v2_to_v4_content.py        # [新增] 迁移v1.0内容到v4.0架构
├── 04_validate_migration_integrity.py    # [新增] 验证迁移完整性和数据一致性
├── 05_rollback_migration.py              # [新增] 迁移回滚脚本
└── utils/
    ├── text_normalizer.py                # [新增] 多语言文本标准化工具
    ├── tts_mapper.py                     # [新增] TTS映射生成器
    ├── hash_generator.py                 # [新增] 文本哈希生成工具
    └── migration_validator.py            # [新增] 迁移验证工具
```

## 2. 分支策略建议

- 建议的特性分支名称: `feature/tts-id-database-migration`
- 建议的 git worktree 文件路径：/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/03-tts-migration-workspace
- 基础分支: `main`
- 分支创建模拟命令行:
    ```bash
    # 概念性命令，用于记录和指导
    # git checkout main
    # git pull origin main
    # git worktree add /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/03-tts-migration-workspace -b feature/tts-id-database-migration main
    ```

## 3. Commit 规划概要 (Commit Plan Summary & Status)
- [ ] feat(db): create v4.0 database schema with tts_assets table
- [ ] feat(utils): implement text normalization and hash generation utilities
- [ ] feat(migration): implement content migration from v1.0 to v4.0
- [ ] feat(tts): implement TTS ID binding and mapping generation
- [ ] feat(validation): implement migration integrity validation
- [ ] feat(rollback): implement migration rollback mechanism
- [ ] test(migration): comprehensive migration testing and validation
- [ ] docs(migration): migration process documentation and reports

## 4. 技术方案蓝图

### 4.1 现有数据库结构分析

#### 当前 words_for_publish 表结构
```sql
CREATE TABLE IF NOT EXISTS "words_for_publish" (
    id INTEGER PRIMARY KEY,
    word TEXT NOT NULL,
    learningLanguage TEXT NOT NULL DEFAULT 'en',
    scaffoldingLanguage TEXT NOT NULL DEFAULT 'zh',
    contentJson TEXT NOT NULL,
    publishStatus TEXT NOT NULL DEFAULT 'pending_upload',
    contentVersion TEXT DEFAULT 'v1.0',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    partsOfSpeech TEXT,
    ttsStatus TEXT DEFAULT 'pending',
    aiAuditScore REAL,
    aiAuditShouldRegenerate INTEGER,
    aiAuditComment TEXT,
    frequency TEXT,
    auditStatus TEXT DEFAULT 'pending_review',
    aiAuditShouldRemove INTEGER DEFAULT 0,
    culturalRiskRegions TEXT DEFAULT '[]'
);
```

#### 现有数据统计
- **总记录数**: 55,703条单词记录
- **ContentJson版本**: v1.0（纯文本结构，无TTS ID）
- **TTS状态**: 全部为'pending'状态
- **平均ContentJson大小**: ~4.9KB

#### 现有ContentJson结构示例
```json
{
  "word": "hello",
  "content": {
    "phoneticSymbols": [
      {
        "type": "BrE",
        "symbol": "/həˈləʊ/"
        // 缺少 ttsId 字段
      }
    ],
    "usageExamples": [
      {
        "examples": [
          {
            "learningLanguage": "Hello, how are you?",
            "translation": "你好，你好吗？"
            // 缺少 ttsId 字段
          }
        ]
      }
    ]
  }
}
```

### 4.2 目标数据库结构设计

#### 扩展后的 words_for_publish 表
```sql
-- 在现有表基础上新增TTS相关字段
ALTER TABLE words_for_publish ADD COLUMN ttsHashList TEXT;           -- JSON数组：["hash1", "hash2"]
ALTER TABLE words_for_publish ADD COLUMN audioGenerated INTEGER DEFAULT 0;  -- 音频生成状态
```

#### 新增 tts_assets 表
```sql
CREATE TABLE tts_assets (
    ttsId TEXT PRIMARY KEY,                 -- 使用哈希作为主键

    -- 文本信息（支持调试和复用）
    originalText TEXT NOT NULL,             -- 原始文本
    normalizedText TEXT NOT NULL,           -- 标准化文本
    textHash TEXT NOT NULL,                 -- 文本哈希（与ttsId相同）

    -- TTS处理信息
    textToSpeak TEXT NOT NULL,              -- 实际用于TTS的文本
    learningLanguage TEXT NOT NULL,         -- 学习语言
    ttsType TEXT NOT NULL,                  -- TTS类型：phonetic_bre, phonetic_name, example_sentence, phrase_breakdown

    -- 音频资产信息
    status TEXT DEFAULT 'pending',          -- pending, processing, completed, failed
    audioUrl TEXT,                          -- 音频文件URL
    audioDuration REAL,                     -- 音频时长（秒）

    -- 时间戳
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,

    -- 索引优化
    INDEX idx_textHash (textHash),
    INDEX idx_languageHash (learningLanguage, textHash),
    INDEX idx_status (status),
    INDEX idx_ttsType (ttsType)
);
```

### 4.3 ContentJson v4.0 目标结构

#### 升级后的ContentJson结构
```json
{
  "word": "hello",
  "content": {
    "phoneticSymbols": [
      {
        "type": "BrE",
        "symbol": "/həˈləʊ/",
        "ttsId": "08107702392dde07a1b2c3d4"  // 新增：TTS资产ID（24位）
      }
    ],
    "usageExamples": [
      {
        "examples": [
          {
            "learningLanguage": "Hello, how are you?",
            "translation": "你好，你好吗？",
            "ttsId": "a1b2c3d4e5f6g7h89f8e7d6c",  // 新增：TTS资产ID（24位）
            "phraseBreakdown": [
              {
                "phrase": "Hello,",
                "translation": "你好，",
                "ttsId": "9f8e7d6c5b4a39285c4b3a29"  // 新增：TTS资产ID（24位）
              }
            ]
          }
        ]
      }
    ]
  }
}
```

## `senseword-content-factory/01-EN/SQLite/workflows/05-例句资产 TTS /scripts/02_create_v4_database_structure.py`

### 核心职责 (Responsibilities):
- 创建v4.0数据库表结构，包括新增tts_assets表和扩展words_for_publish表
- 建立必要的索引和约束，确保数据完整性和查询性能
- 提供数据库结构验证和回滚机制

### 技术需求定义 (Technical Requirements):
- [数据完整性] 必须确保所有外键约束和唯一性约束正确建立
- [性能优化] 为高频查询字段建立合适的索引
- [事务安全] 所有DDL操作必须在事务中执行，支持回滚
- [日志记录] 详细记录所有数据库结构变更操作

### 函数/方法签名 (Function/Method Signatures):
- `def create_tts_assets_table(conn: sqlite3.Connection) -> bool`
- `def extend_words_for_publish_table(conn: sqlite3.Connection) -> bool`
- `def create_indexes_and_constraints(conn: sqlite3.Connection) -> bool`
- `def validate_database_structure(conn: sqlite3.Connection) -> bool`

### 数据结构定义 (Data Structures / DTOs):
```python
@dataclass
class DatabaseStructureConfig:
    """数据库结构配置"""
    tts_assets_table_sql: str
    words_table_extensions: List[str]
    indexes: List[str]
    constraints: List[str]

@dataclass
class MigrationResult:
    """迁移结果"""
    success: bool
    tables_created: List[str]
    indexes_created: List[str]
    error_message: Optional[str]
    execution_time: float
```

### 伪代码实现逻辑 (Pseudocode Implementation Logic):
1. [连接验证] 验证数据库连接和权限
2. [备份创建] 创建数据库结构备份
3. [事务开始] 开始数据库事务
4. [表结构创建] 创建tts_assets表
5. [表结构扩展] 为words_for_publish表添加新字段
6. [索引创建] 创建性能优化索引
7. [约束添加] 添加数据完整性约束
8. [结构验证] 验证新结构的正确性
9. [事务提交] 提交所有变更

## `senseword-content-factory/01-EN/SQLite/workflows/05-例句资产 TTS /scripts/utils/text_normalizer.py`

### 核心职责 (Responsibilities):
- 实现多语言文本标准化算法，确保TTS文本的一致性
- 提供文本清理、格式化和标准化功能
- 支持不同TTS类型的文本预处理

### 技术需求定义 (Technical Requirements):
- [多语言支持] 支持英语、中文、日语等多种语言的文本标准化
- [性能优化] 批量处理时保持高性能，支持55,703条记录的快速处理
- [一致性保证] 相同输入必须产生相同的标准化结果
- [错误处理] 对异常文本提供优雅的降级处理

### 函数/方法签名 (Function/Method Signatures):
- `def normalize_text_for_tts(text: str, language: str, tts_type: str) -> NormalizedText`
- `def clean_phonetic_symbols(symbol: str) -> str`
- `def standardize_example_sentence(sentence: str, language: str) -> str`
- `def generate_tts_context_key(text: str, word: str, context_type: str) -> str`

### 数据结构定义 (Data Structures / DTOs):
```python
@dataclass
class NormalizedText:
    """标准化文本结果"""
    original: str
    normalized: str
    tts_ready: str
    language: str
    tts_type: str
    context_key: str

class TTSTextType(Enum):
    """TTS文本类型"""
    PHONETIC_BRE = "phonetic_bre"
    PHONETIC_NAME = "phonetic_name"
    EXAMPLE_SENTENCE = "example_sentence"
    PHRASE_BREAKDOWN = "phrase_breakdown"
```

### 伪代码实现逻辑 (Pseudocode Implementation Logic):
1. [输入验证] 验证文本和语言参数的有效性
2. [基础清理] 移除多余空格、特殊字符和控制字符
3. [语言特定处理] 根据语言类型应用特定的标准化规则
4. [TTS类型处理] 根据TTS类型进行专门的文本预处理
5. [格式标准化] 生成统一的标准化格式
6. [上下文生成] 生成TTS上下文标识键
7. [结果验证] 验证标准化结果的有效性

## `senseword-content-factory/01-EN/SQLite/workflows/05-例句资产 TTS /scripts/utils/hash_generator.py`

### 核心职责 (Responsibilities):
- 实现文本哈希生成算法，为TTS资产创建唯一标识符
- 提供哈希冲突检测和处理机制
- 确保哈希生成的一致性和可重现性

### 技术需求定义 (Technical Requirements):
- [唯一性保证] 使用SHA256算法确保哈希的唯一性
- [性能优化] 支持批量哈希生成，处理大量文本时保持高性能
- [冲突处理] 提供哈希冲突检测和解决机制
- [可重现性] 相同输入必须产生相同的哈希值

### 函数/方法签名 (Function/Method Signatures):
- `def generate_text_hash(normalized_text: str, language: str) -> str`
- `def batch_generate_hashes(texts: List[NormalizedText]) -> Dict[str, str]`
- `def detect_hash_collision(hash_value: str, existing_hashes: Set[str]) -> bool`
- `def resolve_hash_collision(original_hash: str, collision_count: int) -> str`

### 数据结构定义 (Data Structures / DTOs):
```python
@dataclass
class HashResult:
    """哈希生成结果"""
    original_text: str
    normalized_text: str
    hash_value: str
    algorithm: str
    collision_resolved: bool
    generation_time: datetime

class HashCollisionStrategy(Enum):
    """哈希冲突处理策略"""
    APPEND_COUNTER = "append_counter"
    REGENERATE_WITH_SALT = "regenerate_with_salt"
    USE_LONGER_HASH = "use_longer_hash"
```

### 伪代码实现逻辑 (Pseudocode Implementation Logic):
1. [文本预处理] 对输入文本进行UTF-8编码处理
2. [哈希计算] 使用SHA256算法计算完整哈希值
3. [哈希截取] 取前24位作为TTS ID（考虑多语言对和链接ID使用场景）
4. [冲突检测] 检查生成的哈希是否与现有哈希冲突
5. [冲突解决] 如有冲突，应用冲突解决策略
6. [结果验证] 验证最终哈希的有效性和唯一性

## `senseword-content-factory/01-EN/SQLite/workflows/05-例句资产 TTS /scripts/03_migrate_v2_to_v4_content.py`

### 核心职责 (Responsibilities):
- 执行从v1.0到v4.0的完整内容迁移，处理55,703条单词记录
- 提取ContentJson中的所有TTS文本，生成对应的TTS资产记录
- 更新ContentJson结构，添加ttsId字段并保持数据完整性

### 技术需求定义 (Technical Requirements):
- [批量处理] 支持大规模数据迁移，采用批次处理策略（500条/批次）
- [数据完整性] 确保迁移过程中不丢失任何原始数据
- [事务安全] 使用数据库事务确保迁移的原子性
- [进度监控] 提供详细的迁移进度和状态报告
- [错误恢复] 支持断点续传和错误恢复机制

### 函数/方法签名 (Function/Method Signatures):
- `def migrate_single_word(word_record: WordRecord) -> MigrationResult`
- `def extract_tts_content_from_json(content_json: dict, word: str) -> List[TTSContent]`
- `def create_tts_assets_batch(tts_contents: List[TTSContent]) -> List[TTSAsset]`
- `def update_content_json_with_tts_ids(content_json: dict, tts_id_mappings: Dict[str, str]) -> dict`
- `def process_migration_batch(batch_records: List[WordRecord]) -> BatchMigrationResult`

### 数据结构定义 (Data Structures / DTOs):
```python
@dataclass
class WordRecord:
    """单词记录"""
    id: int
    word: str
    learning_language: str
    scaffolding_language: str
    content_json: str
    content_version: str

@dataclass
class TTSContent:
    """TTS内容"""
    original_text: str
    normalized_text: str
    text_to_speak: str
    tts_type: str
    context_path: str
    word_id: int
    language: str

@dataclass
class TTSAsset:
    """TTS资产"""
    tts_id: str
    original_text: str
    normalized_text: str
    text_hash: str
    text_to_speak: str
    learning_language: str
    tts_type: str
    status: str




@dataclass
class BatchMigrationResult:
    """批次迁移结果"""
    batch_id: int
    processed_count: int
    success_count: int
    error_count: int
    tts_assets_created: int

    errors: List[str]
    processing_time: float
```

### 伪代码实现逻辑 (Pseudocode Implementation Logic):
1. [初始化] 连接数据库，初始化迁移状态
2. [批次划分] 将55,703条记录分成批次（500条/批次）
3. [批次处理] 对每个批次执行以下操作：
   - [记录读取] 读取批次内的所有单词记录
   - [内容提取] 从ContentJson中提取所有TTS文本内容
   - [文本标准化] 对提取的文本进行标准化处理
   - [哈希生成] 为每个文本生成唯一的TTS ID
   - [资产创建] 创建或复用TTS资产记录
   - [JSON更新] 更新ContentJson，添加ttsId字段
   - [数据库更新] 批量更新数据库记录
4. [进度报告] 记录迁移进度和统计信息
5. [完整性验证] 验证迁移结果的完整性

## `senseword-content-factory/01-EN/SQLite/workflows/05-例句资产 TTS /scripts/04_validate_migration_integrity.py`

### 核心职责 (Responsibilities):
- 验证迁移完成后的数据完整性和一致性
- 检查TTS ID的唯一性和映射关系的正确性
- 生成详细的验证报告和统计信息

### 技术需求定义 (Technical Requirements):
- [全面验证] 检查所有迁移数据的完整性和正确性
- [性能优化] 高效执行大规模数据验证
- [详细报告] 生成包含统计信息和错误详情的验证报告
- [修复建议] 对发现的问题提供修复建议

### 函数/方法签名 (Function/Method Signatures):
- `def validate_tts_assets_integrity() -> ValidationResult`
- `def validate_content_json_tts_ids() -> ValidationResult`
- `def validate_tts_id_uniqueness() -> ValidationResult`
- `def validate_mapping_consistency() -> ValidationResult`
- `def generate_migration_report() -> MigrationReport`

### 数据结构定义 (Data Structures / DTOs):
```python
@dataclass
class ValidationResult:
    """验证结果"""
    check_name: str
    passed: bool
    total_checked: int
    errors_found: int
    warnings_found: int
    error_details: List[str]
    warning_details: List[str]

@dataclass
class MigrationReport:
    """迁移报告"""
    migration_date: datetime
    total_words_processed: int
    total_tts_assets_created: int

    validation_results: List[ValidationResult]
    performance_metrics: Dict[str, float]
    recommendations: List[str]
```

### 伪代码实现逻辑 (Pseudocode Implementation Logic):
1. [TTS资产验证] 检查tts_assets表中所有记录的完整性
2. [ID唯一性验证] 验证所有TTS ID的唯一性
3. [映射一致性验证] 检查ContentJson中ttsId与tts_assets表的一致性
4. [数据完整性验证] 验证迁移前后的数据总量一致性
5. [性能指标计算] 计算复用率、处理速度等关键指标
6. [报告生成] 生成详细的验证报告和建议

## 5. AI Agent 需要了解的文件上下文

<context_files>
/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/senseword-content-factory/01-EN/SQLite/senseword_content.db
/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/senseword-content-factory/01-EN/SQLite/workflows/05-例句资产 TTS /scripts/01_calculate_tts_cost.py
/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/senseword-content-factory/01-EN/SQLite/workflows/05-例句资产 TTS /reports/TTS-ID绑定流程完整解析.md
</context_files>

## 6. 冲突检查报告

### 6.1 数据库结构验证 ✅

#### 现有数据库确认
- **✅ words_for_publish表**: 已存在，包含55,703条记录，结构完整
- **✅ 数据完整性**: ContentJson字段包含完整的音标、例句、短语数据
- **✅ 版本兼容性**: 当前v1.0结构可平滑升级到v4.0

#### 新增表结构需求
- **⚠️ tts_assets表**: 需要新增，技术方案中已定义完整结构
- **⚠️ 表字段扩展**: words_for_publish表需要新增3个TTS相关字段

### 6.2 数据迁移验证 ✅

#### 数据量评估
- **✅ 处理规模**: 55,703条记录，平均ContentJson大小4.9KB
- **✅ 预估TTS资产**: 基于文档分析，预计生成约100万个TTS资产


#### 迁移策略可行性
- **✅ 批次处理**: 500条/批次策略适合大规模数据处理
- **✅ 事务安全**: SQLite支持事务，确保数据一致性
- **✅ 性能预期**: 基于文档，预期处理速度203个单词/秒

### 6.3 技术栈兼容性验证 ✅

#### Python环境
- **✅ SQLite3**: Python内置支持，无需额外依赖
- **✅ JSON处理**: 标准库支持，处理ContentJson结构
- **✅ 哈希算法**: hashlib库支持SHA256算法

#### 文件系统
- **✅ 脚本目录**: 目标目录已存在，权限正常
- **✅ 数据库访问**: 数据库文件可读写，无权限问题

### 6.4 潜在风险识别 ⚠️

#### 中等风险项
1. **数据迁移时间**: 55,703条记录的完整迁移可能需要4-6小时，建议分批执行
2. **磁盘空间**: 新增tts_assets表可能增加数据库大小至原来的3-4倍，需确保足够空间
3. **内存使用**: 批量处理时内存使用可能较高，建议监控内存使用情况

#### 低风险项
1. **哈希冲突**: SHA256前24位（96位十六进制）冲突概率分析：
   - 哈希空间：16^24 = 79,228,162,514,264,337,593,543,950,336 (约7922万万亿万亿)
   - 根据生日悖论，50%冲突概率需要约√(16^24) ≈ 281万亿个哈希
   - 对于几千万个TTS资产，冲突概率几乎为0
   - **预期冲突频率**: 约每281万亿个哈希ID产生一次冲突，完全满足多语言对扩展需求
   - **链接ID安全性**: 24位哈希提供足够的安全性，防止链接被猜测或枚举
2. **JSON解析**: 现有ContentJson结构规范，解析失败概率低

### 6.5 修正建议 📝

#### 迁移优化建议
1. **分阶段执行**: 建议分3个阶段执行迁移，每阶段处理约18,000条记录
2. **监控机制**: 添加实时监控和进度报告，便于跟踪迁移状态
3. **备份策略**: 迁移前创建完整数据库备份，确保可回滚

#### 性能优化建议
1. **索引策略**: 迁移完成后再创建索引，提升插入性能
2. **批次大小**: 根据实际性能测试调整批次大小（建议300-800条）
3. **并发处理**: 考虑使用多进程处理不同批次，提升整体速度

### 6.6 结论 ✅

**技术方案与现有项目实现高度兼容**，主要发现：

1. **✅ 数据库结构**: 现有结构完整，支持平滑升级到v4.0
2. **✅ 数据完整性**: 55,703条记录数据完整，ContentJson结构规范
3. **✅ 技术可行性**: Python + SQLite技术栈成熟稳定，迁移方案可行
4. **⚠️ 性能考虑**: 大规模数据迁移需要合理的批次策略和监控机制
5. **📈 预期效果**: 迁移完成后将建立完整的TTS资产管理体系，支持智能复用

**建议继续技术方案实施**，按照分阶段迁移策略执行，重点关注性能监控和数据完整性验证。

## 7. 迁移执行计划

### 7.1 迁移阶段划分
- **阶段一**: 数据库结构创建（预计30分钟）
- **阶段二**: 数据迁移执行（预计4-6小时，分3个子阶段）
- **阶段三**: 数据验证和优化（预计1小时）

### 7.2 关键里程碑
1. **结构创建完成**: tts_assets表创建，words_for_publish表扩展
2. **第一批次迁移**: 验证迁移流程和性能指标
3. **50%进度达成**: 约27,000条记录迁移完成
4. **迁移完成**: 全部55,703条记录迁移完成
5. **验证通过**: 数据完整性验证100%通过

### 7.3 成功标准
- **数据完整性**: 100%记录成功迁移，无数据丢失
- **TTS资产创建**: 预计100万+TTS资产成功创建

- **性能达标**: 平均处理速度150+单词/秒
- **验证通过**: 所有完整性检查100%通过