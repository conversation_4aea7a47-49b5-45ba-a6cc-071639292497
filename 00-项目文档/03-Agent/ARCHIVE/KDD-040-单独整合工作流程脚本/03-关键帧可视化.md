# SenseWord 工作流程脚本整合 - 完整可视化指南

## 1. 整体架构转换流程图

### 1.1 当前状态 → 目标状态架构转换

```mermaid
graph TB
    subgraph "🔴 当前状态 - 分散的脚本架构"
        A1["📁 Google_Cloud_Platform/py_scripts/"]
        A2["📁 01-EN/V1/1_Batch_task_queue/scripts/"]
        A3["📁 01-EN/V1/3_automation_scripts/"]
        A4["📁 01-EN/SQLite/workflows/"]
        A5["📁 Prompts/"]

        A1 --> A6["⚙️ 批处理任务管理"]
        A2 --> A7["📝 内容生成处理"]
        A3 --> A8["🔍 单词提取过滤"]
        A4 --> A9["🗄️ 数据库工作流程"]
        A5 --> A10["🤖 AI提示词管理"]
    end

    A6 -.-> B1
    A7 -.-> B2
    A8 -.-> B3
    A9 -.-> B4
    A10 -.-> B5

    subgraph "🟢 目标状态 - 统一工作流程架构"
        B1["📦 workflow/03-AI批处理筛选/"]
        B2["📦 workflow/05-内容生成/"]
        B3["📦 workflow/01-单词提取/ + workflow/02-文本过滤/"]
        B4["📦 workflow/04-数据库初始化/ + workflow/09-状态管理/"]
        B5["📦 各阶段/prompts/目录"]

        B6["🎯 workflow/orchestrator/"]
        B7["🛠️ workflow/utils/"]
        B8["⚙️ workflow/config/"]

        B1 --> B6
        B2 --> B6
        B3 --> B6
        B4 --> B6
        B5 --> B6

        B6 --> B9["🎭 统一编排管理"]
        B7 --> B10["🔧 公共工具支持"]
        B8 --> B11["📋 配置驱动管理"]
    end

    classDef currentState fill:#ffcccb,stroke:#000000,stroke-width:2px,color:#000000
    classDef targetState fill:#d4edda,stroke:#000000,stroke-width:2px,color:#000000
    classDef coreSystem fill:#cce5ff,stroke:#000000,stroke-width:3px,color:#000000

    class A1,A2,A3,A4,A5,A6,A7,A8,A9,A10 currentState
    class B1,B2,B3,B4,B5 targetState
    class B6,B7,B8,B9,B10,B11 coreSystem
```

### 1.2 架构转换关键优势

**🔴 当前状态问题：**
- 脚本分散在多个不同目录中，难以统一管理
- 缺乏统一的协调机制和依赖管理
- 配置信息硬编码，环境切换困难
- 缺乏标准化的错误处理和监控

**🟢 目标状态优势：**
- 按功能阶段统一组织，清晰的目录结构
- 引入编排器实现智能调度和依赖管理
- 配置驱动的参数化管理，支持多环境
- 完整的监控、错误恢复和断点续传体系

## 2. 完整数据流转换图

### 2.1 9阶段数据流关键帧变化

```mermaid
graph LR
    subgraph "📊 数据流关键帧变化"
        A["📚 原始词典数据<br/>dictionary.txt"] --> B["🔍 01-单词提取"]
        B --> C["📝 提取的单词列表<br/>words_list.txt"]
        C --> D["🔧 02-文本过滤"]
        D --> E["✅ 过滤后单词列表<br/>filtered_words.txt"]
        E --> F["🤖 03-AI批处理筛选"]
        F --> G["📋 筛选结果JSONL<br/>filtered_results.jsonl"]
        G --> H["🗄️ 04-数据库初始化"]
        H --> I["💾 预生产单词条目<br/>word_queue_table"]
        I --> J["📝 05-内容生成"]
        J --> K["📄 生成的内容JSON<br/>content_generated.json"]
        K --> L["👁️ 06-内容审核"]
        L --> M["✨ 审核修复后内容<br/>content_reviewed.json"]
        M --> N["🔊 07-TTS标记"]
        N --> O["🎵 标记TTS ID的内容<br/>content_with_tts.json"]
        O --> P["🎧 08-音频生成"]
        P --> Q["🎶 完整音频资产<br/>audio_assets/"]
        Q --> R["📊 09-状态管理"]
        R --> S["🚀 发布就绪内容<br/>ready_for_publish"]
    end

    subgraph "🔄 关键数据结构变化"
        T1["📝 单词字符串列表<br/>['apple', 'banana', 'cherry']"]
        T2["⚙️ 过滤规则JSON<br/>{rules: {min_length: 3}}"]
        T3["🤖 AI筛选JSONL<br/>{word: 'apple', keep: 1, priority: 8}"]
        T4["🗄️ SQLite数据库<br/>word_processing_queue表"]
        T5["📄 内容生成JSON<br/>{word: 'apple', content: {...}}"]
        T6["✅ 审核指令JSON<br/>{edits: [{path: '...', value: '...'}]}"]
        T7["🎵 TTS映射JSON<br/>{ttsId: 'hash123', audioUrl: '...'}"]
        T8["🎧 音频文件+元数据<br/>audio_files/ + metadata.json"]
        T9["📊 看板状态表<br/>contentGenerated: true, ..."]
    end

    B -.-> T1
    D -.-> T2
    F -.-> T3
    H -.-> T4
    J -.-> T5
    L -.-> T6
    N -.-> T7
    P -.-> T8
    R -.-> T9

    classDef inputData fill:#e3f2fd,stroke:#000000,stroke-width:2px,color:#000000
    classDef processData fill:#f3e5f5,stroke:#000000,stroke-width:2px,color:#000000
    classDef outputData fill:#e8f5e8,stroke:#000000,stroke-width:2px,color:#000000
    classDef dataStructure fill:#fff3e0,stroke:#000000,stroke-width:2px,color:#000000

    class A inputData
    class C,E,G,I,K,M,O,Q processData
    class S outputData
    class T1,T2,T3,T4,T5,T6,T7,T8,T9 dataStructure
```

### 2.2 真实数据示例演示

**阶段1输出示例 (words_list.txt):**
```
apple
banana
sophisticated
algorithm
democracy
```

**阶段3输出示例 (filtered_results.jsonl):**
```json
{"id": 1, "word": "apple", "keep": 1, "priority": 8, "partsOfSpeech": ["noun"]}
{"id": 2, "word": "sophisticated", "keep": 1, "priority": 6, "partsOfSpeech": ["adjective"]}
{"id": 3, "word": "algorithm", "keep": 1, "priority": 5, "partsOfSpeech": ["noun"]}
```

**阶段5输出示例 (content_generated.json):**
```json
{
  "word": "apple",
  "content": {
    "coreDefinition": "A round fruit with red or green skin",
    "examples": [
      {"sentence": "I eat an apple every day", "translation": "我每天吃一个苹果"}
    ],
    "difficulty": "beginner"
  }
}
```

## 3. 工作流程编排器架构图

### 3.1 编排器核心组件架构

```mermaid
graph TB
    subgraph "🎯 编排器核心组件"
        A["🎭 main_pipeline.py<br/>主编排器"]
        B["🎪 stage_coordinator.py<br/>阶段协调器"]
        C["🔗 dependency_manager.py<br/>依赖管理器"]

        A --> D["⚙️ 流水线配置加载"]
        A --> E["📊 全局状态管理"]
        A --> F["🔄 错误恢复机制"]

        B --> G["🎯 阶段执行协调"]
        B --> H["⚡ 并行处理管理"]
        B --> I["📤 数据传递控制"]

        C --> J["✅ 依赖关系验证"]
        C --> K["🔍 前置条件检查"]
        C --> L["🚀 执行顺序优化"]
    end

    subgraph "📊 监控与恢复系统"
        M["👁️ pipeline_monitor.py<br/>流水线监控"]
        N["🚨 error_detector.py<br/>错误检测器"]
        O["💾 checkpoint_manager.py<br/>检查点管理"]

        M --> P["📈 实时状态监控"]
        N --> Q["⚠️ 异常检测告警"]
        O --> R["🔄 断点续传支持"]

        P --> S["📊 执行进度跟踪"]
        Q --> T["🔍 错误分类处理"]
        R --> U["📸 状态快照管理"]
    end

    subgraph "🛠️ 工具支持系统"
        V["🗄️ database_utils.py<br/>数据库工具"]
        W["🤖 vertex_ai_client.py<br/>AI批处理客户端"]
        X["📁 file_utils.py<br/>文件操作工具"]

        V --> Y["🔗 统一数据库接口"]
        W --> Z["☁️ AI批处理接口"]
        X --> AA["📋 文件操作标准化"]

        Y --> BB["🏊 连接池管理"]
        Z --> CC["🔄 混合架构保持"]
        AA --> DD["✅ 数据验证集成"]
    end

    A -.-> M
    B -.-> N
    C -.-> O

    classDef orchestrator fill:#fff3cd,stroke:#000000,stroke-width:3px,color:#000000
    classDef monitoring fill:#d1ecf1,stroke:#000000,stroke-width:2px,color:#000000
    classDef utilities fill:#d4edda,stroke:#000000,stroke-width:2px,color:#000000
    classDef functions fill:#f8d7da,stroke:#000000,stroke-width:1px,color:#000000

    class A,B,C orchestrator
    class M,N,O monitoring
    class V,W,X utilities
    class D,E,F,G,H,I,J,K,L,P,Q,R,S,T,U,Y,Z,AA,BB,CC,DD functions
```

### 3.2 编排器执行时序图

```mermaid
sequenceDiagram
    participant User as 👤 用户
    participant Main as 🎭 主编排器
    participant Coord as 🎪 阶段协调器
    participant Dep as 🔗 依赖管理器
    participant Monitor as 👁️ 监控器
    participant Stage as 📦 具体阶段

    User->>Main: 🚀 启动工作流程
    Main->>Monitor: 📊 初始化监控
    Main->>Dep: 🔍 检查全局依赖
    Dep-->>Main: ✅ 依赖验证通过

    loop 每个阶段执行
        Main->>Coord: 🎯 执行阶段N
        Coord->>Dep: 🔍 检查阶段依赖
        Dep-->>Coord: ✅ 前置条件满足
        Coord->>Stage: ▶️ 启动阶段执行
        Stage->>Monitor: 📈 报告执行状态

        alt 执行成功
            Stage-->>Coord: ✅ 阶段完成
            Coord->>Monitor: 💾 创建检查点
            Coord-->>Main: ✅ 阶段成功
        else 执行失败
            Stage-->>Coord: ❌ 阶段失败
            Coord->>Monitor: 🚨 记录错误
            Coord-->>Main: ❌ 阶段失败
            Main->>Monitor: 🔄 启动错误恢复
        end
    end

    Main-->>User: 🎉 工作流程完成
```

## 4. 配置管理架构图

### 4.1 配置层次结构

```mermaid
graph TB
    subgraph "🔴 当前配置状态"
        A1["💻 硬编码配置<br/>PROJECT_ID = 'magic-sw-465214'"]
        A2["📂 分散在各脚本中<br/>重复定义相同配置"]
        A3["🔧 难以统一管理<br/>修改需要改多个文件"]
        A4["🌍 环境切换困难<br/>手动修改脚本"]

        A1 --> A5["⚠️ 维护困难"]
        A2 --> A6["⚠️ 配置重复"]
        A3 --> A7["⚠️ 管理混乱"]
        A4 --> A8["⚠️ 部署复杂"]
    end

    A1 -.-> B1
    A2 -.-> B2
    A3 -.-> B3
    A4 -.-> B4

    subgraph "🟢 目标配置状态"
        B1["📄 配置文件驱动<br/>JSON/YAML配置"]
        B2["📚 分层配置管理<br/>继承和覆盖机制"]
        B3["🎯 统一配置中心<br/>集中管理所有配置"]
        B4["🔄 环境自动切换<br/>dev/prod自动识别"]

        B1 --> B5["📋 global_config.json"]
        B2 --> B6["🌍 environment_config.json"]
        B3 --> B7["⚙️ pipeline_config.json"]
        B4 --> B8["📦 阶段级配置文件"]

        B5 --> C1["🏢 项目基础信息"]
        B6 --> C2["🌐 环境特定配置"]
        B7 --> C3["🔧 流水线参数"]
        B8 --> C4["⚙️ 阶段执行参数"]
    end

    subgraph "📊 配置继承关系"
        D1["🌍 全局配置<br/>基础项目信息"] --> D2["🏠 环境配置<br/>dev/prod差异"]
        D2 --> D3["⚙️ 流水线配置<br/>执行策略参数"]
        D3 --> D4["📦 阶段配置<br/>具体执行参数"]

        D1 -.-> E1["🏢 project_id, location"]
        D2 -.-> E2["🔧 log_level, batch_size"]
        D3 -.-> E3["⏱️ timeout, retry_count"]
        D4 -.-> E4["📝 stage_specific_params"]
    end

    classDef currentProblems fill:#ffcccb,stroke:#000000,stroke-width:2px,color:#000000
    classDef targetSolutions fill:#d4edda,stroke:#000000,stroke-width:2px,color:#000000
    classDef configHierarchy fill:#e3f2fd,stroke:#000000,stroke-width:2px,color:#000000
    classDef configDetails fill:#fff3e0,stroke:#000000,stroke-width:1px,color:#000000

    class A1,A2,A3,A4,A5,A6,A7,A8 currentProblems
    class B1,B2,B3,B4,B5,B6,B7,B8,C1,C2,C3,C4 targetSolutions
    class D1,D2,D3,D4 configHierarchy
    class E1,E2,E3,E4 configDetails
```

### 4.2 配置文件真实示例

**全局配置 (global_config.json):**
```json
{
  "project": {
    "name": "senseword-content-factory",
    "version": "2.0.0",
    "description": "SenseWord内容生产工作流程"
  },
  "google_cloud": {
    "project_id": "magic-sw-465214",
    "project_number": "189763113180",
    "location": "us-central1",
    "bucket": "gs://senseword-batch-processing"
  },
  "database": {
    "default_path": "senseword-content-factory/01-EN/SQLite/senseword_content_v4.db",
    "backup_retention_days": 30
  }
}
```

**环境配置 (environment_config.json):**
```json
{
  "development": {
    "log_level": "DEBUG",
    "enable_monitoring": true,
    "batch_size": 10,
    "timeout_seconds": 300,
    "vertex_ai": {
      "model": "gemini-1.5-pro",
      "max_retries": 2
    }
  },
  "production": {
    "log_level": "INFO",
    "enable_monitoring": true,
    "batch_size": 100,
    "timeout_seconds": 1800,
    "vertex_ai": {
      "model": "gemini-1.5-pro",
      "max_retries": 5
    }
  }
}
```

## 5. 错误处理与恢复机制图

### 5.1 错误处理流程图

```mermaid
graph TB
    subgraph "🚨 错误检测与分类"
        A["⚠️ 错误发生"] --> B["🔍 错误检测器"]
        B --> C{"🤔 错误类型判断"}

        C -->|网络错误| D["🌐 网络异常<br/>重试机制"]
        C -->|数据错误| E["📊 数据异常<br/>数据修复"]
        C -->|配置错误| F["⚙️ 配置异常<br/>配置验证"]
        C -->|系统错误| G["💻 系统异常<br/>系统检查"]
    end

    subgraph "🔄 恢复策略执行"
        D --> H["🔁 自动重试<br/>指数退避"]
        E --> I["🛠️ 数据清理<br/>格式修复"]
        F --> J["📋 配置重载<br/>默认值回退"]
        G --> K["🔧 系统重启<br/>资源清理"]

        H --> L{"✅ 重试成功?"}
        I --> M{"✅ 修复成功?"}
        J --> N{"✅ 配置有效?"}
        K --> O{"✅ 系统正常?"}
    end

    subgraph "📊 状态管理与报告"
        L -->|是| P["✅ 继续执行"]
        L -->|否| Q["❌ 标记失败"]
        M -->|是| P
        M -->|否| Q
        N -->|是| P
        N -->|否| Q
        O -->|是| P
        O -->|否| Q

        P --> R["📈 更新状态"]
        Q --> S["📝 记录错误日志"]

        R --> T["🎯 继续下一阶段"]
        S --> U["🚨 发送告警通知"]
    end

    classDef errorDetection fill:#ffcccb,stroke:#000000,stroke-width:2px,color:#000000
    classDef recoveryStrategy fill:#fff3cd,stroke:#000000,stroke-width:2px,color:#000000
    classDef statusManagement fill:#d4edda,stroke:#000000,stroke-width:2px,color:#000000
    classDef decision fill:#e3f2fd,stroke:#000000,stroke-width:2px,color:#000000

    class A,B,C,D,E,F,G errorDetection
    class H,I,J,K recoveryStrategy
    class P,Q,R,S,T,U statusManagement
    class L,M,N,O decision
```

### 5.2 断点续传机制图

```mermaid
graph LR
    subgraph "💾 检查点创建"
        A["🎯 阶段开始"] --> B["📸 创建检查点"]
        B --> C["💾 保存状态快照"]
        C --> D["📝 记录执行位置"]
    end

    subgraph "⚠️ 异常发生"
        D --> E["❌ 执行失败"]
        E --> F["🔍 检测失败点"]
        F --> G["📊 分析失败原因"]
    end

    subgraph "🔄 恢复执行"
        G --> H["📂 查找最近检查点"]
        H --> I["🔄 恢复状态快照"]
        I --> J["📍 定位执行位置"]
        J --> K["▶️ 从断点继续"]
    end

    K --> L["✅ 执行成功"]
    L --> M["🎉 完成阶段"]

    classDef checkpoint fill:#e3f2fd,stroke:#000000,stroke-width:2px,color:#000000
    classDef failure fill:#ffcccb,stroke:#000000,stroke-width:2px,color:#000000
    classDef recovery fill:#d4edda,stroke:#000000,stroke-width:2px,color:#000000
    classDef success fill:#d1ecf1,stroke:#000000,stroke-width:2px,color:#000000

    class A,B,C,D checkpoint
    class E,F,G failure
    class H,I,J,K recovery
    class L,M success
```

## 6. 实施计划时序图

### 6.1 完整实施时序

```mermaid
gantt
    title 🚀 SenseWord 工作流程脚本整合实施计划
    dateFormat  YYYY-MM-DD
    section 📋 阶段1: 基础架构
    创建目录结构           :done, arch1, 2025-01-15, 2d
    实现公共工具类         :done, arch2, after arch1, 3d
    配置管理系统          :active, arch3, after arch2, 2d

    section 🔧 阶段2: 核心迁移
    迁移批处理脚本         :batch1, after arch3, 4d
    迁移数据库脚本         :batch2, after arch3, 3d
    迁移状态管理脚本       :batch3, after batch2, 2d

    section 🎯 阶段3: 编排器
    主编排器开发          :orch1, after batch1, 3d
    阶段协调器开发        :orch2, after orch1, 2d
    依赖管理器开发        :orch3, after orch2, 2d

    section 📊 阶段4: 监控系统
    监控系统开发          :mon1, after orch3, 3d
    错误恢复系统          :mon2, after mon1, 2d
    检查点系统           :mon3, after mon2, 2d

    section ✅ 阶段5: 测试验证
    单元测试             :test1, after mon3, 3d
    集成测试             :test2, after test1, 2d
    端到端测试           :test3, after test2, 2d

    section 📚 阶段6: 文档完善
    技术文档             :doc1, after test3, 2d
    用户手册             :doc2, after doc1, 1d
    迁移指南             :doc3, after doc2, 1d
```

### 6.2 关键里程碑

| 里程碑 | 时间节点 | 关键交付物 | 验收标准 |
|--------|----------|------------|----------|
| 🏗️ 基础架构完成 | Week 1 | 目录结构 + 公共工具 | 所有工具类单元测试通过 |
| 🔄 核心迁移完成 | Week 2 | 批处理/数据库脚本迁移 | 现有功能100%保持 |
| 🎯 编排器完成 | Week 3 | 完整编排系统 | 能够协调执行所有阶段 |
| 📊 监控系统完成 | Week 4 | 监控和恢复系统 | 错误检测和自动恢复 |
| ✅ 测试验证完成 | Week 5 | 完整测试套件 | 所有测试用例通过 |
| 📚 文档完善 | Week 6 | 完整文档体系 | 用户能够独立使用 |

## 7. 系统架构总览图

### 7.1 最终系统架构

```mermaid
graph TB
    subgraph "🎭 编排层 (Orchestration Layer)"
        A["🎯 主编排器<br/>main_pipeline.py"]
        B["🎪 阶段协调器<br/>stage_coordinator.py"]
        C["🔗 依赖管理器<br/>dependency_manager.py"]
    end

    subgraph "📦 执行层 (Execution Layer)"
        D["🔍 01-单词提取"]
        E["🔧 02-文本过滤"]
        F["🤖 03-AI批处理筛选"]
        G["🗄️ 04-数据库初始化"]
        H["📝 05-内容生成"]
        I["👁️ 06-内容审核"]
        J["🔊 07-TTS标记"]
        K["🎧 08-音频生成"]
        L["📊 09-状态管理"]
    end

    subgraph "🛠️ 工具层 (Utility Layer)"
        M["🗄️ database_utils"]
        N["🤖 vertex_ai_client"]
        O["📁 file_utils"]
        P["📝 logging_utils"]
        Q["✅ validation_utils"]
    end

    subgraph "📊 监控层 (Monitoring Layer)"
        R["👁️ pipeline_monitor"]
        S["🚨 error_detector"]
        T["💾 checkpoint_manager"]
        U["📈 performance_tracker"]
    end

    subgraph "⚙️ 配置层 (Configuration Layer)"
        V["🌍 global_config.json"]
        W["🏠 environment_config.json"]
        X["⚙️ pipeline_config.json"]
        Y["📦 stage_configs/"]
    end

    A --> B
    B --> C
    A --> D
    B --> E
    B --> F
    C --> G
    A --> H
    B --> I
    B --> J
    C --> K
    A --> L

    D --> M
    E --> N
    F --> O
    G --> P
    H --> Q

    A --> R
    B --> S
    C --> T
    A --> U

    A --> V
    B --> W
    C --> X
    D --> Y

    classDef orchestration fill:#fff3cd,stroke:#000000,stroke-width:3px,color:#000000
    classDef execution fill:#d4edda,stroke:#000000,stroke-width:2px,color:#000000
    classDef utilities fill:#e3f2fd,stroke:#000000,stroke-width:2px,color:#000000
    classDef monitoring fill:#d1ecf1,stroke:#000000,stroke-width:2px,color:#000000
    classDef configuration fill:#f8d7da,stroke:#000000,stroke-width:2px,color:#000000

    class A,B,C orchestration
    class D,E,F,G,H,I,J,K,L execution
    class M,N,O,P,Q utilities
    class R,S,T,U monitoring
    class V,W,X,Y configuration
```

## 8. 总结与下一步行动

### 8.1 整合方案核心价值

**🎯 统一管理：** 从分散的脚本管理转向统一的工作流程编排
**⚙️ 配置驱动：** 从硬编码配置转向灵活的配置文件管理
**📊 全面监控：** 从缺乏监控转向实时监控和智能恢复
**🔄 可扩展性：** 从固化流程转向可配置的灵活流程

### 8.2 立即行动项

1. **📋 创建基础目录结构** - 建立新的workflow目录架构
2. **🛠️ 实现公共工具类** - 开发database_utils, file_utils等核心工具
3. **🔄 迁移核心脚本** - 优先迁移AI批处理和数据库相关脚本
4. **🎯 开发编排器** - 实现主编排器和阶段协调机制

## 9. 多语言分层架构设计 🌍

### 9.1 语言分层架构图

```mermaid
graph TB
    subgraph "🛠️ 算法层 (Language-Agnostic Scripts)"
        A["📦 workflow/"]
        A --> A1["🔍 01-单词提取"]
        A --> A2["🔧 02-文本过滤"]
        A --> A3["🤖 03-AI批处理筛选"]
        A --> A4["🗄️ 04-数据库初始化"]
        A --> A5["📝 05-内容生成"]
        A --> A6["👁️ 06-内容审核"]
        A --> A7["🔊 07-TTS标记"]
        A --> A8["🎧 08-音频生成"]
        A --> A9["📊 09-状态管理"]
        A --> A10["🎯 orchestrator/"]
        A --> A11["🛠️ utils/"]
    end

    subgraph "📊 数据资产层 (Language-Specific Data)"
        B["📁 content-assets/"]

        B --> C["🇺🇸 en/ (学习语言：英语)"]
        C --> C1["🇨🇳 zh/ (教学语言：中文)"]
        C --> C2["🇪🇸 es/ (教学语言：西班牙语)"]
        C --> C3["🇳🇱 nl/ (教学语言：荷兰语)"]
        C --> C4["🇯🇵 ja/ (教学语言：日语)"]

        B --> D["🇪🇸 es/ (学习语言：西班牙语)"]
        D --> D1["🇨🇳 zh/ (中文教学)"]
        D --> D2["🇺🇸 en/ (英文教学)"]

        B --> E["🇫🇷 fr/ (学习语言：法语)"]
        E --> E1["🇨🇳 zh/ (中文教学)"]
        E --> E2["🇺🇸 en/ (英文教学)"]
    end

    subgraph "📂 数据阶段目录 (每个语言对下)"
        F["📁 01-extracted-words/"]
        G["📁 02-filtered-words/"]
        H["📁 03-ai-filtered/"]
        I["📁 04-database/"]
        J["📁 05-generated-content/"]
        K["📁 06-reviewed-content/"]
        L["📁 07-tts-marked/"]
        M["📁 08-audio-assets/"]
        N["📁 09-ready-to-publish/"]
    end

    A1 -.-> F
    A2 -.-> G
    A3 -.-> H
    A4 -.-> I
    A5 -.-> J
    A6 -.-> K
    A7 -.-> L
    A8 -.-> M
    A9 -.-> N

    C1 --> F
    C1 --> G
    C1 --> H
    C1 --> I
    C1 --> J
    C1 --> K
    C1 --> L
    C1 --> M
    C1 --> N

    classDef algorithms fill:#e3f2fd,stroke:#000000,stroke-width:3px,color:#000000
    classDef dataAssets fill:#f3e5f5,stroke:#000000,stroke-width:2px,color:#000000
    classDef learningLang fill:#d4edda,stroke:#000000,stroke-width:2px,color:#000000
    classDef teachingLang fill:#fff3cd,stroke:#000000,stroke-width:2px,color:#000000
    classDef stageData fill:#ffcccb,stroke:#000000,stroke-width:1px,color:#000000

    class A,A1,A2,A3,A4,A5,A6,A7,A8,A9,A10,A11 algorithms
    class B dataAssets
    class C,D,E learningLang
    class C1,C2,C3,C4,D1,D2,E1,E2 teachingLang
    class F,G,H,I,J,K,L,M,N stageData
```

### 9.2 多语言工作流程执行图

```mermaid
graph LR
    subgraph "🎯 执行参数"
        A["📝 学习语言: en<br/>教学语言: zh"]
        B["📝 学习语言: es<br/>教学语言: zh"]
        C["📝 学习语言: fr<br/>教学语言: en"]
    end

    subgraph "🛠️ 统一算法层"
        D["🎭 主编排器<br/>main_pipeline.py"]
        E["🎪 阶段协调器<br/>stage_coordinator.py"]
        F["🔗 依赖管理器<br/>dependency_manager.py"]
    end

    subgraph "📊 数据路径解析"
        G["📁 content-assets/en/zh/"]
        H["📁 content-assets/es/zh/"]
        I["📁 content-assets/fr/en/"]
    end

    subgraph "🔄 并行执行"
        J["🇺🇸→🇨🇳 英中内容生产"]
        K["🇪🇸→🇨🇳 西中内容生产"]
        L["🇫🇷→🇺🇸 法英内容生产"]
    end

    A --> D
    B --> D
    C --> D

    D --> E
    E --> F

    A -.-> G
    B -.-> H
    C -.-> I

    G --> J
    H --> K
    I --> L

    classDef params fill:#e3f2fd,stroke:#000000,stroke-width:2px,color:#000000
    classDef algorithms fill:#fff3cd,stroke:#000000,stroke-width:3px,color:#000000
    classDef paths fill:#f3e5f5,stroke:#000000,stroke-width:2px,color:#000000
    classDef execution fill:#d4edda,stroke:#000000,stroke-width:2px,color:#000000

    class A,B,C params
    class D,E,F algorithms
    class G,H,I paths
    class J,K,L execution
```

### 9.3 配置管理多语言支持

```mermaid
graph TB
    subgraph "🌍 多语言配置架构"
        A["⚙️ global_config.json<br/>全局基础配置"]
        B["🌐 language_config.json<br/>语言对配置"]
        C["📋 pipeline_config.json<br/>流水线配置"]
    end

    subgraph "📝 语言对配置示例"
        D["🇺🇸 learning: 'en'<br/>🇨🇳 teaching: 'zh'"]
        E["🇪🇸 learning: 'es'<br/>🇨🇳 teaching: 'zh'"]
        F["🇫🇷 learning: 'fr'<br/>🇺🇸 teaching: 'en'"]
    end

    subgraph "📂 动态路径生成"
        G["📁 content-assets/{learning}/{teaching}/"]
        H["🗄️ database: {learning}_{teaching}_content.db"]
        I["🎵 audio: {learning}_{teaching}_audio/"]
    end

    A --> B
    B --> C

    B --> D
    B --> E
    B --> F

    D --> G
    E --> G
    F --> G

    G --> H
    G --> I

    classDef config fill:#e3f2fd,stroke:#000000,stroke-width:3px,color:#000000
    classDef langPairs fill:#fff3cd,stroke:#000000,stroke-width:2px,color:#000000
    classDef paths fill:#d4edda,stroke:#000000,stroke-width:2px,color:#000000

    class A,B,C config
    class D,E,F langPairs
    class G,H,I paths
```

### 9.4 架构优势总结

**🎯 关注点分离：**
- **算法层**：语言无关的处理逻辑，一次开发，多语言复用
- **数据层**：按语言对组织，便于管理和扩展

**🚀 可扩展性：**
- 新增学习语言：只需在content-assets下创建目录
- 新增教学语言：在对应学习语言下创建子目录
- 新增语言对：配置文件添加支持即可

**🔧 维护性：**
- 脚本更新只影响workflow目录
- 数据迁移只影响content-assets目录
- 不同语言对可以独立管理和部署

**⚡ 并行处理：**
- 不同语言对可以并行处理
- 资源隔离，互不影响
- 支持分布式部署

这个多语言分层架构设计完美地解决了国际化扩展的需求，为SenseWord成为真正的全球化语言学习平台奠定了坚实的技术基础。