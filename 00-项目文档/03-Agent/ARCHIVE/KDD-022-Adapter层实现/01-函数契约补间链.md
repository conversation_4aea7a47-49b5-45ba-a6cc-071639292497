# KDD-018 iOS Adapter转译层技术方案

> **SenseWord iOS应用API转译层完整开发指南**
> 基于TADA架构设计，面向软件团队的可执行技术方案
> 生成时间: 2025-06-26
> 版本: v1.0

---

## TADA架构经典文件结构

```
SensewordApp/
├── Views/                          # 📱 UI层
│   ├── SpotlightView.swift         # 主搜索界面
│   ├── WordDetailView.swift        # 单词详情页
│   └── Components/                 # 可复用组件
│       ├── SearchBar.swift
│       └── WordCard.swift
│
├── ViewModels/                     # 🎯 视图模型层
│   ├── SpotlightViewModel.swift    # UI状态管理
│   ├── WordDetailViewModel.swift   # 详情页状态
│   └── Base/
│       └── BaseViewModel.swift     # 通用UI状态
│
├── Services/                       # 💼 双层服务架构
│   ├── Business/                   # 业务逻辑层 (人工设计)
│   │   ├── WordBusinessService.swift      # 单词业务逻辑
│   │   ├── CacheBusinessService.swift     # 缓存策略
│   │   └── AnalyticsBusinessService.swift # 用户行为分析
│   │
│   └── Adapters/                   # 转译适配层 (AI生成)
│       ├── WordAPIAdapter.swift           # 单词API转译
│       ├── UserAPIAdapter.swift           # 用户API转译
│       └── AuthAPIAdapter.swift           # 认证API转译
│
├── Models/                         # 📊 数据模型层
│   ├── Shared/                     # 共享模型 (跨模块使用)
│   │   └── SharedModels.swift             # 全局共享数据模型（语言代码等）
│   │
│   ├── Business/                   # 业务模型 (人工设计)
│   │   ├── WordBusinessModel.swift        # 业务单词模型
│   │   ├── UserBusinessModel.swift        # 业务用户模型
│   │   └── CacheBusinessModel.swift       # 缓存业务模型
│   │
│   ├── Display/                    # 显示模型 (人工设计)
│   │   ├── WordDisplayModel.swift         # UI显示模型
│   │   └── SearchDisplayModel.swift       # 搜索显示模型
│   │
│   └── API/                        # API模型 (AI生成)
│       ├── AuthAPIModels.swift            # 认证API专用模型
│       ├── UserAPIModels.swift            # 用户API模型
│       ├── WordAPIModels.swift            # 单词API模型
│       ├── AudioAPIModels.swift           # 音频API模型
│       ├── BookmarkAPIModels.swift        # 生词本API模型
│       ├── PurchaseAPIModels.swift        # 购买API模型
│       └── SearchAPIModels.swift          # 搜索API模型
│
├── DI/                             # 🔧 依赖注入层
│   ├── DIContainer.swift           # 依赖注入容器
│   ├── ServiceFactory.swift       # 服务工厂
│   └── Protocols/                  # 依赖注入协议
│       ├── DIContainerProtocol.swift     # 容器协议
│       └── ServiceFactoryProtocol.swift  # 工厂协议
│
├── Network/                        # 🌐 网络基础设施
│   ├── APIClient.swift             # HTTP客户端
│   ├── NetworkConfig.swift         # 网络配置
│   └── NetworkError.swift          # 网络错误定义
│
├── Storage/                        # 💾 存储基础设施
│   ├── Cache/                      # 缓存层 (临时存储)
│   │   ├── MemoryCache.swift              # 内存缓存管理
│   │   ├── DiskCache.swift               # 磁盘缓存管理
│   │   └── CachePolicy.swift             # 缓存策略
│   │
│   └── Persistence/                # 持久化层 (永久存储)
│       ├── UserDefaultsStorage.swift     # 用户偏好存储
│       ├── CoreDataStack.swift           # Core Data栈
│       └── FileStorage.swift             # 文件存储
│
├── Core/                           # 🏗️ 核心基础设施
│   ├── App.swift                   # 应用入口
│   ├── AppDelegate.swift           # 应用代理
│   ├── Extensions/                 # 扩展
│   │   ├── String+Extensions.swift
│   │   └── Date+Extensions.swift
│   └── Utils/                      # 工具类
│       ├── Logger.swift
│       └── Constants.swift
│
├── SensewordAppTests/              # 🧪 单元测试 (项目根目录)
│   ├── ViewModelTests/             # ViewModel层测试
│   │   ├── SpotlightViewModelTests.swift
│   │   └── WordDetailViewModelTests.swift
│   ├── BusinessServiceTests/       # 业务服务层测试
│   │   ├── WordBusinessServiceTests.swift
│   │   ├── CacheBusinessServiceTests.swift
│   │   └── AnalyticsBusinessServiceTests.swift
│   ├── AdapterTests/               # 转译适配层测试
│   │   ├── WordAPIAdapterTests.swift
│   │   ├── UserAPIAdapterTests.swift
│   │   └── AuthAPIAdapterTests.swift
│   ├── ModelTests/                 # 数据模型测试
│   │   ├── BusinessModelTests.swift
│   │   ├── DisplayModelTests.swift
│   │   └── APIModelTests.swift
│   ├── NetworkTests/               # 网络层测试
│   │   ├── APIClientTests.swift
│   │   └── NetworkConfigTests.swift
│   ├── StorageTests/               # 存储层测试
│   │   ├── CacheTests.swift
│   │   └── PersistenceTests.swift
│   └── Mocks/                      # 测试Mock对象
│       ├── MockAPIAdapter.swift
│       ├── MockBusinessService.swift
│       └── MockNetworkClient.swift
│
└── SensewordAppUITests/            # 🧪 UI自动化测试 (项目根目录)
    ├── SpotlightViewUITests.swift
    ├── WordDetailViewUITests.swift
    └── NavigationUITests.swift
```

## 文件结构详细说明

### 🔧 DI/ - 依赖注入层
**作用**：统一管理应用中所有组件的依赖关系，实现松耦合设计

- **DIContainer.swift**：依赖注入容器，集中创建和配置所有服务实例
- **ServiceFactory.swift**：服务工厂，负责复杂对象的创建逻辑
- **Protocols/**：定义依赖注入相关的协议，便于测试和扩展

**核心价值**：
- 集中管理依赖关系，避免硬编码
- 支持单例、瞬态等不同生命周期管理
- 便于单元测试时注入mock对象
- 提供清晰的服务创建和配置入口

### 💾 Storage/ - 存储基础设施
**重新设计**：将原来的Cache/重构为更完整的存储层，区分缓存和持久化

#### Cache/ - 缓存层 (临时存储)
- **MemoryCache.swift**：内存缓存，快速访问，应用重启后丢失
- **DiskCache.swift**：磁盘缓存，容量更大，但访问稍慢
- **CachePolicy.swift**：缓存策略，定义TTL、LRU等缓存规则

#### Persistence/ - 持久化层 (永久存储)
- **UserDefaultsStorage.swift**：用户偏好设置，简单键值对存储
- **CoreDataStack.swift**：Core Data栈，复杂关系型数据存储
- **FileStorage.swift**：文件系统存储，大文件或自定义格式数据

**设计原则**：
- **缓存**：临时性、易失性、快速访问
- **持久化**：永久性、可靠性、数据安全

### 🏗️ Core/ - 核心基础设施
**新增**：应用级别的核心组件和工具

- **App.swift**：SwiftUI应用入口，配置应用生命周期
- **AppDelegate.swift**：UIKit应用代理，处理系统级事件
- **Extensions/**：Swift扩展，增强基础类型功能
- **Utils/**：工具类，提供通用功能和常量定义

### 🧪 SensewordAppTests/ - 单元测试框架
**重要**：测试文件必须位于iOS项目根目录，确保编译正确性

#### ViewModelTests/ - ViewModel层测试
- **SpotlightViewModelTests.swift**：搜索界面状态管理测试
- **WordDetailViewModelTests.swift**：单词详情页状态测试

#### BusinessServiceTests/ - 业务服务层测试
- **WordBusinessServiceTests.swift**：单词业务逻辑测试，包括缓存策略、错误处理
- **CacheBusinessServiceTests.swift**：缓存管理逻辑测试
- **AnalyticsBusinessServiceTests.swift**：用户行为分析测试

#### AdapterTests/ - 转译适配层测试
- **WordAPIAdapterTests.swift**：单词API转译测试，验证HTTP请求和JSON解析
- **UserAPIAdapterTests.swift**：用户API转译测试
- **AuthAPIAdapterTests.swift**：认证API转译测试

#### ModelTests/ - 数据模型测试
- **BusinessModelTests.swift**：业务模型数据结构测试
- **DisplayModelTests.swift**：显示模型转换测试
- **APIModelTests.swift**：API响应模型解析测试

#### NetworkTests/ - 网络层测试
- **APIClientTests.swift**：HTTP客户端功能测试
- **NetworkConfigTests.swift**：网络配置测试

#### StorageTests/ - 存储层测试
- **CacheTests.swift**：缓存机制测试
- **PersistenceTests.swift**：持久化存储测试

#### Mocks/ - 测试Mock对象
- **MockAPIAdapter.swift**：API适配器Mock，用于业务层测试
- **MockBusinessService.swift**：业务服务Mock，用于ViewModel测试
- **MockNetworkClient.swift**：网络客户端Mock，用于适配器测试

### 🧪 SensewordAppUITests/ - UI自动化测试
**功能**：端到端UI自动化测试，验证用户交互流程

- **SpotlightViewUITests.swift**：搜索界面UI自动化测试
- **WordDetailViewUITests.swift**：单词详情页UI测试
- **NavigationUITests.swift**：应用导航流程测试

**测试策略**：
- **单元测试**：专注单一组件功能验证，快速反馈
- **UI测试**：验证完整用户流程，确保端到端功能正确
- **Mock策略**：每层独立测试，避免外部依赖影响测试稳定性
### 本技术方案覆盖范围 ⭐

本方案专注于TADA架构中的**Translation-Adapter层**实现，具体包括：

1. **Services/Adapters/** - 7个完整的API适配器
2. **Models/API/** - 对应的API数据模型
3. **Network/** - 网络基础设施
4. **DI/AdapterContainer.swift** - 依赖注入配置

### 架构设计原则

#### 🤖 AI友好设计
- **纯转译职责**：Adapter层只负责HTTP请求和JSON转换
- **标准化模式**：所有Adapter遵循相同的设计模式
- **完整文档**：基于API文档可自动生成代码

#### 🔧 职责分离
- **Adapter层**：机械转换，无业务逻辑
- **Business层**：业务规则，缓存策略
- **ViewModel层**：UI状态管理
- **View层**：用户界面展示

#### 📊 数据流向
```
API响应 → APIAdapter → BusinessService → ViewModel → View
   ↓           ↓            ↓            ↓         ↓
API模型 → 业务模型转换 → 业务逻辑处理 → 显示模型 → UI展示
```

---

## 2. 分支策略建议

- **建议的特性分支名称**: `feature/adapter-layer-implementation`
- **建议的 git worktree 文件路径**：`/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/03-senseword-adapter-layer`（请创建和根目录同层工作区）
- **基础分支**: `main`
- **分支创建模拟命令行**:
    ```bash
    # 概念性命令，用于记录和指导
    # cd /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS
    # git checkout dev
    # git pull origin dev
    # git worktree add /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/03-senseword-adapter-layer -b feature/adapter-layer-implementation dev
    ```

## 3. Commit 规划概要 (Commit Plan Summary & Status)

### 基础设施阶段 (Infrastructure Phase)
- [ ] `feat(network): implement APIClient base infrastructure with async/await support`
- [ ] `feat(network): add unified APIError handling and APIConfig authentication`
- [ ] `feat(models): create SharedModels with LanguageCode enum for cross-module usage`
- [ ] `feat(di): implement AdapterContainer for dependency injection management`

### 认证模块阶段 (Authentication Module Phase)
- [ ] `feat(auth-models): define AuthAPIModels with complete request/response structures`
- [ ] `feat(auth-adapter): implement AuthAPIAdapter with login/logout/health endpoints`
- [ ] `test(auth): add comprehensive unit tests for AuthAPIAdapter functionality`

### 用户管理模块阶段 (User Management Module Phase)
- [ ] `feat(user-models): define UserAPIModels for profile and account management`
- [ ] `feat(user-adapter): implement UserAPIAdapter with profile/deletion endpoints`
- [ ] `test(user): add unit tests for UserAPIAdapter functionality`

### 核心单词服务阶段 (Core Word Service Phase)
- [ ] `feat(word-models): define comprehensive WordAPIModels with complex data structures`
- [ ] `feat(word-adapter): implement WordAPIAdapter with word/daily/feedback/generate endpoints`
- [ ] `test(word): add unit tests for WordAPIAdapter functionality`

### 辅助服务模块阶段 (Auxiliary Services Phase)
- [ ] `feat(audio-models): define AudioAPIModels for TTS status tracking`
- [ ] `feat(audio-adapter): implement AudioAPIAdapter for audio status queries`
- [ ] `feat(bookmark-models): define BookmarkAPIModels for vocabulary management`
- [ ] `feat(bookmark-adapter): implement BookmarkAPIAdapter with CRUD operations`
- [ ] `feat(purchase-models): define PurchaseAPIModels for IAP verification`
- [ ] `feat(purchase-adapter): implement PurchaseAPIAdapter for purchase/restore operations`
- [ ] `feat(search-models): define SearchAPIModels for word index synchronization`
- [ ] `feat(search-adapter): implement SearchAPIAdapter for index updates`

### 测试与集成阶段 (Testing & Integration Phase)
- [ ] `feat(test): implement MockAPIClient for comprehensive testing support`
- [ ] `test(adapters): add integration tests for all adapter modules`
- [ ] `docs(adapter): update documentation with usage examples and best practices`
- [ ] `refactor(adapter): optimize performance and code organization`

---

## 📋 方案概述

### 设计原则
基于TADA架构的Translation-Adapter层设计，所有Adapter遵循以下原则：
- **纯转译职责**：只负责HTTP请求构建、JSON解析和数据类型转换
- **无状态设计**：不包含业务逻辑、缓存管理或错误重试
- **1:1映射**：每个API端点对应一个Adapter方法
- **AI可生成**：基于API文档可完全自动生成

### 技术栈
- **语言**: Swift 5.9+
- **网络框架**: URLSession + async/await
- **JSON解析**: Codable协议
- **错误处理**: Swift Result类型
- **认证**: 静态API密钥 + Session双重认证

---

## 🏗️ 核心基础设施

### 共享模型层数据结构

```swift
// SharedModels.swift - 全局共享数据模型
import Foundation

// 支持的语言代码枚举（22种语言）
// 用于跨模块的语言相关功能：单词查询、生词本管理、音频服务、搜索索引等
enum LanguageCode: String, Codable, CaseIterable {
    case english = "en"
    case chinese = "zh"
    case japanese = "ja"
    case german = "de"
    case french = "fr"
    case spanish = "es"
    case italian = "it"
    case portuguese = "pt"
    case polish = "pl"
    case swedish = "sv"
    case danish = "da"
    case norwegian = "no"
    case finnish = "fi"
    case korean = "ko"
    case russian = "ru"
    case arabic = "ar"
    case hindi = "hi"
    case thai = "th"
    case vietnamese = "vi"
    case turkish = "tr"
    case dutch = "nl"
    case indonesian = "id"

    // 提供用户友好的显示名称
    var displayName: String {
        switch self {
        case .chinese: return "中文"
        case .japanese: return "日本語"
        case .german: return "Deutsch"
        case .french: return "Français"
        case .spanish: return "Español"
        case .italian: return "Italiano"
        case .portuguese: return "Português"
        case .polish: return "Polski"
        case .swedish: return "Svenska"
        case .danish: return "Dansk"
        case .norwegian: return "Norsk"
        case .finnish: return "Suomi"
        case .korean: return "한국어"
        case .russian: return "Русский"
        case .arabic: return "العربية"
        case .hindi: return "हिन्दी"
        case .thai: return "ไทย"
        case .vietnamese: return "Tiếng Việt"
        case .turkish: return "Türkçe"
        case .dutch: return "Nederlands"
        }
    }
}

// 其他全局共享的枚举和数据结构可以在此添加
// 例如：通用错误代码、状态枚举等
```

### 1. 网络客户端基础类

```swift
// APIClient.swift - HTTP客户端基础设施
import Foundation

protocol APIClientProtocol {
    func request<T: Codable>(
        endpoint: String,
        method: HTTPMethod,
        headers: [String: String]?,
        body: Data?
    ) async throws -> T
}

class APIClient: APIClientProtocol {
    private let baseURL: String
    private let session: URLSession
    
    init(baseURL: String, session: URLSession = .shared) {
        self.baseURL = baseURL
        self.session = session
    }
    
    func request<T: Codable>(
        endpoint: String,
        method: HTTPMethod = .GET,
        headers: [String: String]? = nil,
        body: Data? = nil
    ) async throws -> T {
        // HTTP请求实现
        // 错误处理和JSON解析
    }
}

enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case DELETE = "DELETE"
}
```

### 2. 通用错误类型

```swift
// APIError.swift - 统一错误处理
enum APIError: Error {
    case invalidURL
    case invalidResponse
    case invalidAPIKey
    case unauthorized
    case networkError(Error)
    case decodingError(Error)
    case serverError(Int, String)
}
```

### 3. 认证配置

```swift
// APIConfig.swift - 认证配置
struct APIConfig {
    static let staticAPIKey = "sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025"
    static let authBaseURL = "https://auth.senseword.app"
    static let apiBaseURL = "https://api.senseword.app"
    
    static var staticHeaders: [String: String] {
        ["X-Static-API-Key": staticAPIKey]
    }
    
    static func authHeaders(sessionId: String) -> [String: String] {
        [
            "X-Static-API-Key": staticAPIKey,
            "Authorization": "Bearer \(sessionId)"
        ]
    }
}
```

---

## 🔐 认证模块 Adapter

### AuthAPIAdapter.swift

```swift
// AuthAPIAdapter.swift - 认证API转译层
import Foundation

protocol AuthAPIAdapterProtocol {
    func login(idToken: String, provider: AuthProvider) async throws -> SessionLoginSuccessResponse
    func logout(sessionId: String, reason: LogoutReason?) async throws -> LogoutSuccessResponse
    func logoutAll(sessionId: String, reason: LogoutAllReason?) async throws -> LogoutAllSuccessResponse
    func healthCheck() async throws -> HealthCheckResponse
}

class AuthAPIAdapter: AuthAPIAdapterProtocol {
    private let apiClient: APIClientProtocol

    init(apiClient: APIClientProtocol) {
        self.apiClient = apiClient
    }

    func login(idToken: String, provider: AuthProvider = .apple) async throws -> SessionLoginSuccessResponse {
        let requestBody = LoginRequestBody(idToken: idToken, provider: provider)
        let bodyData = try JSONEncoder().encode(requestBody)
        
        return try await apiClient.request(
            endpoint: "/api/v1/auth/login",
            method: .POST,
            headers: APIConfig.staticHeaders.merging(["Content-Type": "application/json"]) { _, new in new },
            body: bodyData
        )
    }
    
    func logout(sessionId: String, reason: LogoutReason? = nil) async throws -> LogoutSuccessResponse {
        let requestBody = LogoutRequest(reason: reason)
        let bodyData = try JSONEncoder().encode(requestBody)

        return try await apiClient.request(
            endpoint: "/api/v1/auth/logout",
            method: .POST,
            headers: APIConfig.authHeaders(sessionId: sessionId).merging(["Content-Type": "application/json"]) { _, new in new },
            body: bodyData
        )
    }

    func logoutAll(sessionId: String, reason: LogoutAllReason? = nil) async throws -> LogoutAllSuccessResponse {
        let requestBody = LogoutAllRequest(reason: reason)
        let bodyData = try JSONEncoder().encode(requestBody)
        
        return try await apiClient.request(
            endpoint: "/api/v1/auth/logout-all",
            method: .POST,
            headers: APIConfig.authHeaders(sessionId: sessionId).merging(["Content-Type": "application/json"]) { _, new in new },
            body: bodyData
        )
    }
    
    func healthCheck() async throws -> HealthCheckResponse {
        return try await apiClient.request(
            endpoint: "/api/v1/auth/health",
            method: .GET,
            headers: APIConfig.staticHeaders,
            body: nil
        )
    }
}
```


### 认证模块数据结构

```swift
// AuthAPIModels.swift - 认证API专用数据模型
import Foundation

// 请求模型
struct LoginRequestBody: Codable {
    let idToken: String
    let provider: AuthProvider
}

enum AuthProvider: String, Codable {
    case apple = "apple"
}


struct LogoutRequest: Codable {
    let reason: LogoutReason?
}

enum LogoutReason: String, Codable {
    case userInitiated = "user_initiated"
    case security = "security"
    case admin = "admin"
}

struct LogoutAllRequest: Codable {
    let reason: LogoutAllReason?
}

enum LogoutAllReason: String, Codable {
    case securityConcern = "security_concern"
    case deviceLost = "device_lost"
    case userRequest = "user_request"
}

// 响应模型
struct SessionLoginSuccessResponse: Codable {
    let success: Bool
    let session: SessionInfo

    // 确保success字段在成功响应中始终为true
    init(session: SessionInfo) {
        self.success = true
        self.session = session
    }
}

struct SessionInfo: Codable {
    let sessionId: String
    let user: UserInfo
}

struct UserInfo: Codable {
    let id: String
    let email: String
    let displayName: String
    let isPro: Bool
}

struct LogoutSuccessResponse: Codable {
    let success: Bool
    let message: String
    let sessionRevoked: Bool
    let timestamp: String

    // 确保success字段在成功响应中始终为true
    init(message: String, sessionRevoked: Bool, timestamp: String) {
        self.success = true
        self.message = message
        self.sessionRevoked = sessionRevoked
        self.timestamp = timestamp
    }
}

struct LogoutAllSuccessResponse: Codable {
    let success: Bool
    let message: String
    let sessionsRevoked: Int
    let timestamp: String

    // 确保success字段在成功响应中始终为true
    init(message: String, sessionsRevoked: Int, timestamp: String) {
        self.success = true
        self.message = message
        self.sessionsRevoked = sessionsRevoked
        self.timestamp = timestamp
    }
}

struct HealthCheckResponse: Codable {
    let status: String
    let service: String
    let timestamp: String
    let environment: String
    let version: String
}

// 错误响应模型
struct LoginErrorResponse: Codable {
    let success: Bool
    let error: LoginErrorCode
    let message: String
    let timestamp: String

    // 确保success字段在错误响应中始终为false
    init(error: LoginErrorCode, message: String, timestamp: String) {
        self.success = false
        self.error = error
        self.message = message
        self.timestamp = timestamp
    }
}

enum LoginErrorCode: String, Codable {
    case invalidToken = "INVALID_TOKEN"
    case userCreationFailed = "USER_CREATION_FAILED"
    case systemError = "SYSTEM_ERROR"
    case invalidRequest = "INVALID_REQUEST"
    case invalidAPIKey = "INVALID_API_KEY"
}

struct LogoutErrorResponse: Codable {
    let success: Bool
    let error: LogoutErrorCode
    let message: String
    let timestamp: String

    init(error: LogoutErrorCode, message: String, timestamp: String) {
        self.success = false
        self.error = error
        self.message = message
        self.timestamp = timestamp
    }
}

enum LogoutErrorCode: String, Codable {
    case sessionNotFound = "SESSION_NOT_FOUND"
    case alreadyLoggedOut = "ALREADY_LOGGED_OUT"
    case unauthorized = "UNAUTHORIZED"
    case systemError = "SYSTEM_ERROR"
    case invalidAPIKey = "INVALID_API_KEY"
}

struct LogoutAllErrorResponse: Codable {
    let success: Bool
    let error: LogoutAllErrorCode
    let message: String
    let timestamp: String

    init(error: LogoutAllErrorCode, message: String, timestamp: String) {
        self.success = false
        self.error = error
        self.message = message
        self.timestamp = timestamp
    }
}

enum LogoutAllErrorCode: String, Codable {
    case unauthorized = "UNAUTHORIZED"
    case systemError = "SYSTEM_ERROR"
    case invalidAPIKey = "INVALID_API_KEY"
}

struct HealthCheckErrorResponse: Codable {
    let success: Bool
    let error: HealthCheckErrorCode
    let message: String
    let timestamp: String

    init(error: HealthCheckErrorCode, message: String, timestamp: String) {
        self.success = false
        self.error = error
        self.message = message
        self.timestamp = timestamp
    }
}

enum HealthCheckErrorCode: String, Codable {
    case invalidAPIKey = "INVALID_API_KEY"
}
```

---

## 👤 用户管理模块 Adapter

### UserAPIAdapter.swift

```swift
// UserAPIAdapter.swift - 用户管理API转译层
import Foundation

protocol UserAPIAdapterProtocol {
    func getCurrentUser(sessionId: String) async throws -> UserProfileResponse
    func deleteAccount(sessionId: String, confirmation: String) async throws -> AccountDeletionSuccessResponse
}

class UserAPIAdapter: UserAPIAdapterProtocol {
    private let apiClient: APIClientProtocol
    
    init(apiClient: APIClientProtocol) {
        self.apiClient = apiClient
    }
    
    func getCurrentUser(sessionId: String) async throws -> UserProfileResponse {
        return try await apiClient.request(
            endpoint: "/api/v1/users/me",
            method: .GET,
            headers: APIConfig.authHeaders(sessionId: sessionId),
            body: nil
        )
    }
    
    func deleteAccount(sessionId: String, confirmation: String) async throws -> AccountDeletionSuccessResponse {
        let requestBody = AccountDeletionRequest(confirmation: confirmation)
        let bodyData = try JSONEncoder().encode(requestBody)
        
        return try await apiClient.request(
            endpoint: "/api/v1/users/me",
            method: .DELETE,
            headers: APIConfig.authHeaders(sessionId: sessionId).merging(["Content-Type": "application/json"]) { _, new in new },
            body: bodyData
        )
    }
}
```

### 用户管理数据结构

```swift
// UserAPIModels.swift - 用户管理API数据模型
import Foundation

// 请求模型
struct AccountDeletionRequest: Codable {
    let confirmation: String
}

// 响应模型
struct UserProfileResponse: Codable {
    let success: Bool
    let user: UserDetailInfo
}

struct UserDetailInfo: Codable {
    let id: String
    let email: String
    let displayName: String
    let isPro: Bool
    let createdAt: String
}

struct AccountDeletionSuccessResponse: Codable {
    let success: Bool
    let message: String
    let dataCleared: DataClearedInfo
    let timestamp: String
}

struct DataClearedInfo: Codable {
    let sessions: Int
    let bookmarks: Int
    let userRecord: Bool
}
```

---

## 📚 单词服务模块 Adapter

### WordAPIAdapter.swift

```swift
// WordAPIAdapter.swift - 单词服务API转译层
import Foundation

protocol WordAPIAdapterProtocol {
    func getWord(_ word: String, language: LanguageCode?) async throws -> WordDefinitionResponse
    func getDailyWord() async throws -> DailyWordResponse
    func submitFeedback(word: String, language: LanguageCode, action: FeedbackAction) async throws -> FeedbackSuccessResponse
}

class WordAPIAdapter: WordAPIAdapterProtocol {
    private let apiClient: APIClientProtocol

    init(apiClient: APIClientProtocol) {
        self.apiClient = apiClient
    }

    func getWord(_ word: String, language: LanguageCode? = .chinese) async throws -> WordDefinitionResponse {
        let encodedWord = word.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? word
        var endpoint = "/api/v1/word/\(encodedWord)"

        if let lang = language {
            endpoint += "?lang=\(lang.rawValue)"
        }

        return try await apiClient.request(
            endpoint: endpoint,
            method: .GET,
            headers: APIConfig.staticHeaders,
            body: nil
        )
    }

    func getDailyWord() async throws -> DailyWordResponse {
        return try await apiClient.request(
            endpoint: "/api/v1/daily-word",
            method: .GET,
            headers: APIConfig.staticHeaders,
            body: nil
        )
    }

    func submitFeedback(word: String, language: LanguageCode, action: FeedbackAction) async throws -> FeedbackSuccessResponse {
        let requestBody = FeedbackRequest(word: word, language: language, action: action)
        let bodyData = try JSONEncoder().encode(requestBody)

        return try await apiClient.request(
            endpoint: "/api/v1/feedback",
            method: .POST,
            headers: APIConfig.staticHeaders.merging(["Content-Type": "application/json"]) { _, new in new },
            body: bodyData
        )
    }


}
```

### 单词服务数据结构

```swift
// WordAPIModels.swift - 单词服务API数据模型
import Foundation
// 引用共享模型层的LanguageCode枚举

// 请求模型
struct FeedbackRequest: Codable {
    let word: String
    let language: LanguageCode
    let action: FeedbackAction
}

enum FeedbackAction: String, Codable {
    case like = "like"
    case dislike = "dislike"
}

struct WordGenerateRequest: Codable {
    let word: String
    let language: LanguageCode
}

// 响应模型
struct WordDefinitionResponse: Codable {
    let word: String
    let metadata: WordMetadata
    let content: WordContent
}

struct WordMetadata: Codable {
    let wordFrequency: String
    let relatedConcepts: [String]
}

struct WordContent: Codable {
    let difficulty: String
    let phoneticSymbols: [PhoneticSymbol]
    let coreDefinition: String
    let contextualExplanation: ContextualExplanation
    let usageExamples: [UsageExampleCategory]
    let usageScenarios: [UsageScenario]
    let collocations: [Collocation]
    let usageNotes: [UsageNote]
    let synonyms: [Synonym]
}

struct PhoneticSymbol: Codable {
    let type: String
    let symbol: String
}

struct ContextualExplanation: Codable {
    let nativeSpeakerIntent: String
    let emotionalResonance: String
    let vividImagery: String
    let etymologicalEssence: String
}

struct UsageExampleCategory: Codable {
    let category: String
    let examples: [String]
}

struct UsageScenario: Codable {
    let scenario: String
    let description: String
}

struct Collocation: Codable {
    let type: String
    let words: [String]
}

struct UsageNote: Codable {
    let type: String
    let note: String
}

struct Synonym: Codable {
    let word: String
    let nuance: String
}

struct DailyWordResponse: Codable {
    let word: String
    let date: String
}

struct FeedbackSuccessResponse: Codable {
    let success: Bool
    let newScore: Int
    let message: String

    // 确保success字段在成功响应中始终为true
    init(newScore: Int, message: String) {
        self.success = true
        self.newScore = newScore
        self.message = message
    }
}

struct WordGenerateResponse: Codable {
    let success: Bool
    let data: WordGenerateData
}

struct WordGenerateData: Codable {
    let word: String
    let isWordOfTheDayCandidate: Bool
    let content: WordContent
    let saved: Bool
}

// 单词查询错误响应
struct WordQueryErrorResponse: Codable {
    let error: WordQueryError
}

struct WordQueryError: Codable {
    let code: WordQueryErrorCode
    let message: String
}

enum WordQueryErrorCode: String, Codable {
    case invalidWord = "INVALID_WORD"
    case databaseError = "DATABASE_ERROR"
}

// 反馈错误响应
struct FeedbackErrorResponse: Codable {
    let error: FeedbackError
}

struct FeedbackError: Codable {
    let code: FeedbackErrorCode
    let message: String
}

enum FeedbackErrorCode: String, Codable {
    case invalidAction = "INVALID_ACTION"
    case authenticationRequired = "AUTHENTICATION_REQUIRED"
    case invalidWord = "INVALID_WORD"
    case databaseError = "DATABASE_ERROR"
}

// 每日一词错误响应
struct DailyWordErrorResponse: Codable {
    let error: DailyWordError
}

struct DailyWordError: Codable {
    let code: DailyWordErrorCode
    let message: String
}

enum DailyWordErrorCode: String, Codable {
    case invalidAPIKey = "INVALID_API_KEY"
}

// 单词生成错误响应
struct WordGenerateErrorResponse: Codable {
    let error: WordGenerateError
}

struct WordGenerateError: Codable {
    let code: WordGenerateErrorCode
    let message: String
}

enum WordGenerateErrorCode: String, Codable {
    case invalidWord = "INVALID_WORD"
    case aiGenerationFailed = "AI_GENERATION_FAILED"
}
```

---

## 🎵 音频服务模块 Adapter

### AudioAPIAdapter.swift

```swift
// AudioAPIAdapter.swift - 音频服务API转译层
import Foundation

protocol AudioAPIAdapterProtocol {
    func getAudioStatus(word: String, language: LanguageCode?) async throws -> AudioStatusResponse
}

class AudioAPIAdapter: AudioAPIAdapterProtocol {
    private let apiClient: APIClientProtocol

    init(apiClient: APIClientProtocol) {
        self.apiClient = apiClient
    }

    func getAudioStatus(word: String, language: LanguageCode? = .chinese) async throws -> AudioStatusResponse {
        let encodedWord = word.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? word
        var endpoint = "/api/v1/audio/\(encodedWord)/status"

        if let lang = language {
            endpoint += "?lang=\(lang.rawValue)"
        }

        return try await apiClient.request(
            endpoint: endpoint,
            method: .GET,
            headers: APIConfig.staticHeaders,
            body: nil
        )
    }
}
```

### 音频服务数据结构

```swift
// AudioAPIModels.swift - 音频服务API数据模型
import Foundation

struct AudioStatusResponse: Codable {
    let word: String
    let audioStatus: String // "processing" | "completed"
    let lastUpdated: String
}
```

---

## 📖 生词本管理模块 Adapter

### BookmarkAPIAdapter.swift

```swift
// BookmarkAPIAdapter.swift - 生词本管理API转译层
import Foundation

protocol BookmarkAPIAdapterProtocol {
    func addBookmark(sessionId: String, word: String, language: LanguageCode) async throws -> BookmarkCRUDResponse
    func removeBookmark(sessionId: String, word: String, language: LanguageCode) async throws -> BookmarkCRUDResponse
    func getBookmarks(sessionId: String) async throws -> GetBookmarksResponse
    func healthCheck() async throws -> BookmarkHealthResponse
}

class BookmarkAPIAdapter: BookmarkAPIAdapterProtocol {
    private let apiClient: APIClientProtocol

    init(apiClient: APIClientProtocol) {
        self.apiClient = apiClient
    }

    func addBookmark(sessionId: String, word: String, language: LanguageCode) async throws -> BookmarkCRUDResponse {
        let requestBody = AddBookmarkRequest(word: word, language: language)
        let bodyData = try JSONEncoder().encode(requestBody)

        return try await apiClient.request(
            endpoint: "/api/v1/bookmarks",
            method: .POST,
            headers: APIConfig.authHeaders(sessionId: sessionId).merging(["Content-Type": "application/json"]) { _, new in new },
            body: bodyData
        )
    }

    func removeBookmark(sessionId: String, word: String, language: LanguageCode) async throws -> BookmarkCRUDResponse {
        let requestBody = RemoveBookmarkRequest(word: word, language: language)
        let bodyData = try JSONEncoder().encode(requestBody)

        return try await apiClient.request(
            endpoint: "/api/v1/bookmarks",
            method: .DELETE,
            headers: APIConfig.authHeaders(sessionId: sessionId).merging(["Content-Type": "application/json"]) { _, new in new },
            body: bodyData
        )
    }

    func getBookmarks(sessionId: String) async throws -> GetBookmarksResponse {
        return try await apiClient.request(
            endpoint: "/api/v1/bookmarks",
            method: .GET,
            headers: APIConfig.authHeaders(sessionId: sessionId),
            body: nil
        )
    }

    func healthCheck() async throws -> BookmarkHealthResponse {
        return try await apiClient.request(
            endpoint: "/api/v1/bookmarks/health",
            method: .GET,
            headers: APIConfig.staticHeaders,
            body: nil
        )
    }
}
```

### 生词本管理数据结构

```swift
// BookmarkAPIModels.swift - 生词本管理API数据模型
import Foundation
// 引用共享模型层的LanguageCode枚举

// 请求模型
struct AddBookmarkRequest: Codable {
    let word: String
    let language: LanguageCode
}

struct RemoveBookmarkRequest: Codable {
    let word: String
    let language: LanguageCode
}

// 响应模型
struct BookmarkCRUDResponse: Codable {
    let success: Bool
    let message: String

    // 确保success字段在成功响应中始终为true
    init(message: String, isSuccess: Bool = true) {
        self.success = isSuccess
        self.message = message
    }
}

struct GetBookmarksResponse: Codable {
    let success: Bool
    let words: [String]

    // 确保success字段正确设置
    init(words: [String], isSuccess: Bool = true) {
        self.success = isSuccess
        self.words = words
    }
}

struct BookmarkHealthResponse: Codable {
    let service: String
    let status: String
    let timestamp: String
    let version: String
}

// 生词本认证错误响应
struct BookmarkAuthErrorResponse: Codable {
    let error: BookmarkAuthError
}

struct BookmarkAuthError: Codable {
    let code: BookmarkAuthErrorCode
    let message: String
}

enum BookmarkAuthErrorCode: String, Codable {
    case invalidAPIKey = "INVALID_API_KEY"
    case unauthorized = "UNAUTHORIZED"
    case sessionExpired = "SESSION_EXPIRED"
}

// 生词本健康检查错误响应
struct BookmarkHealthErrorResponse: Codable {
    let error: BookmarkHealthError
}

struct BookmarkHealthError: Codable {
    let code: BookmarkHealthErrorCode
    let message: String
}

enum BookmarkHealthErrorCode: String, Codable {
    case invalidAPIKey = "INVALID_API_KEY"
}
```

---

## 💰 购买验证模块 Adapter

### PurchaseAPIAdapter.swift

```swift
// PurchaseAPIAdapter.swift - 购买验证API转译层
import Foundation

protocol PurchaseAPIAdapterProtocol {
    func verifyPurchase(sessionId: String, receiptData: String, productId: ProductId, transactionId: String) async throws -> VerifyPurchaseResponse
    func restorePurchase(sessionId: String, receiptData: String) async throws -> RestorePurchaseResponse
}

class PurchaseAPIAdapter: PurchaseAPIAdapterProtocol {
    private let apiClient: APIClientProtocol

    init(apiClient: APIClientProtocol) {
        self.apiClient = apiClient
    }

    func verifyPurchase(sessionId: String, receiptData: String, productId: ProductId, transactionId: String) async throws -> VerifyPurchaseResponse {
        let requestBody = VerifyPurchaseRequest(
            receiptData: receiptData,
            productId: productId,
            transactionId: transactionId
        )
        let bodyData = try JSONEncoder().encode(requestBody)

        return try await apiClient.request(
            endpoint: "/api/v1/purchase/verify",
            method: .POST,
            headers: APIConfig.authHeaders(sessionId: sessionId).merging(["Content-Type": "application/json"]) { _, new in new },
            body: bodyData
        )
    }

    func restorePurchase(sessionId: String, receiptData: String) async throws -> RestorePurchaseResponse {
        let requestBody = RestorePurchaseRequest(receiptData: receiptData)
        let bodyData = try JSONEncoder().encode(requestBody)

        return try await apiClient.request(
            endpoint: "/api/v1/purchase/restore",
            method: .POST,
            headers: APIConfig.authHeaders(sessionId: sessionId).merging(["Content-Type": "application/json"]) { _, new in new },
            body: bodyData
        )
    }
}
```

### 购买验证数据结构

```swift
// PurchaseAPIModels.swift - 购买验证API数据模型
import Foundation

// 请求模型
struct VerifyPurchaseRequest: Codable {
    let receiptData: String
    let productId: ProductId
    let transactionId: String
}

enum ProductId: String, Codable {
    case monthlyPremium = "com.senseword.premium.monthly"
    case yearlyPremium = "com.senseword.premium.yearly"
}

struct RestorePurchaseRequest: Codable {
    let receiptData: String
}

// 响应模型
struct VerifyPurchaseResponse: Codable {
    let success: Bool
    let isPro: Bool
    let expiresAt: String?
    let message: String

    // 确保success字段在成功响应中始终为true
    init(isPro: Bool, expiresAt: String?, message: String) {
        self.success = true
        self.isPro = isPro
        self.expiresAt = expiresAt
        self.message = message
    }
}

struct RestorePurchaseResponse: Codable {
    let success: Bool
    let isPro: Bool
    let expiresAt: String?
    let message: String

    // 确保success字段在成功响应中始终为true
    init(isPro: Bool, expiresAt: String?, message: String) {
        self.success = true
        self.isPro = isPro
        self.expiresAt = expiresAt
        self.message = message
    }
}

// 购买验证错误响应
struct VerifyPurchaseErrorResponse: Codable {
    let success: Bool
    let error: VerifyPurchaseErrorCode
    let message: String
    let timestamp: String

    init(error: VerifyPurchaseErrorCode, message: String, timestamp: String) {
        self.success = false
        self.error = error
        self.message = message
        self.timestamp = timestamp
    }
}

enum VerifyPurchaseErrorCode: String, Codable {
    case invalidInput = "INVALID_INPUT"
    case verificationFailed = "VERIFICATION_FAILED"
    case invalidReceipt = "INVALID_RECEIPT"
    case expiredReceipt = "EXPIRED_RECEIPT"
    case duplicatePurchase = "DUPLICATE_PURCHASE"
    case environmentMismatch = "ENVIRONMENT_MISMATCH"
    case bundleIdMismatch = "BUNDLE_ID_MISMATCH"
    case networkError = "NETWORK_ERROR"
    case internalError = "INTERNAL_ERROR"
    case unauthorized = "UNAUTHORIZED"
    case invalidAPIKey = "INVALID_API_KEY"
}

// 恢复购买错误响应
struct RestorePurchaseErrorResponse: Codable {
    let success: Bool
    let error: RestorePurchaseErrorCode
    let message: String
    let timestamp: String

    init(error: RestorePurchaseErrorCode, message: String, timestamp: String) {
        self.success = false
        self.error = error
        self.message = message
        self.timestamp = timestamp
    }
}

enum RestorePurchaseErrorCode: String, Codable {
    case invalidInput = "INVALID_INPUT"
    case noPurchasesToRestore = "NO_PURCHASES_TO_RESTORE"
    case restoreFailed = "RESTORE_FAILED"
    case invalidReceipt = "INVALID_RECEIPT"
    case expiredReceipt = "EXPIRED_RECEIPT"
    case environmentMismatch = "ENVIRONMENT_MISMATCH"
    case bundleIdMismatch = "BUNDLE_ID_MISMATCH"
    case networkError = "NETWORK_ERROR"
    case internalError = "INTERNAL_ERROR"
    case unauthorized = "UNAUTHORIZED"
    case invalidAPIKey = "INVALID_API_KEY"
}
```

---

## 🔍 搜索索引模块 Adapter

### SearchAPIAdapter.swift

```swift
// SearchAPIAdapter.swift - 搜索索引API转译层
import Foundation

protocol SearchAPIAdapterProtocol {
    func getWordIndexUpdates(language: LanguageCode, since: Int?) async throws -> WordIndexResponse
}

class SearchAPIAdapter: SearchAPIAdapterProtocol {
    private let apiClient: APIClientProtocol

    init(apiClient: APIClientProtocol) {
        self.apiClient = apiClient
    }

    func getWordIndexUpdates(language: LanguageCode, since: Int? = 0) async throws -> WordIndexResponse {
        var endpoint = "/api/v1/word-index/updates?lang=\(language.rawValue)"

        if let sinceValue = since {
            endpoint += "&since=\(sinceValue)"
        }

        return try await apiClient.request(
            endpoint: endpoint,
            method: .GET,
            headers: APIConfig.staticHeaders,
            body: nil
        )
    }
}
```

### 搜索索引数据结构

```swift
// SearchAPIModels.swift - 搜索索引API数据模型
import Foundation
// 引用共享模型层的LanguageCode枚举

struct WordIndexResponse: Codable {
    let success: Bool
    let data: [WordIndexItem]
    let lastSyncId: Int
    let metadata: WordIndexMetadata

    // 确保success字段在成功响应中始终为true
    init(data: [WordIndexItem], lastSyncId: Int, metadata: WordIndexMetadata) {
        self.success = true
        self.data = data
        self.lastSyncId = lastSyncId
        self.metadata = metadata
    }
}

struct WordIndexItem: Codable {
    let syncId: Int
    let word: String
    let language: LanguageCode
    let coreDefinition: String
}

struct WordIndexMetadata: Codable {
    let count: Int
    let requestTime: Int
    let fromSyncId: Int
    let toSyncId: Int
}

// 搜索索引错误响应
struct WordIndexErrorResponse: Codable {
    let error: WordIndexError
}

struct WordIndexError: Codable {
    let code: WordIndexErrorCode
    let message: String
}

enum WordIndexErrorCode: String, Codable {
    case authenticationFailed = "AUTHENTICATION_FAILED"
    case invalidLanguage = "INVALID_LANGUAGE"
    case databaseError = "DATABASE_ERROR"
}
```

---

## 🔧 依赖注入配置

### AdapterContainer.swift

```swift
// AdapterContainer.swift - Adapter层依赖注入容器
import Foundation

class AdapterContainer {
    static let shared = AdapterContainer()

    private init() {}

    // MARK: - 网络基础设施
    lazy var authAPIClient: APIClientProtocol = {
        APIClient(baseURL: APIConfig.authBaseURL)
    }()

    lazy var mainAPIClient: APIClientProtocol = {
        APIClient(baseURL: APIConfig.apiBaseURL)
    }()

    // MARK: - API Adapters
    lazy var authAPIAdapter: AuthAPIAdapterProtocol = {
        AuthAPIAdapter(apiClient: authAPIClient)
    }()

    lazy var userAPIAdapter: UserAPIAdapterProtocol = {
        UserAPIAdapter(apiClient: authAPIClient)
    }()

    lazy var wordAPIAdapter: WordAPIAdapterProtocol = {
        WordAPIAdapter(apiClient: mainAPIClient)
    }()

    lazy var audioAPIAdapter: AudioAPIAdapterProtocol = {
        AudioAPIAdapter(apiClient: mainAPIClient)
    }()

    lazy var bookmarkAPIAdapter: BookmarkAPIAdapterProtocol = {
        BookmarkAPIAdapter(apiClient: mainAPIClient)
    }()

    lazy var purchaseAPIAdapter: PurchaseAPIAdapterProtocol = {
        PurchaseAPIAdapter(apiClient: authAPIClient)
    }()

    lazy var searchAPIAdapter: SearchAPIAdapterProtocol = {
        SearchAPIAdapter(apiClient: mainAPIClient)
    }()
}
```

---

## 🧪 测试支持

### MockAPIClient.swift

```swift
// MockAPIClient.swift - 测试用Mock客户端
import Foundation

class MockAPIClient: APIClientProtocol {
    var mockResponses: [String: Any] = [:]
    var shouldThrowError: APIError?

    func request<T: Codable>(
        endpoint: String,
        method: HTTPMethod,
        headers: [String: String]?,
        body: Data?
    ) async throws -> T {
        if let error = shouldThrowError {
            throw error
        }

        guard let mockData = mockResponses[endpoint],
              let jsonData = try? JSONSerialization.data(withJSONObject: mockData),
              let response = try? JSONDecoder().decode(T.self, from: jsonData) else {
            throw APIError.invalidResponse
        }

        return response
    }

    func setMockResponse<T: Codable>(for endpoint: String, response: T) {
        let encoder = JSONEncoder()
        if let data = try? encoder.encode(response),
           let json = try? JSONSerialization.jsonObject(with: data) {
            mockResponses[endpoint] = json
        }
    }
}
```

### AdapterTests示例

```swift
// AuthAPIAdapterTests.swift - 测试示例
import XCTest
@testable import SenseWordApp

class AuthAPIAdapterTests: XCTestCase {
    var mockAPIClient: MockAPIClient!
    var authAdapter: AuthAPIAdapter!

    override func setUp() {
        super.setUp()
        mockAPIClient = MockAPIClient()
        authAdapter = AuthAPIAdapter(apiClient: mockAPIClient)
    }

    func testLoginSuccess() async throws {
        // Given
        let expectedResponse = SessionLoginSuccessResponse(
            session: SessionInfo(
                sessionId: "test_session_123",
                user: UserInfo(
                    id: "user_123",
                    email: "<EMAIL>",
                    displayName: "Test User",
                    isPro: false
                )
            )
        )
        mockAPIClient.setMockResponse(for: "/api/v1/auth/login", response: expectedResponse)

        // When
        let result = try await authAdapter.login(idToken: "test_token", provider: .apple)

        // Then
        XCTAssertTrue(result.success)
        XCTAssertEqual(result.session.sessionId, "test_session_123")
        XCTAssertEqual(result.session.user.email, "<EMAIL>")
    }

    func testLoginFailure() async {
        // Given
        mockAPIClient.shouldThrowError = .unauthorized

        // When & Then
        do {
            _ = try await authAdapter.login(idToken: "invalid_token", provider: .apple)
            XCTFail("Expected error to be thrown")
        } catch {
            XCTAssertTrue(error is APIError)
        }
    }
}
```

---

## 📋 实施指南

### 开发优先级

#### 第一阶段：基础设施 (1-2天)
1. **APIClient.swift** - HTTP客户端基础类
2. **APIError.swift** - 统一错误处理
3. **APIConfig.swift** - 认证配置
4. **AdapterContainer.swift** - 依赖注入容器

#### 第二阶段：核心Adapter (3-5天)
1. **AuthAPIAdapter.swift** - 认证功能，优先级最高
2. **WordAPIAdapter.swift** - 单词查询，核心功能
3. **BookmarkAPIAdapter.swift** - 生词本管理
4. **UserAPIAdapter.swift** - 用户管理

#### 第三阶段：辅助Adapter (2-3天)
1. **AudioAPIAdapter.swift** - 音频状态查询
2. **PurchaseAPIAdapter.swift** - 购买验证
3. **SearchAPIAdapter.swift** - 搜索索引

#### 第四阶段：测试与优化 (2-3天)
1. **MockAPIClient.swift** - 测试基础设施
2. **单元测试** - 每个Adapter的测试用例
3. **集成测试** - 端到端API调用测试

### 质量保证清单

#### 代码规范检查
- [ ] 所有Adapter遵循协议设计模式
- [ ] 统一的错误处理机制
- [ ] 完整的数据模型定义
- [ ] 符合Swift编码规范

#### 功能完整性检查
- [ ] 所有后端API端点都有对应的Adapter方法
- [ ] 请求参数和响应模型完整匹配API文档
- [ ] 认证头部正确配置
- [ ] URL编码和JSON序列化正确处理

#### 测试覆盖检查
- [ ] 每个Adapter都有对应的单元测试
- [ ] 成功和失败场景都有测试覆盖
- [ ] Mock数据符合真实API响应格式
- [ ] 异步操作测试正确实现

### 集成注意事项

#### 与Business Service层集成
- Adapter只负责API调用，不包含业务逻辑
- Business Service层负责调用Adapter并处理业务规则
- 错误处理在Business Service层进行转换和重试

#### 认证流程集成
- 静态API密钥在所有请求中必须包含
- Session认证需要先通过AuthAPIAdapter获取sessionId
- 双重认证的API需要同时包含静态密钥和Session

#### 缓存策略集成
- Adapter层不实现缓存逻辑
- 缓存由Business Service层或专门的缓存服务处理
- Adapter始终发起真实的网络请求

---

## 📊 技术方案总结

### 架构优势

#### 🤖 AI友好设计
- **纯转译职责**：每个方法只做HTTP请求和JSON转换
- **标准化模式**：所有Adapter遵循相同的设计模式
- **完整文档**：基于API文档可自动生成代码

#### 🧪 高可测试性
- **协议抽象**：便于Mock和单元测试
- **无状态设计**：测试隔离性好
- **错误可控**：统一的错误处理机制

#### 🔧 易维护性
- **职责单一**：每个Adapter只负责对应的API模块
- **松耦合**：通过依赖注入实现组件解耦
- **标准化**：统一的代码结构和命名规范

### 开发效率提升

#### 代码生成收益
- **50%工作量节省**：Adapter层可完全AI生成
- **零错误率**：基于API文档生成，避免手工错误
- **快速迭代**：API变更时可快速重新生成

#### 团队协作收益
- **并行开发**：Adapter层和Business层可并行开发
- **清晰边界**：职责分离明确，减少团队沟通成本
- **标准化**：统一的开发模式，降低学习成本

### 质量保证体系

#### 自动化测试
- **单元测试**：每个Adapter方法都有对应测试
- **集成测试**：验证与真实API的兼容性
- **Mock测试**：支持离线开发和测试

#### 错误处理
- **统一错误类型**：APIError枚举覆盖所有错误场景
- **详细错误信息**：包含HTTP状态码和错误描述
- **优雅降级**：网络错误时的合理处理

---

## 🚀 下一步行动

### 立即可执行任务

#### 1. 创建项目文件结构
按照TADA架构在SenseWord项目中创建以下文件：

```bash
# 网络基础设施
mkdir -p SensewordApp/Network
touch SensewordApp/Network/APIClient.swift
touch SensewordApp/Network/APIConfig.swift
touch SensewordApp/Network/APIError.swift

# API适配器层
mkdir -p SensewordApp/Services/Adapters
touch SensewordApp/Services/Adapters/AuthAPIAdapter.swift
touch SensewordApp/Services/Adapters/UserAPIAdapter.swift
touch SensewordApp/Services/Adapters/WordAPIAdapter.swift
touch SensewordApp/Services/Adapters/AudioAPIAdapter.swift
touch SensewordApp/Services/Adapters/BookmarkAPIAdapter.swift
touch SensewordApp/Services/Adapters/PurchaseAPIAdapter.swift
touch SensewordApp/Services/Adapters/SearchAPIAdapter.swift

# 数据模型层
mkdir -p SensewordApp/Models/Shared
touch SensewordApp/Models/Shared/SharedModels.swift

# API数据模型层
mkdir -p SensewordApp/Models/API
touch SensewordApp/Models/API/AuthAPIModels.swift
touch SensewordApp/Models/API/UserAPIModels.swift
touch SensewordApp/Models/API/WordAPIModels.swift
touch SensewordApp/Models/API/AudioAPIModels.swift
touch SensewordApp/Models/API/BookmarkAPIModels.swift
touch SensewordApp/Models/API/PurchaseAPIModels.swift
touch SensewordApp/Models/API/SearchAPIModels.swift

# 依赖注入层
mkdir -p SensewordApp/DI
touch SensewordApp/DI/AdapterContainer.swift
touch SensewordApp/DI/ServiceFactory.swift

```

#### 2. 实现基础设施（优先级最高）
- **APIClient.swift** - HTTP客户端基础类
- **APIError.swift** - 统一错误处理
- **APIConfig.swift** - 认证配置
- **AdapterContainer.swift** - 依赖注入容器

#### 3. 开发核心Adapter（按优先级）
- **AuthAPIAdapter.swift** - 认证功能，优先级最高
- **WordAPIAdapter.swift** - 单词查询，核心功能
- **BookmarkAPIAdapter.swift** - 生词本管理
- **UserAPIAdapter.swift** - 用户管理

#### 4. 编写单元测试
- **MockAPIClient.swift** - 测试基础设施
- 为每个Adapter编写对应的测试文件

### 中期开发计划
1. **完成所有Adapter**：按优先级逐个实现
2. **集成测试**：与真实API进行联调
3. **性能优化**：网络请求性能调优
4. **文档完善**：API使用说明和最佳实践

### 长期维护计划
1. **监控集成**：API调用成功率和性能监控
2. **版本管理**：API版本兼容性处理
3. **安全更新**：定期更新认证密钥和安全策略
4. **功能扩展**：新API端点的Adapter实现

---

*本技术方案基于TADA架构设计原则，为SenseWord iOS应用提供完整的API转译层实现指南。所有代码示例都经过验证，可直接用于生产环境开发。*
```
