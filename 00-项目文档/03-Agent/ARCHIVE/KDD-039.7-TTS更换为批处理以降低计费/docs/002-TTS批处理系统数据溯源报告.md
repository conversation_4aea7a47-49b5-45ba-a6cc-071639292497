# TTS批处理系统数据溯源报告

**编号**: 002  
**创建时间**: 2024-12-19  
**分析范围**: 用户提交任务 → Azure批处理完成 → 解压缩下载 → R2上传 → 数据库状态回写  
**分析目标**: 追踪关键数据在整个TTS批处理系统中的流转和变化

---

## 1. 溯源目标

本次溯源的目标是**TTS批处理系统中从用户提交任务开始到任务完成的完整数据流转过程**，重点分析：
- 关键数据结构的转换和映射关系
- 数据的单一真相来源（SSOT）
- 潜在的数据不一致风险点
- 系统优化建议

## 2. 溯源路径

### 2.1 完整数据血统路径

```
起点：tts_tasks表 (status='pending')
  ↓
阶段1：任务提交阶段
  HTTP请求 → ProductionSubmitRequest → 数据库查询 → TTSTask[]
  ↓
阶段2：分组打包阶段
  类型分组 → Record<TTSType, TTSTask[]> → 批次打包 → BatchQueueMessage[]
  ↓
阶段3：队列处理阶段
  消息验证 → ValidatedTaskBatch → orderIndex生成
  ↓
阶段4：Azure创建阶段
  Azure提交 → AzureBatchCreationResponse → taskMapping创建
  ↓
阶段5：状态监控阶段
  定期检查 → AzureBatchStatus → 本地状态同步
  ↓
阶段6：ZIP处理阶段
  ZIP下载 → 文件解压 → ExtractedAudioFile[] → orderIndex解析
  ↓
阶段7：R2上传阶段
  taskMapping映射 → ttsId还原 → R2存储 → UploadResult
  ↓
阶段8：状态回写阶段
  任务状态更新 → 计费数据生成 → 流程完成
  ↓
终点：音频文件存储在R2 + tts_tasks表 (status='completed')
```

### 2.2 详细阶段分析

#### 阶段1：任务提交阶段
- **入口**: `production-submit.ts` 的 `handleProductionSubmitRequest`
- **输入数据**: `ProductionSubmitRequest { batchSize?: number, ttsType?: TTSType }`
- **关键转换**: HTTP参数解析 → 数据库查询条件
- **输出数据**: `TTSTask[] { ttsId, text, type, status }`
- **数据来源**: `tts_tasks` 表中 `status='pending'` 的记录

#### 阶段2：分组打包阶段
- **处理器**: `BatchPackager.packageAndSubmitTasks`
- **关键转换**: 
  - 按 `TTSType` 分组：`Record<TTSType, TTSTask[]>`
  - 每50个任务打包：`BatchQueueMessage[]`
- **重要数据生成**: 
  - `batchId`: 通过 `generateBatchId()` 生成唯一标识
  - `timestamp`: 批次创建时间

#### 阶段3：队列处理阶段
- **处理器**: `batch-processing.service.ts` 的 `processQueueBatch`
- **关键转换**: 
  - 任务验证：检查 `ttsId`、`text`、`type` 完整性
  - **orderIndex生成**: 数组索引作为顺序标识（关键）
- **输出数据**: `ValidatedTaskBatch { ttsType, tasks: Array<{ ttsId, text, orderIndex }> }`

#### 阶段4：Azure创建阶段
- **处理器**: `createAzureBatch` 函数
- **关键数据生成**: 
  - **taskMapping**: `Record<string, string>` = `orderIndex → ttsId` 映射
  - Azure批处理请求：`AzureBatchRequest`
- **数据持久化**: 
  - `azure_batch_jobs` 表：存储批处理元数据和 `taskMapping`
  - `tts_tasks` 表：状态更新为 `'processing'`

#### 阶段5：状态监控阶段
- **处理器**: `monitorAzureBatchStatus` 函数
- **状态映射**: 
  - Azure `'NotStarted'/'Running'` → 本地 `'running'`
  - Azure `'Succeeded'` → 本地 `'succeeded'`
  - Azure `'Failed'` → 本地 `'failed'`
- **关键数据获取**: `outputs.result` 包含ZIP文件下载链接

#### 阶段6：ZIP处理阶段
- **处理器**: `processCompletedBatch` 函数
- **关键转换**: 
  - ZIP下载：`fetch(zipUrl) → ArrayBuffer`
  - JSZip解压：`JSZip.loadAsync(zipBuffer)`
  - **文件名解析**: `"0001.wav" → orderIndex = 1`
  - **任务映射查找**: `taskMapping[orderIndex] → ttsId`
- **输出数据**: `ExtractedAudioFile[] { filename, content, orderIndex, taskId }`

#### 阶段7：R2上传阶段
- **关键转换**: 
  - R2键名生成：`"audio/tts/{taskId}.wav"`
  - 音频数据上传：`ArrayBuffer → R2存储`
- **结果记录**: `UploadResult { successfulUploads, failedUploads, uploadedFiles, errors }`

#### 阶段8：状态回写阶段
- **处理器**: `updateTasksAndBilling` 函数
- **数据更新**: 
  - `tts_tasks` 表：`status → 'completed'`, `audio_url`, `completed_at`
  - `azure_batch_jobs` 表：`status → 'processed'`
- **计费数据**: 发送计费消息到 `BILLING_QUEUE`

## 3. 单一真相来源（SSOT）

### 3.1 任务数据SSOT
- **来源**: `tts_tasks` 表
- **职责**: 
  - 存储原始任务信息：`ttsId`, `text`, `type`, `status`
  - 记录最终结果：`audio_url`, `completed_at`
  - 维护任务生命周期状态

### 3.2 批处理状态SSOT
- **来源**: `azure_batch_jobs` 表
- **职责**: 
  - 存储批处理元数据：`batch_id`, `type`, `task_count`
  - **关键映射数据**: `task_mapping` (JSON格式)
  - 维护批处理状态：`status`, `submitted_at`, `processed_at`

### 3.3 顺序映射SSOT
- **来源**: `taskMapping` 对象
- **创建时机**: Azure批处理提交时
- **存储位置**: `azure_batch_jobs.task_mapping` 字段
- **关键作用**: 连接Azure文件顺序与原始任务ID

## 4. 问题与发现

### 4.1 数据映射风险
1. **orderIndex依赖风险**
   - **问题**: 依赖数组索引作为orderIndex，顺序变化会导致映射错误
   - **影响**: 音频文件会关联到错误的ttsId
   - **风险等级**: 高

2. **JSON序列化风险**
   - **问题**: taskMapping存储为JSON字符串
   - **风险**: JSON解析失败或数据损坏
   - **影响**: 整个批次的音频文件无法正确映射
   - **风险等级**: 高

3. **文件名解析风险**
   - **问题**: 依赖"0001.wav"格式解析orderIndex
   - **风险**: Azure改变文件命名规则
   - **影响**: 无法正确解析orderIndex，映射失败
   - **风险等级**: 中

### 4.2 状态同步风险
1. **多表状态维护**
   - **问题**: `tts_tasks.status` 和 `azure_batch_jobs.status` 存在重复
   - **风险**: 状态不一致，数据冗余
   - **影响**: 查询结果不准确，维护复杂
   - **风险等级**: 中

2. **网络故障影响**
   - **问题**: Azure状态检查依赖网络连接
   - **风险**: 网络问题导致状态同步失败
   - **影响**: 已完成的批次可能不被处理
   - **风险等级**: 中

### 4.3 错误处理粒度
1. **批次级别错误处理**
   - **问题**: 整个批次失败时所有任务都标记为失败
   - **风险**: 无法区分具体失败原因
   - **影响**: 可能重复处理已成功的任务
   - **风险等级**: 中

### 4.4 数据冗余问题
1. **时间戳冗余**
   - **问题**: `submitted_at`, `completed_at`, `processed_at` 多个时间字段
   - **影响**: 存储空间浪费，维护复杂
   - **风险等级**: 低

2. **映射数据存储方式**
   - **问题**: taskMapping存储为JSON而非关系型结构
   - **影响**: 查询困难，无法利用数据库索引
   - **风险等级**: 中

## 5. 优化建议

### 5.1 映射关系优化
**建议**: 创建独立的 `batch_task_mapping` 表
```sql
CREATE TABLE batch_task_mapping (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  batch_id TEXT NOT NULL,
  tts_id TEXT NOT NULL,
  order_index INTEGER NOT NULL,
  created_at TEXT NOT NULL,
  UNIQUE(batch_id, order_index),
  UNIQUE(batch_id, tts_id)
);
```
**优势**: 
- 支持索引查询，提高性能
- 便于数据恢复和审计
- 避免JSON解析风险

### 5.2 状态管理优化
**建议**: 引入状态机模式
```typescript
type TaskState = 'pending' | 'queued' | 'processing' | 'completed' | 'failed';
type BatchState = 'submitted' | 'running' | 'succeeded' | 'failed' | 'processed';

// 状态转换规则
const TASK_STATE_TRANSITIONS: Record<TaskState, TaskState[]> = {
  'pending': ['queued', 'failed'],
  'queued': ['processing', 'failed'],
  'processing': ['completed', 'failed'],
  'completed': [],
  'failed': ['pending'] // 允许重试
};
```

### 5.3 错误处理优化
**建议**: 实现分级错误处理
```typescript
interface ErrorClassification {
  type: 'network' | 'business' | 'system';
  severity: 'low' | 'medium' | 'high' | 'critical';
  retryable: boolean;
  maxRetries: number;
}

// 错误分类和重试策略
const ERROR_HANDLING_RULES: Record<string, ErrorClassification> = {
  'AZURE_API_TIMEOUT': { type: 'network', severity: 'medium', retryable: true, maxRetries: 3 },
  'INVALID_TASK_DATA': { type: 'business', severity: 'high', retryable: false, maxRetries: 0 },
  'R2_UPLOAD_FAILED': { type: 'system', severity: 'high', retryable: true, maxRetries: 2 }
};
```

### 5.4 监控和可观测性
**建议**: 添加数据血统追踪
```typescript
interface DataLineageEvent {
  eventId: string;
  taskId: string;
  batchId?: string;
  stage: string;
  action: string;
  inputData: any;
  outputData: any;
  timestamp: string;
  duration: number;
  success: boolean;
  errorMessage?: string;
}
```

### 5.5 数据一致性保证
**建议**: 使用数据库事务
```typescript
// 原子性更新示例
async function updateTasksAndBatchAtomic(db: D1Database, updates: TaskUpdate[]) {
  const statements = [
    // 更新任务状态
    ...updates.map(update => 
      db.prepare('UPDATE tts_tasks SET status = ?, audio_url = ?, completed_at = ? WHERE id = ?')
        .bind(update.status, update.audioUrl, update.completedAt, update.taskId)
    ),
    // 更新批处理状态
    db.prepare('UPDATE azure_batch_jobs SET status = ?, processed_at = ? WHERE batch_id = ?')
      .bind('processed', new Date().toISOString(), batchId)
  ];
  
  return await db.batch(statements);
}
```

## 6. 总结

### 6.1 系统优势
1. **设计合理**: 通过taskMapping巧妙解决了Azure批处理顺序文件与原始任务ID的映射问题
2. **数据流清晰**: 8个主要阶段，每个阶段都有明确的输入输出
3. **可扩展性**: 支持不同TTSType的批处理，易于扩展新类型

### 6.2 关键依赖
整个系统的正确性**完全依赖于taskMapping的准确性和持久性**。这是系统的核心组件，需要特别关注其：
- 生成的准确性（orderIndex的正确分配）
- 存储的可靠性（JSON序列化的完整性）
- 解析的正确性（ZIP文件处理时的映射查找）

### 6.3 优化优先级
1. **高优先级**: 映射关系结构化存储（解决核心风险）
2. **中优先级**: 状态管理优化和错误处理细化
3. **低优先级**: 监控增强和数据冗余清理

### 6.4 风险评估
- **整体风险**: 中等
- **最大风险点**: taskMapping的JSON存储方式
- **建议**: 优先实施映射关系优化，降低系统核心风险

---

**报告完成时间**: 2024-12-19  
**下一步行动**: 根据优化建议制定具体的代码改进计划