# Azure TTS 批处理 API 技术要点与踩坑指南

> **文档版本**: v1.0  
> **更新时间**: 2024-12-19  
> **适用版本**: Azure Cognitive Services Speech API 2024-04-01  

## 🚨 关键技术要点

### 1. API 端点变更（重要！）

#### ❌ 旧版本（已废弃）
```typescript
// 错误的端点 - 这是 Speech-to-Text 的批处理端点
const wrongEndpoint = `https://${region}.tts.speech.microsoft.com/cognitiveservices/v1/batchsynthesis`;
```

#### ✅ 新版本（正确）
```typescript
// 正确的 Text-to-Speech 批处理端点
const correctEndpoint = `https://${region}.api.cognitive.microsoft.com/texttospeech/batchsyntheses/${batchId}?api-version=2024-04-01`;
```

**⚠️ 踩坑警告**: 
- 旧端点 `speechtotext/v3.2/batchsynthesis` 是语音转文本的批处理，不是文本转语音！
- 新端点必须包含 `api-version=2024-04-01` 参数
- 创建批处理必须使用 `PUT` 方法，不是 `POST`

### 2. 请求格式变更

#### ❌ 旧格式
```typescript
const oldRequest = {
  displayName: "TTS Batch",
  textType: 'PlainText',  // 错误字段名
  inputs: [{ text: "hello" }],  // 错误字段名
  properties: {
    voice: { name: "en-US-JennyNeural" }  // 错误位置
  }
};
```

#### ✅ 新格式
```typescript
const newRequest = {
  description: "Batch processing for TTS tasks",
  inputKind: 'PlainText',  // 正确字段名
  inputs: [{ content: "hello" }],  // 正确字段名
  synthesisConfig: {  // 当 inputKind 为 PlainText 时必需
    voice: { name: "en-US-JennyNeural" }
  },
  properties: {
    outputFormat: "riff-24khz-16bit-mono-pcm",
    concatenateResult: false,
    wordBoundaryEnabled: false,
    sentenceBoundaryEnabled: false,
    decompressOutputFiles: false
  }
};
```

**⚠️ 踩坑警告**:
- `textType` → `inputKind`
- `inputs[].text` → `inputs[].content`
- `properties.voice` → `synthesisConfig.voice`
- `displayName` 字段已移除

### 3. HTTP 方法变更

#### ❌ 旧方法
```typescript
// 错误：使用 POST 创建批处理
fetch(endpoint, { method: 'POST', ... })
```

#### ✅ 新方法
```typescript
// 正确：使用 PUT 创建批处理，batchId 在 URL 中指定
fetch(`${baseUrl}/batchsyntheses/${batchId}?api-version=2024-04-01`, { 
  method: 'PUT', 
  ... 
})
```

## 🔧 完整实现示例

### Azure API 响应结构
```typescript
// Azure 批处理创建响应的完整类型定义
interface AzureBatchCreationResponse {
  id: string;                        // Azure批处理ID
  status: 'NotStarted' | 'Running' | 'Succeeded' | 'Failed'; // 批处理状态
  createdDateTime: string;           // 创建时间 (ISO 8601格式)
  lastActionDateTime: string;        // 最后操作时间 (ISO 8601格式)
  description?: string;              // 批处理描述
  inputKind?: 'PlainText' | 'SSML';  // 输入类型
  customProperties?: Record<string, any>; // 自定义属性
  outputs?: {
    result?: string;                 // 结果文件下载URL
    summary?: string;                // 摘要文件下载URL
  };
  synthesisConfig?: {
    voice: { name: string };
  };
  properties?: {
    outputFormat: string;
    concatenateResult: boolean;
    wordBoundaryEnabled: boolean;
    sentenceBoundaryEnabled: boolean;
    decompressOutputFiles: boolean;
  };
}
```

### 创建批处理（完整示例）
```typescript
export async function createAzureBatch(
  batchId: string,
  tasks: Array<{ content: string }>,
  voiceName: string,
  env: Env
): Promise<AzureBatchCreationResponse> {
  const azureRequest = {
    description: `Batch processing for ${tasks.length} TTS tasks`,
    inputKind: 'PlainText' as const,
    inputs: tasks,
    synthesisConfig: {
      voice: { name: voiceName }
    },
    properties: {
      outputFormat: "riff-24khz-16bit-mono-pcm",
      concatenateResult: false,
      wordBoundaryEnabled: false,
      sentenceBoundaryEnabled: false,
      decompressOutputFiles: false
    }
  };

  const response = await fetch(
    `https://${env.AZURE_TTS_REGION}.api.cognitive.microsoft.com/texttospeech/batchsyntheses/${batchId}?api-version=2024-04-01`,
    {
      method: 'PUT',
      headers: {
        'Ocp-Apim-Subscription-Key': env.AZURE_TTS_KEY,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(azureRequest)
    }
  );

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Azure batch creation failed: ${response.status} ${errorText}`);
  }

  // 返回完整的响应结构
  const result = await response.json() as AzureBatchCreationResponse;
  return result;
}
```

### 查询批处理状态
```typescript
export async function getBatchStatus(
  batchId: string,
  env: Env
): Promise<AzureBatchStatus> {
  const response = await fetch(
    `https://${env.AZURE_TTS_REGION}.api.cognitive.microsoft.com/texttospeech/batchsyntheses/${batchId}?api-version=2024-04-01`,
    {
      headers: {
        'Ocp-Apim-Subscription-Key': env.AZURE_TTS_KEY
      }
    }
  );

  if (!response.ok) {
    throw new Error(`Status check failed: ${response.status}`);
  }

  return await response.json();
}
```

## 🎯 最佳实践

### 1. 错误处理
```typescript
try {
  await createAzureBatch(batchId, tasks, voiceName, env);
} catch (error) {
  if (error.message.includes('400')) {
    // 请求格式错误，检查 inputKind 和 synthesisConfig
    console.error('Request format error:', error);
  } else if (error.message.includes('401')) {
    // 认证失败，检查 API Key
    console.error('Authentication failed:', error);
  } else if (error.message.includes('404')) {
    // 端点错误，检查 URL 格式
    console.error('Endpoint not found:', error);
  }
  throw error;
}
```

### 2. 批处理 ID 生成

```typescript
// 确保 batchId 符合要求：3-64字符，字母数字加连字符下划线，首尾必须是字母或数字
export function generateBatchId(prefix: string = 'batch'): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 9);
  return `${prefix}_${timestamp}_${random}`;
}

// 使用示例
const batchId = generateBatchId(); // 生成: "batch_1703123456789_abc123def"
```

**重要说明：**
- Azure 返回的 ID 与我们提供的 batchId 相同
- 本地生成的 batchId 作为 Azure API 的路径参数
- Azure 响应中的 `id` 字段会返回相同的值

### 3. 状态轮询
```typescript
async function waitForBatchCompletion(
  batchId: string,
  env: Env,
  maxWaitTime: number = 300000 // 5分钟
): Promise<AzureBatchStatus> {
  const startTime = Date.now();
  
  while (Date.now() - startTime < maxWaitTime) {
    const status = await getBatchStatus(batchId, env);
    
    if (status.status === 'Succeeded' || status.status === 'Failed') {
      return status;
    }
    
    // 等待 10 秒后重试
    await new Promise(resolve => setTimeout(resolve, 10000));
  }
  
  throw new Error('Batch processing timeout');
}
```

## 📚 参考资料

- [Azure Cognitive Services TTS Batch Synthesis API](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/batch-synthesis) <mcreference link="https://learn.microsoft.com/en-us/azure/ai-services/speech-service/batch-synthesis" index="1">1</mcreference>
- [Batch synthesis properties](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/batch-synthesis-properties) <mcreference link="https://learn.microsoft.com/en-us/azure/ai-services/speech-service/batch-synthesis-properties" index="3">3</mcreference>
- [Text to speech REST API reference](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/rest-text-to-speech) <mcreference link="https://learn.microsoft.com/en-us/azure/ai-services/speech-service/rest-text-to-speech" index="4">4</mcreference>

## 🔄 版本历史

- **v1.0** (2024-12-19): 初始版本，记录 API 端点变更和请求格式更新