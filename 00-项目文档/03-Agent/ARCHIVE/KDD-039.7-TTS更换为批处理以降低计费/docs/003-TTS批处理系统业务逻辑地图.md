# TTS批处理系统业务逻辑地图

> 基于KDD-039.7简化批处理方案的端到端业务流程分析
> 生成时间：2025年1月19日

## 业务流程概述

本文档展现TTS批处理系统从用户提交任务到Azure批处理完成、音频文件生成并上传到R2存储的完整端到端业务流程，严格按照实际代码调用关系和层级进行嵌套组织。

---

- **TTS批处理系统端到端处理流程**
    - **阶段1：Production Submit端点处理** (`cloudflare/workers/tts`)
        - HTTP请求到达 `/production/submit` 端点
            - 路由处理：`app.post('/production/submit', handleProductionSubmitRequest)` (from: `cloudflare/workers/tts/src/production-submit.ts`)
                - 请求参数解析：`parseRequestParams(request): { batchSize: number, ttsType?: TTSType }` (from: `cloudflare/workers/tts/src/production-submit.ts`)
                    - 验证batch_size参数（默认值：`QUEUE_CONFIG.maxBatchSize`）
                    - 验证tts_type参数（可选，使用`isValidTTSType`验证）
                - 调用优化批处理提交：`queryAndSubmitTasksOptimized(input: TaskQueryInput, env: Env): Promise<TaskSubmissionResult>` (from: `cloudflare/workers/tts/src/services/batch-processing.service.ts`)
                    - **阶段2：任务查询与Azure批处理创建**
                        - 构建SQL查询语句查询pending任务
                            - 基础查询：`SELECT ttsId, text, type FROM tts_tasks WHERE status = 'pending'`
                            - (若指定tts_type) 添加类型过滤：`AND type = ?`
                            - 排序和限制：`ORDER BY type, createdAt ASC LIMIT ?`
                        - 执行数据库查询：`env.TTS_DB.prepare(query).bind(...params).all()`
                            - (若查询结果为空) → 返回空结果：`{ queuedTasks: 0, groupedByType: {}, queueMessages: [] }`
                        - 按TTS类型分组任务：`tasksByType = new Map<TTSType, Array<{ttsId, text, orderIndex}>>()`
                        - 为每个类型直接创建Azure批处理：`createAzureBatch({ validatedBatch }, env)` (from: `cloudflare/workers/tts/src/services/batch-processing.service.ts`)
                            - **阶段3：Azure批处理创建详细流程**
                                - 生成批次ID：`generateBatchId()` (from: `cloudflare/workers/tts/src/types/realtime-tts-types.ts`)
                                - 构建Azure批处理请求：`AzureBatchRequest`
                                    - 设置描述：`description: 'Batch processing for ${tasks.length} ${ttsType} tasks'`
                                    - 设置输入类型：`inputKind: 'PlainText'`
                                    - 构建输入数据：`inputs: tasks.map(task => ({ text: task.text }))`
                                    - 配置语音合成：`synthesisConfig.voice.name = VOICE_MAPPING[ttsType]` (from: `cloudflare/workers/tts/src/types/realtime-tts-types.ts`)
                                    - 设置输出格式：`properties.outputFormat = TTS_CONFIG.AUDIO_FORMAT`
                                    - 配置其他属性：`concatenateResult: false, wordBoundaryEnabled: false, sentenceBoundaryEnabled: false, decompressOutputFiles: false`
                                - 调用Azure TTS API：`fetch('https://${env.AZURE_TTS_REGION}.api.cognitive.microsoft.com/texttospeech/batchsyntheses/${batchId}?api-version=2024-04-01')`
                                    - HTTP方法：`PUT`
                                    - 请求头：`'Ocp-Apim-Subscription-Key': env.AZURE_TTS_KEY, 'Content-Type': 'application/json'`
                                    - 请求体：`JSON.stringify(azureRequest)`
                                    - (若API调用失败) → 抛出错误：`throw new Error('Azure batch creation failed: ${response.status} ${errorText}')`
                                    - (若API调用成功) → 解析响应：`azureResult = await response.json() as AzureBatchCreationResponse`
                                - 创建任务映射：`taskMapping: Record<string, string>`
                                    - 格式：`taskMapping[task.orderIndex.toString().padStart(4, '0')] = task.ttsId`
                                    - 用途：后续ZIP文件处理时将文件名映射回具体任务ID
                                - 保存批处理记录到数据库：`env.TTS_DB.prepare('INSERT INTO azure_batch_jobs ...')`
                                    - 字段：`batchId, type, status='submitted', taskCount, submittedAt, taskMapping`
                                - 更新任务状态为processing：`env.TTS_DB.prepare('UPDATE tts_tasks SET status = 'processing', batchId = ? ...')`
                                    - 更新所有批次中的任务状态
                                    - 设置batchId关联
                                    - 更新updatedAt时间戳
                            - (若createAzureBatch成功) → 记录成功日志：`console.log('✅ 成功创建 ${ttsType} 批处理，包含 ${typeTasks.length} 个任务')`
                            - (若createAzureBatch失败) → 错误处理：
                                - 记录错误日志：`console.error('❌ 创建 ${ttsType} 批处理失败:', error)`
                                - 重置任务状态为pending：`UPDATE tts_tasks SET status = 'pending', updatedAt = datetime('now') WHERE ttsId IN (...)`
                - 构建成功响应：`createResponse(tasksFound, batchesCreated, estimatedProcessingTime)` (from: `cloudflare/workers/tts/src/production-submit.ts`)
                    - 返回格式：`{ success: true, message: '...', tasksFound: number, batchesCreated: number, estimatedProcessingTime: string, timestamp: string }`
                - (若整个流程失败) → 构建错误响应：
                    - 记录错误：`console.error('Production submit failed:', error)`
                    - 返回错误响应：`{ success: false, message: 'Failed to submit tasks: ...', tasksFound: 0, batchesCreated: 0, ... }`

    - **阶段4：定时监控与状态检查** (`cloudflare/workers/tts`)
        - 定时任务触发：每5分钟执行一次
            - 定时任务处理器：`handleScheduled(event: ScheduledEvent, env: Env, ctx: ExecutionContext): Promise<void>` (from: `cloudflare/workers/tts/src/scheduled.ts`)
                - 监控批处理状态：`monitorBatchStatus(input: BatchMonitoringInput, env: Env): Promise<ProcessingResult>` (from: `cloudflare/workers/tts/src/services/batch-processing.service.ts`)
                    - 获取进行中的批处理：`getPendingBatches(env): Promise<PendingBatchJob[]>` (from: `cloudflare/workers/tts/src/services/batch-processing.service.ts`)
                        - 查询数据库：`SELECT batchId, type, taskCount, submittedAt, taskMapping FROM azure_batch_jobs WHERE status IN ('submitted', 'running') ORDER BY submittedAt ASC`
                    - 遍历每个待处理批次：
                        - 查询Azure批处理状态：`fetch('https://${env.AZURE_TTS_REGION}.api.cognitive.microsoft.com/texttospeech/batchsyntheses/${batch.batchId}?api-version=2024-04-01')`
                            - HTTP方法：`GET`
                            - 请求头：`'Ocp-Apim-Subscription-Key': env.AZURE_TTS_KEY`
                            - (若状态查询失败) → 记录错误：`result.errors.push('Batch ${batch.batchId} monitoring failed: ...')`
                            - (若状态查询成功) → 解析状态：`azureStatus = await statusResponse.json() as AzureBatchStatus`
                        - 状态映射和数据库更新：
                            - Azure状态 → 本地状态映射：
                                - `'NotStarted'` | `'Running'` → `'running'`
                                - `'Succeeded'` → `'succeeded'`
                                - `'Failed'` → `'failed'`
                            - 更新批处理状态：`UPDATE azure_batch_jobs SET status = ?, updatedAt = datetime('now') WHERE batchId = ?`
                        - **阶段5：批处理完成处理分支**
                            - (若Azure状态为'Succeeded'且有输出结果) → 处理完成的批次：`processCompletedBatch({ zipUrl: azureStatus.outputs.result, taskMapping: JSON.parse(batch.taskMapping), batchId: batch.batchId }, env)` (from: `cloudflare/workers/tts/src/services/batch-processing.service.ts`)
                                - **ZIP文件下载与解压**
                                    - 下载ZIP文件：`fetch(zipUrl)`
                                        - (若下载失败) → 抛出错误：`throw new Error('Failed to download ZIP: ${zipResponse.status}')`
                                        - (若下载成功) → 获取文件内容：`zipBuffer = await zipResponse.arrayBuffer()`
                                    - 解压ZIP文件：`zip = await JSZip.loadAsync(zipBuffer)`
                                    - 初始化上传结果：`result: UploadResult = { successfulUploads: 0, failedUploads: 0, uploadedFiles: [], errors: [] }`
                                - **音频文件处理与R2上传**
                                    - 遍历ZIP文件中的所有文件：`for (const [filename, file] of Object.entries(zip.files))`
                                        - 跳过目录和非音频文件：`if (file.dir || !filename.endsWith('.wav')) continue`
                                        - 解析文件名获取orderIndex：`orderMatch = filename.match(/^(\d+)\.wav$/)`
                                            - (若文件名格式无效) → 记录错误：`result.errors.push({ taskId: 'unknown', error: 'Invalid filename: ${filename}' })`
                                            - (若文件名格式有效) → 提取orderIndex：`orderIndex = orderMatch[1]`
                                        - 通过taskMapping获取taskId：`taskId = taskMapping[orderIndex]`
                                            - (若找不到对应taskId) → 记录错误：`result.errors.push({ taskId: 'unknown', error: 'No task mapping for index: ${orderIndex}' })`
                                        - 获取音频文件内容：`audioContent = await file.async('arraybuffer')`
                                        - 生成R2存储键名：`r2Key = '${taskId}.wav'`
                                        - 上传到R2存储：`env.AUDIO_BUCKET.put(r2Key, audioContent, { httpMetadata: { contentType: 'audio/wav', cacheControl: TTS_CONFIG.CACHE_CONTROL } })`
                                            - (若上传成功) → 记录成功：
                                                - `result.uploadedFiles.push({ taskId, r2Key, fileSize: audioContent.byteLength })`
                                                - `result.successfulUploads++`
                                                - `console.log('✅ Successfully uploaded audio for task ${taskId}, file size: ${audioContent.byteLength} bytes')`
                                            - (若上传失败) → 记录错误：
                                                - `result.errors.push({ taskId, error: error.message })`
                                                - `result.failedUploads++`
                                                - `console.error('❌ Failed to process file ${filename}:', error)`
                                - **阶段6：任务状态更新与计费统计**
                                    - 调用状态更新：`updateTasksAndBilling({ uploadResults: [result], batchId, completedAt: new Date().toISOString() }, env)` (from: `cloudflare/workers/tts/src/services/batch-processing.service.ts`)
                                        - 更新成功上传的任务：
                                            - 遍历成功上传的文件：`for (const uploadedFile of uploadResult.uploadedFiles)`
                                                - 构建音频URL：`audioUrl = 'https://audio.senseword.app/${uploadedFile.r2Key}'`
                                                - 更新任务状态为completed：`UPDATE tts_tasks SET status = 'completed', audioUrl = ?, completedAt = ?, updatedAt = datetime('now') WHERE ttsId = ?`
                                        - 更新失败的任务：
                                            - 遍历处理错误：`for (const error of uploadResult.errors)`
                                                - (若taskId不为'unknown') → 更新任务状态为failed：`UPDATE tts_tasks SET status = 'failed', errorMessage = ?, updatedAt = datetime('now') WHERE ttsId = ?`
                                        - 更新批处理状态为processed：`UPDATE azure_batch_jobs SET status = 'processed', completedAt = ? WHERE batchId = ?`
                                        - 发送计费消息：`env.BILLING_QUEUE.send({ type: 'batch_processing', taskCount: totalTasks, batchId, timestamp: completedAt })`
                            - (若Azure状态为'Failed') → 处理失败的批次：`handleFailedBatch(batch.batchId, azureStatus.error?.message || 'Unknown error', env)`
                                - 将批次中所有任务标记为失败：`UPDATE tts_tasks SET status = 'failed', errorMessage = ?, updatedAt = datetime('now') WHERE batchId = ?`
                                - 更新批处理状态为失败：`UPDATE azure_batch_jobs SET status = 'failed', completedAt = datetime('now') WHERE batchId = ?`
                - 清理过期批处理记录：`cleanupOldBatches(env)` (from: `cloudflare/workers/tts/src/scheduled.ts`)
                    - 删除7天前的过期记录：`DELETE FROM azure_batch_jobs WHERE status IN ('processed', 'failed') AND datetime(completedAt) < datetime('now', '-7 days')`
                - 健康检查：`healthCheckBatches(env)` (from: `cloudflare/workers/tts/src/scheduled.ts`)
                    - 检查长时间未完成的批处理（超过6小时）
                    - 检查长时间未检查状态的批处理（超过1小时）
                    - 验证数据库连接状态

    - **错误处理与重试机制**
        - **任务级别错误处理**
            - Azure批处理创建失败 → 任务状态重置为'pending'以便重试
            - ZIP文件下载失败 → 整个批次处理失败
            - 单个音频文件处理失败 → 该任务标记为'failed'，其他任务继续处理
            - R2上传失败 → 任务标记为'failed'，记录错误信息
        - **批处理级别错误处理**
            - Azure批处理状态查询失败 → 记录错误，继续处理其他批次
            - Azure批处理执行失败 → 批次中所有任务标记为'failed'
        - **系统级别错误处理**
            - 数据库连接失败 → 健康检查记录，定时任务继续执行
            - 队列消息发送失败 → 记录错误日志，不影响主流程

    - **数据库表结构与状态流转**
        - **tts_tasks表**
            - 主要字段：`ttsId, text, type, status, audioUrl, batchId, errorMessage, createdAt, updatedAt, completedAt`
            - 状态流转：`pending → processing → completed/failed`
            - 重试机制：`processing → pending`（当批处理创建失败时）
        - **azure_batch_jobs表**
            - 主要字段：`batchId, type, status, taskCount, submittedAt, completedAt, taskMapping`
            - 状态流转：`submitted → running → succeeded/failed → processed`
            - taskMapping存储：`JSON.stringify({ "0001": "taskId1", "0002": "taskId2", ... })`

    - **外部服务集成**
        - **Azure TTS API集成**
            - 批处理创建：`PUT /texttospeech/batchsyntheses/{batchId}`
            - 状态查询：`GET /texttospeech/batchsyntheses/{batchId}`
            - 认证方式：`Ocp-Apim-Subscription-Key`头部
        - **R2存储集成**
            - 音频文件上传：`env.AUDIO_BUCKET.put(key, content, metadata)`
            - 访问URL格式：`https://audio.senseword.app/{taskId}.wav`
            - 缓存策略：`public, max-age=31536000`
        - **队列系统集成**
            - 计费队列：`env.BILLING_QUEUE.send(billingMessage)`
            - TTS队列：`env.TTS_QUEUE.send(batchMessage)`（在简化方案中已跳过）

---

## 系统配置与类型定义

### 关键配置常量

- **VOICE_MAPPING** (from: `cloudflare/workers/tts/src/types/realtime-tts-types.ts`)
  - 定义TTS类型到Azure语音名称的映射
  - 示例：`'en_us_male' → 'en-US-AndrewNeural'`

- **TTS_CONFIG** (from: `cloudflare/workers/tts/src/types/realtime-tts-types.ts`)
  - `AUDIO_FORMAT: 'riff-24khz-16bit-mono-pcm'`
  - `CACHE_CONTROL: 'public, max-age=31536000'`

- **QUEUE_CONFIG** (from: `cloudflare/workers/tts/src/types/realtime-tts-types.ts`)
  - `maxBatchSize`: 默认批处理大小限制

### 核心类型定义

- **TTSType**: 支持的TTS类型枚举
- **TaskStatus**: `'pending' | 'processing' | 'completed' | 'failed'`
- **BatchStatus**: `'submitted' | 'running' | 'succeeded' | 'failed' | 'processed'`
- **AzureBatchRequest**: Azure批处理API请求结构
- **AzureBatchStatus**: Azure批处理状态响应结构
- **UploadResult**: R2上传结果统计结构

---

## 架构特点与优势

### KDD-039.7简化方案特点

1. **直接批处理创建**：跳过队列机制，直接从任务查询到Azure批处理创建
2. **按类型分组处理**：自动按TTS类型分组，优化批处理效率
3. **完整错误处理**：多层级错误处理和重试机制
4. **状态追踪完整**：从任务提交到音频生成的全程状态追踪
5. **资源优化**：定时清理过期记录，健康检查机制

### 复杂度管理

通过业务逻辑地图的层次化组织，每个函数和组件都被视为可信的"黑盒"，其内部复杂性在流程理解层面被有效抽象，实现了"复杂度重置"的认知管理目标。开发者可以专注于理解组件间的交互和数据流转，而无需深入每个函数的具体实现细节。