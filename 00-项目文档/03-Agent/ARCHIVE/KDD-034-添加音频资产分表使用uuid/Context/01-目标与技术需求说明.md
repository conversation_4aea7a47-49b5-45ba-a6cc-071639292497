### **最终提示词：SenseWord数据库v2.0至v3.0迁移脚本生成器**

#### **1. 角色与使命 (Role & Mission)**

You are an expert Python developer and data engineer specializing in SQLite database migration and complex JSON data restructuring.

Your mission is to write a standalone Python script that performs a one-time migration of the SenseWord SQLite database from its current single-table `v2.0` architecture to a new, more robust, multi-table `v3.0` architecture.

The core objectives of this migration are:

1.  **Table Splitting**: Deconstruct the monolithic `words_for_publish` table into two new, normalized tables: `words` and `tts_assets`.
2.  **UUID Injection**: Parse the `contentJson` field, identify all text segments that require Text-to-Speech (TTS) audio, assign a unique and persistent UUID to each segment, and restructure the JSON accordingly.
3.  **Data Decoupling**: Populate the new `tts_assets` table with the text and their newly assigned UUIDs, creating a decoupled mapping between text and its future audio asset.
4.  **Robustness**: The script must be robust, transactional, and provide clear progress feedback.

#### **2. 输入：当前数据库架构 (v2.0)**

The script will read from a source database file named `senseword_content.db`. This database contains a single table with the following schema:

**Table: `words_for_publish`**
| 字段名 | 数据类型 |
| :--- | :--- |
| `id` | INTEGER |
| `word` | TEXT |
| `contentJson` | TEXT (A JSON string) |
| ... (and all other fields as you provided) | ... |

**`contentJson` 内部结构 (扁平化):**

```json
{
  "content": {
    "usageExamples": [
      {
        "examples": [
          {
            "learningLanguage": "This is an example sentence.",
            "phraseBreakdown": [
              {
                "phrase": "This is",
                ...
              }
            ]
          }
        ]
      }
    ]
  }
}
```

#### **3. 输出：目标数据库架构 (v3.0)**

The script will create a new database file named `senseword_content_v3.db`. This new database must contain the following two tables:

**Table 1: `words` (核心内容表)**

```sql
CREATE TABLE IF NOT EXISTS words (
    id INTEGER PRIMARY KEY,
    word TEXT NOT NULL,
    learningLanguage TEXT NOT NULL,
    scaffoldingLanguage TEXT NOT NULL,
    contentJson TEXT NOT NULL, -- This will store the RESTRUCTURED JSON
    publishStatus TEXT NOT NULL DEFAULT 'pending_upload',
    contentVersion TEXT DEFAULT 'v3.0',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    partsOfSpeech TEXT,
    ttsStatus TEXT DEFAULT 'pending',
    aiAuditScore REAL,
    aiAuditShouldRegenerate INTEGER,
    aiAuditComment TEXT,
    frequency TEXT,
    priorityScore INTEGER DEFAULT 0,
    auditStatus TEXT DEFAULT 'pending_review',
    UNIQUE(word, learningLanguage, scaffoldingLanguage)
);
```

**Table 2: `tts_assets` (TTS资产映射表)**

```sql
CREATE TABLE IF NOT EXISTS tts_assets (
    ttsId TEXT PRIMARY KEY,
    text_to_speak TEXT NOT NULL,
    learningLanguage TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    audio_url TEXT,
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**`contentJson` 重构后的目标结构 (UUID注入):**
The `learningLanguage` and `phrase` fields, which were previously strings, must be transformed into objects containing the text and a new `ttsId`.

```json
{
  "content": {
    "phoneticSymbols": [
      {
        "type": "BrE",
        "symbol": "/英式音标/",
        "ttsId": "a1b2c3d4-e5f6-7890-1234-567890abcdef"
      },
      {
        "type": "NAmE", 
        "symbol": "/美式音标/",
        "ttsId": "a1b2c3d4-e5f6-7890-1234-567890abcdef"
      }
    ],
    "usageExamples": [
      {
        "examples": [
          {
            "learningLanguage": {
              "text": "This is an example sentence.",
              "ttsId": "a1b2c3d4-e5f6-7890-1234-567890abcdef"
            },
            "phraseBreakdown": [
              {
                "phrase": {
                  "text": "This is",
                  "ttsId": "b2c3d4e5-f6a1-2345-6789-0abcdef12345"
                },
                ...
              }
            ]
          }
        ]
      }
    ]
  }
}
```

#### **4. 脚本核心逻辑与需求 (Script Requirements)**

Please write a single, well-commented Python script named `migrate_v2_to_v3.py` that performs the following steps:

1.  **Setup**:

      * Import necessary libraries: `sqlite3`, `json`, `uuid`, and `tqdm` for a progress bar.
      * Define source (`senseword_content.db`) and destination (`senseword_content_v3.db`) database file paths.
      * Establish connections to both databases.
      * Execute the `CREATE TABLE` statements for `words` and `tts_assets` in the destination database.

2.  **Migration Function**:

      * Create a main migration function.
      * Use a `try...except...finally` block to ensure database connections are closed properly.
      * Fetch all records from the `words_for_publish` table in the source database. Use `tqdm` to wrap this fetch operation to show a progress bar.

3.  **Data Transformation Loop**:

      * Inside a transaction for performance and safety (`conn_dest.execute('BEGIN TRANSACTION;')`).
      * Iterate through each row from the source table.
      * For each row:
        a.  Load the `contentJson` string into a Python dictionary.
        b.  Create an empty list to hold the new TTS asset records for this word (`tts_records_for_word = []`).
        c.  **Traverse and Restructure the JSON**:
        \* Navigate into `contentJson['content']['usageExamples']`.
        \* For each example in the `examples` array:
        \* Target the `learningLanguage` string. Generate a UUID v4 for it. Create the new object `{"text": original_string, "ttsId": new_uuid}` and replace the original string with this object. Add `(new_uuid, original_string, learningLanguage, 'pending', NULL)` to `tts_records_for_word`.
        \* Target the `phraseBreakdown` array. For each `phrase` string inside it, do the same: generate a UUID, create the new object, replace the string, and add the new TTS record to the list.
        d.  After traversing, convert the modified Python dictionary back into a JSON string.
        e.  **Insert into New Tables**:
        \* Construct and execute an `INSERT` statement for the new `words` table, using all the corresponding data from the old row and the new, restructured `contentJson` string.
        \* Execute multiple `INSERT` statements (or a single `executemany`) to insert all records from `tts_records_for_word` into the `tts_assets` table.

4.  **Finalization**:

      * Commit the transaction (`conn_dest.execute('COMMIT;')`).
      * Print a final success message with a summary (e.g., "Migration complete. Processed X words and created Y TTS assets.").
      * Include error handling to rollback the transaction if any error occurs.

Please ensure the script is clean, efficient, and contains comments explaining each major step of the logic.
