# 需求："完善 iOS TTS 状态数据更新机制" 的函数契约补间链 (V1.0)

## 0. 依赖关系与影响分析

- [重用] `AudioStatusManager.swift`: 现有的轮询机制将被扩展，添加P1完成回调机制
- [重用] `SearchService.swift`: 将复用其 `getWordContent` 方法重新获取最新单词数据
- [重用] `GlobalAudioManager.swift`: 将复用其音频缓存和预加载机制
- [修改] `WordResultView.swift`: 需要添加P1状态监听和音频预加载逻辑
- [修改] `ControlledWordResultView.swift`: 需要同步添加相同的状态监听机制

## 1. 项目文件结构概览 (Project File Structure Overview)

```
iOS/SensewordApp/
├── Services/
│   ├── AudioStatusManager.swift           # [修改] 添加P1完成回调机制
│   └── Business/
│       └── SearchService.swift            # [重用] 用于重新获取单词数据
├── Views/
│   └── WordResult/
│       └── WordResultView.swift           # [修改] 添加P1状态监听和音频预加载
└── Models/
    └── API/
        └── WordAPIModels.swift            # [重用] 现有数据结构
```

## 2. 分支策略建议

- 建议的特性分支名称: `feature/ios-tts-status-update`
- 基础分支: `main`
- 工作目录: 当前iOS项目目录

## 3. Commit 规划概要 (Commit Plan Summary & Status)

- [ ] feat(audio-manager): 添加P1阶段完成回调机制
- [ ] feat(word-view): 实现P1状态监听和音频数据更新逻辑
- [ ] feat(audio-preload): 实现例句和短语音频预加载功能
- [ ] refactor(controlled-view): 同步ControlledWordResultView的状态监听机制
- [ ] test(audio-update): 添加音频状态更新的测试验证

## 4. 技术方案蓝图

### `iOS/SensewordApp/Services/AudioStatusManager.swift`

**核心职责 (Responsibilities):**
- 扩展现有轮询机制，添加P1阶段完成的回调通知功能，使外部组件能够响应音频生成完成事件

**技术需求定义 (Technical Requirements):**
- [状态管理] 添加新的 `.p1Completed` 状态，明确区分P1阶段完成和普通ready状态
- [回调机制] 提供类型安全的单回调接口，传递单词和语言信息（当前只需支持一个组件）
- [线程安全] 确保回调在主线程执行，避免UI更新问题
- [内存管理] 使用弱引用避免循环引用，在视图消失时清理回调
- [错误隔离] 回调执行失败不应影响AudioStatusManager的正常运行

**函数/方法签名 (Function/Method Signatures):**
```swift
// 新增状态枚举值
enum AudioStatus {
    case p1Completed // P1阶段完成，所有音频已准备好
}

// 新增回调属性
var onP1Completed: ((String, String) -> Void)?
```

**数据结构定义 (Data Structures / DTOs):**
```swift
/**
 * @description P1完成回调参数
 */
typealias P1CompletionCallback = (String, String) -> Void
// 参数1: word - 单词
// 参数2: language - 语言代码
```

**伪代码实现逻辑 (Pseudocode Implementation Logic):**
1. [状态检查] 在 `performP1Poll` 方法中检测到 `all_audio_ready` 状态
2. [状态更新] 将内部状态更新为 `.p1Completed`
3. [回调触发] 使用 do-catch 包装回调执行，避免回调错误影响管理器
4. [参数传递] 使用当前的 `currentWord` 和 `currentLanguage` 调用回调
5. [日志记录] 记录P1完成和回调触发的日志信息
6. [轮询停止] 停止后续轮询，避免重复处理
7. [内存清理] 在 `stopMonitoring` 时清理回调引用

### `iOS/SensewordApp/Views/WordResult/WordResultView.swift`

**核心职责 (Responsibilities):**
- 监听AudioStatusManager的P1完成状态，自动重新获取最新的单词数据，预加载例句和短语音频，但不触发UI刷新

**技术需求定义 (Technical Requirements):**
- [状态监听] 使用 `.onChange` 监听 `audioStatusManager.status` 变化
- [数据获取] 通过 `SearchService` 重新获取最新单词数据
- [音频预加载] 添加 `GlobalAudioManager` 的公共预加载接口
- [静默更新] 使用深拷贝策略更新数据，避免触发SwiftUI的自动刷新机制
- [错误处理] 优雅处理网络请求失败和音频预加载失败
- [内存管理] 在回调中使用 `[weak self]` 避免循环引用

**函数/方法签名 (Function/Method Signatures):**
```swift
private func preloadUpdatedAudioData() async
private func preloadExampleAndPhraseAudio(from wordData: WordDefinitionResponse) async
private func updateAudioUrlsSilently(from updatedWordData: WordDefinitionResponse) async

// 新增：GlobalAudioManager 扩展
extension GlobalAudioManager {
    public func preloadAudioData(from url: URL) async throws -> Data
}
```

**数据结构定义 (Data Structures / DTOs):**
```swift
/**
 * @description 音频预加载任务
 */
struct AudioPreloadTask {
    let audioUrl: String
    let description: String
    let type: AudioType
}

enum AudioType {
    case example
    case phrase
}
```

**伪代码实现逻辑 (Pseudocode Implementation Logic):**

#### preloadUpdatedAudioData():
1. [数据获取] 调用 `searchService.getWordContent` 重新获取最新单词数据
2. [音频预加载] 调用 `preloadExampleAndPhraseAudio` 预加载音频
3. [静默更新] 调用 `updateAudioUrlsSilently` 更新本地数据
4. [错误处理] 捕获并记录任何异常，不影响用户体验

#### preloadExampleAndPhraseAudio():
1. [URL收集] 遍历 `usageExamples` 收集所有例句的 `audioUrl`
2. [短语收集] 遍历每个例句的 `phraseBreakdown` 收集短语音频URL
3. [去重处理] 移除重复的音频URL
4. [并发预加载] 使用 `GlobalAudioManager.getAudioData` 预加载音频到缓存
5. [进度记录] 记录预加载成功和失败的音频数量

#### updateAudioUrlsSilently():
1. [深拷贝策略] 创建 `wordData` 的深拷贝，避免直接修改 `@State` 对象
2. [结构对比] 按索引对比当前数据和更新数据的结构
3. [URL更新] 在拷贝对象中更新 `usageExamples` 的 `audioUrl` 字段
4. [短语更新] 在拷贝对象中更新 `phraseBreakdown` 的音频URL
5. [批量替换] 一次性替换整个 `wordData` 对象，最小化UI更新触发

### `iOS/SensewordApp/Views/WordResult/ControlledWordResultView.swift`

**核心职责 (Responsibilities):**
- 同步WordResultView的P1状态监听机制，确保无限内容流中的单词卡片也能获得音频更新

**技术需求定义 (Technical Requirements):**
- [一致性] 与WordResultView保持相同的状态监听和预加载逻辑
- [性能优化] 考虑无限滚动场景，避免过度的网络请求
- [内存管理] 及时释放不再需要的音频缓存

**函数/方法签名 (Function/Method Signatures):**
```swift
// 与WordResultView相同的方法签名
private func preloadUpdatedAudioData() async
private func preloadExampleAndPhraseAudio(from wordData: WordDefinitionResponse) async
private func updateAudioUrlsSilently(from updatedWordData: WordDefinitionResponse) async
```

**伪代码实现逻辑 (Pseudocode Implementation Logic):**
1. [代码复用] 复制WordResultView中的相同实现
2. [性能考虑] 添加防抖机制，避免频繁的API调用
3. [生命周期] 在视图消失时取消正在进行的预加载任务

### `iOS/SensewordApp/Services/GlobalAudioManager.swift` (扩展)

**核心职责 (Responsibilities):**
- 添加公共的音频预加载接口，支持外部组件进行音频缓存预加载

**技术需求定义 (Technical Requirements):**
- [接口暴露] 将私有的 `getAudioData` 方法包装为公共接口
- [错误处理] 提供详细的错误信息和日志记录
- [缓存优化] 复用现有的音频缓存机制

**函数/方法签名 (Function/Method Signatures):**
```swift
public func preloadAudioData(from url: URL) async throws -> Data
```

**伪代码实现逻辑 (Pseudocode Implementation Logic):**
1. [参数验证] 验证URL的有效性
2. [缓存检查] 检查音频是否已在缓存中
3. [下载处理] 如果不在缓存中，则下载并缓存
4. [返回数据] 返回音频数据，供调用方确认预加载成功

## 5. AI Agent 需要了解的文件上下文

<context_files>
iOS/SensewordApp/Services/AudioStatusManager.swift
iOS/SensewordApp/Services/Business/SearchService.swift
iOS/SensewordApp/Services/GlobalAudioManager.swift
iOS/SensewordApp/Views/WordResult/WordResultView.swift
iOS/SensewordApp/Views/WordResult/ControlledWordResultView.swift
iOS/SensewordApp/Models/API/WordAPIModels.swift
iOS/SensewordApp/Services/Adapters/WordAPIAdapter.swift
iOS/SensewordApp/Network/APIClient.swift
</context_files>

## X. 冲突检查报告

### X.1 Adapter能力接口验证 ✅

#### SearchService验证结果
- **✅ 方法存在**: `getWordContent(word: String, language: LanguageCode) async throws -> WordContentResponse`
- **✅ 协议匹配**: WordAPIAdapterProtocol已定义并实现
- **✅ 返回类型**: WordDefinitionResponse确实存在于WordAPIModels.swift
- **✅ 参数类型**: LanguageCode已在SharedModels.swift中定义

#### GlobalAudioManager验证结果
- **✅ 方法存在**: `getAudioData(from url: URL) async throws -> Data`
- **✅ 缓存机制**: 已实现音频缓存功能
- **✅ 预加载支持**: 支持批量音频预加载

### X.2 数据模型验证 ✅

#### 现有数据模型确认
- **✅ WordDefinitionResponse**: 已实现，包含word、metadata、content字段
- **✅ UsageExample**: 已实现，包含english、translation、audioUrl、phraseBreakdown字段
- **✅ PhraseBreakdown**: 已实现，包含phrase、translation、audioUrl字段

#### 新增数据模型需求
- **⚠️ AudioPreloadTask**: 需要新增，技术方案中已定义结构
- **⚠️ P1CompletionCallback**: 需要新增，技术方案中已定义类型别名

### X.3 架构兼容性验证 ✅

#### MVVM架构一致性
- **✅ 视图层职责**: 现有实现严格遵循视图层原则，无业务逻辑泄露
- **✅ 数据流向**: @StateObject -> @Published -> UI更新的数据流清晰
- **✅ 依赖注入**: 现有服务层架构可平滑集成新的回调机制

#### SwiftUI兼容性
- **✅ 状态管理**: 项目使用SwiftUI @State和@Published，支持响应式更新
- **✅ 异步处理**: 支持async/await和Task并发处理
- **✅ 生命周期**: .onAppear和.onChange修饰符已广泛使用

### X.4 潜在风险识别 ⚠️

#### 中等风险项
1. **内存泄漏风险**: 回调闭包可能导致循环引用，建议使用[weak self]
2. **并发安全**: 多个视图同时触发音频预加载可能导致竞态条件

#### 低风险项
1. **网络开销**: CDN缓存可以有效减少重复请求的影响
2. **UI性能**: 静默更新机制避免了不必要的UI刷新

### X.5 修正建议 📝

#### 技术方案微调
1. **内存管理**: 在回调中使用 `[weak self]`，在 `stopMonitoring` 时清理回调
2. **错误隔离**: 使用 do-catch 包装回调执行，避免回调错误影响AudioStatusManager
3. **静默更新**: 使用深拷贝策略避免触发SwiftUI自动刷新机制
4. **接口扩展**: 为GlobalAudioManager添加公共预加载接口
5. **性能优化**: 考虑添加防抖机制，避免频繁的API调用

#### 实现优先级调整
1. **优先级1**: GlobalAudioManager公共接口扩展
2. **优先级2**: AudioStatusManager回调机制完善（添加错误隔离和内存管理）
3. **优先级3**: WordResultView状态监听和预加载（使用深拷贝策略）
4. **优先级4**: ControlledWordResultView同步实现
5. **优先级5**: 测试验证和性能优化

### X.6 结论 ✅

**技术方案与现有项目实现高度兼容**，主要发现：

1. **✅ 架构兼容**: 方案完全符合现有MVVM架构和SwiftUI最佳实践
2. **✅ 接口可用**: 所需的API接口和数据模型均已存在且可用
3. **✅ 技术栈匹配**: 使用的技术栈与项目现有实现完全一致
4. **⚠️ 内存管理**: 需要注意回调闭包的内存管理，避免循环引用
5. **📈 用户体验**: 方案将显著提升音频播放的流畅度和响应速度

**建议继续技术方案**，按照优化后的优先级顺序实施，重点关注：
1. **接口扩展**: 优先添加GlobalAudioManager公共接口
2. **内存安全**: 使用弱引用和错误隔离机制
3. **静默更新**: 采用深拷贝策略避免意外的UI刷新
4. **工程实践**: 遵循iOS开发最佳实践，确保代码质量和可维护性