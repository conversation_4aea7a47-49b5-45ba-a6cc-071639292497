# KDD-TTS-优化：分阶段TTS音频处理系统

## 1. 项目概述

### 核心目标
将TTS音频处理从单一异步流程优化为两阶段处理：
1. **P0阶段**：单词发音音频与单词数据同步下发
2. **P1阶段**：例句和短语音频异步处理，延长轮询间隔

### 技术原理
- 单词发音是核心功能，应立即可用
- 例句短语是增强功能，可延迟处理
- 分离关键路径和非关键路径，提升用户体验

---

## 2. 关键帧数据结构定义

### 关键帧A：AI生成完成状态
```typescript
interface AIGeneratedWordContent {
  word: string;
  content: {
    phoneticSymbols: Array<{
      type: 'BrE' | 'NAmE';
      symbol: string;
      audioUrl: string; // 空字符串，待P0阶段填充
    }>;
    usageExamples: Array<{
      examples: Array<{
        english: string;
        translation: string;
        audioUrl: string; // 空字符串，待P1阶段填充
        phraseBreakdown?: Array<{
          phrase: string;
          translation: string;
          audioUrl: string; // 空字符串，待P1阶段填充
        }>;
      }>;
    }>;
  };
  ttsStatus: 'ai_generated'; // 新状态
}
```

### 关键帧B：P0阶段完成状态
```typescript
interface WordWithP0Audio {
  word: string;
  content: {
    phoneticSymbols: Array<{
      type: 'BrE' | 'NAmE';
      symbol: string;
      audioUrl: string; // 已填充音频URL
    }>;
    usageExamples: Array<{
      examples: Array<{
        english: string;
        translation: string;
        audioUrl: string; // 仍为空字符串
        phraseBreakdown?: Array<{
          phrase: string;
          translation: string;
          audioUrl: string; // 仍为空字符串
        }>;
      }>;
    }>;
  };
  ttsStatus: 'word_audio_ready'; // P0完成状态
}
```

### 关键帧C：P1阶段完成状态
```typescript
interface WordWithAllAudio {
  word: string;
  content: {
    phoneticSymbols: Array<{
      type: 'BrE' | 'NAmE';
      symbol: string;
      audioUrl: string; // 已有音频URL
    }>;
    usageExamples: Array<{
      examples: Array<{
        english: string;
        translation: string;
        audioUrl: string; // 已填充音频URL
        phraseBreakdown?: Array<{
          phrase: string;
          translation: string;
          audioUrl: string; // 已填充音频URL
        }>;
      }>;
    }>;
  };
  ttsStatus: 'all_audio_ready'; // P1完成状态
}
```

---

## 3. 函数契约补间链

### [FC-TTS-01]: AI内容标准化增强服务
- **职责**: 在AI生成后立即为音频字段预设空字符串
- **函数签名**: `standardizeAIContentWithAudioPlaceholders(aiContent: AIGeneratedContent): AIGeneratedContent`
- **所在文件**: `cloudflare/workers/api/src/services/ai.service.ts`

>>>>> **输入**: AI原始生成内容
```typescript
interface AIGeneratedContent {
  word: string;
  content: WordContent; // 可能缺少audioUrl字段
}
```

<<<<< **输出**: 标准化后的AI内容
```typescript
interface AIGeneratedContent {
  word: string;
  content: WordContent; // 所有audioUrl字段已预设为空字符串
  ttsStatus: 'ai_generated';
}
```

### [FC-TTS-02]: P0阶段同步音频生成服务
- **职责**: 在单词保存前同步生成单词发音音频
- **函数签名**: `generateWordPronunciationAudio(content: WordContent, word: string, env: Env): Promise<WordContent>`
- **所在文件**: `cloudflare/workers/api/src/services/word-audio.service.ts` (新建)

>>>>> **输入**: 标准化AI内容
```typescript
interface WordAudioGenerationInput {
  word: string;
  content: WordContent;
  env: Env;
}
```

<<<<< **输出**: 填充单词音频的内容
```typescript
interface WordContent {
  phoneticSymbols: Array<{
    type: 'BrE' | 'NAmE';
    symbol: string;
    audioUrl: string; // 已填充实际音频URL
  }>;
  // 其他字段保持不变
}
```

### [FC-TTS-03]: 数据库保存增强服务
- **职责**: 保存带有P0音频的单词数据，设置正确的TTS状态
- **函数签名**: `saveWordDefinitionWithP0Audio(db: any, content: AIGeneratedContent, language: string): Promise<boolean>`
- **所在文件**: `cloudflare/workers/api/src/services/word.service.ts`

>>>>> **输入**: 包含P0音频的内容
```typescript
interface SaveWordWithP0AudioInput {
  db: any;
  content: AIGeneratedContent; // phoneticSymbols已有audioUrl
  language: string;
}
```

<<<<< **输出**: 保存结果
```typescript
interface SaveResult {
  success: boolean;
  ttsStatus: 'word_audio_ready';
}
```

### [FC-TTS-04]: P1阶段异步触发服务
- **职责**: 延迟触发例句和短语音频生成
- **函数签名**: `triggerP1AudioGeneration(word: string, language: string, env: Env): Promise<void>`
- **所在文件**: `cloudflare/workers/api/src/services/word.service.ts`

>>>>> **输入**: 单词信息
```typescript
interface P1TriggerInput {
  word: string;
  language: string;
  delaySeconds: number; // 50秒延迟
}
```

<<<<< **输出**: 无返回值
```typescript
// void - 异步处理
```

### [FC-TTS-05]: TTS Worker P1处理服务
- **职责**: 专门处理例句和短语音频生成
- **函数签名**: `generateP1Audio(record: DatabaseRecord, env: TTSEnv): Promise<boolean>`
- **所在文件**: `cloudflare/workers/tts/src/services/audio.service.ts`

>>>>> **输入**: 数据库记录
```typescript
interface DatabaseRecord {
  word: string;
  language: string;
  contentJson: string;
  ttsStatus: 'word_audio_ready';
}
```

<<<<< **输出**: 处理结果
```typescript
interface P1AudioResult {
  success: boolean;
  newStatus: 'all_audio_ready' | 'word_audio_ready';
}
```

### [FC-TTS-06]: iOS轮询优化服务
- **职责**: 优化音频状态轮询逻辑，延长P1阶段轮询间隔
- **函数签名**: `startOptimizedAudioMonitoring(word: string, language: string, hasWordAudio: boolean): void`
- **所在文件**: `iOS/SensewordApp/Services/AudioStatusManager.swift`

>>>>> **输入**: 监控参数
```typescript
interface AudioMonitoringInput {
  word: string;
  language: string;
  hasWordAudio: boolean; // 是否已有单词音频
}
```

<<<<< **输出**: 无返回值
```typescript
// void - 状态管理
```

---

## 4. 实施计划

### Commit规划

#### Commit 1: 后端P0音频同步生成
- 创建word-audio.service.ts
- 修改ai.service.ts标准化逻辑
- 修改word.service.ts保存逻辑
- 更新TTS状态枚举

#### Commit 2: TTS Worker P1处理优化
- 修改audio.service.ts分离P0/P1逻辑
- 更新TTS Worker路由处理
- 优化数据库状态管理

#### Commit 3: iOS端轮询优化
- 修改AudioStatusManager轮询逻辑
- 更新音频状态枚举
- 优化UI状态显示

---

## 5. 上下文文件

### 需要修改的核心文件
- `cloudflare/workers/api/src/services/ai.service.ts`
- `cloudflare/workers/api/src/services/word.service.ts`
- `cloudflare/workers/tts/src/services/audio.service.ts`
- `cloudflare/workers/tts/src/index.ts`
- `iOS/SensewordApp/Services/AudioStatusManager.swift`

### 需要创建的新文件
- `cloudflare/workers/api/src/services/word-audio.service.ts`

### 数据库迁移
- 更新TTS状态枚举值
- 添加P0/P1阶段状态支持
