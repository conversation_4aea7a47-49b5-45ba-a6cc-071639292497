# SenseWord 单词查询与生成系统 - 函数契约补间链 (V1.0)

## 1. 项目文件结构概览 (Project File Structure Overview)

```
SenseWord-iOS/
├── cloudflare/
│   ├── d1/
│   │   └── migrations/
│   │       └── 0001_create_word_definitions.sql     # [新增] D1数据库表结构
│   └── workers/
│       └── api/
│           ├── src/
│           │   ├── index.ts                         # [新增] Worker主入口和路由
│           │   ├── types/
│           │   │   └── word-types.ts                # [新增] 后端数据类型定义
│           │   └── services/
│           │       ├── word.service.ts              # [新增] 单词查询与生成服务
│           │       └── ai.service.ts                # [新增] AI内容生成服务
│           ├── assets/                              # [新增] 静态资源目录
│           │   └── prompts/
│           │       └── senseword_ai-v7.md          # [新增] AI提示词文件
│           ├── package.json                         # [新增] Worker依赖配置
│           └── wrangler.toml                        # [新增] Cloudflare配置
├── iOS/
│   └── Packages/
│       └── SharedModels/
│           └── Sources/
│               └── SharedModels/
│                   ├── WordModels.swift             # [重写] 基于后端类型的Swift模型
│                   └── APIService.swift             # [重写] 简化的API服务
└── 0-KDD - 关键帧驱动开发/
    └── 02-KDD/
        └── 00-KDD-Package-001/
            ├── 01-函数契约补间链.md                 # [当前文件]
            ├── 02-补间测试报告.md                   # [待创建]
            ├── 03-关键帧可视化.md                   # [待创建]
            └── 04-进度日志.md                       # [待创建]
```

## 2. 分支策略建议

- **建议的特性分支名称**: `feature/word-query-generation-system`
- **建议的 git worktree 文件路径**: `/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/03-word-system-worktree`
- **基础分支**: `main`
- **分支创建模拟命令行**:
    ```bash
    # 概念性命令，用于记录和指导
    # git checkout main
    # git pull origin main
    # git worktree add /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/03-word-system-worktree -b feature/word-query-generation-system main
    ```

## 3. Commit 规划概要 (Commit Plan Summary & Status)

- [x] refactor(cleanup): 清理过时的协议文件和重复的iOS模型定义 ✅ **已完成** (d61024b)
- [x] chore(db): 创建word_definitions表结构和索引 ✅ **已实现并提交**
- [x] feat(backend): 实现后端数据类型定义 ✅ **已实现并提交**
- [x] feat(backend): 实现提示词预处理和语言映射服务 ✅ **已实现并提交**
- [x] feat(backend): 实现Gemini AI内容生成服务 ✅ **已实现并提交**
- [x] feat(backend): 实现单词查询与生成核心服务 ✅ **已实现并提交**
- [x] feat(backend): 创建Worker API入口和路由 ✅ **已实现并提交**
- [x] feat(ios): 重写SharedModels中的Word数据模型 ✅ **已实现并提交**
- [x] feat(ios): 实现简化的API服务客户端 ✅ **已实现并提交**
- [x] fix(config): 修复Wrangler配置和编译验证 ✅ **已实现并提交**
- [x] feat(backend): 实现用户反馈API端点和数据库更新服务 ✅ **已实现并提交**
- [x] feat(backend): 配置Assets静态资源和提示词文件管理 ✅ **已实现并提交** (7a71698)
- [ ] test(backend): 添加单词生成流程的补间测试 ⏳ **待实施**
- [ ] test(ios): 添加API服务和数据模型测试 ⏳ **待实施**

## 4. 函数契约补间链 (Function Contract Tweening Chain)

### [FC-01]: iOS API客户端请求发起器

- **职责**: 从iOS应用发起单词查询请求，处理网络通信和错误处理
- **函数签名**: `fetchWord(word: String, language: SupportedLanguage) async throws -> WordDefinitionResponse`
- **所在文件**: `iOS/Packages/SharedModels/Sources/SharedModels/APIService.swift`

>>>>> **输入 (Input)**: `WordQueryRequest`

```swift
// iOS语言枚举定义，确保与后端一致 - 扩展更多语言支持
enum SupportedLanguage: String, CaseIterable, Codable {
    // 核心语言
    case english = "en"
    case chinese = "zh"
    case japanese = "ja"
    case german = "de"
    case french = "fr"
    case spanish = "es"

    // 新增亚洲语言
    case korean = "ko"
    case russian = "ru"
    case arabic = "ar"
    case hindi = "hi"
    case thai = "th"
    case vietnamese = "vi"
    case turkish = "tr"
    case indonesian = "id"

    // 新增欧洲语言
    case italian = "it"
    case portuguese = "pt"
    case polish = "pl"
    case dutch = "nl"
    case swedish = "sv"
    case danish = "da"
    case norwegian = "no"
    case finnish = "fi"

    var displayName: String {
        switch self {
        case .english: return "English"
        case .chinese: return "中文"
        case .japanese: return "日本語"
        case .german: return "Deutsch"
        case .french: return "Français"
        case .spanish: return "Español"
        case .indonesian: return "Bahasa Indonesia"
        // ... 其他语言的显示名称
        default: return rawValue.uppercased()
        }
    }

    var backendLanguageName: String {
        switch self {
        case .chinese: return "Chinese"
        case .japanese: return "Japanese"
        case .german: return "German"
        case .french: return "French"
        case .spanish: return "Spanish"
        }
    }
}

struct WordQueryRequest {
    let word: String                    // 要查询的英语单词，如 "progressive"
    let language: SupportedLanguage     // 目标语言枚举，如 .chinese
}
```

<<<<< **输出 (Output)**: `WordDefinitionResponse`

```swift
struct WordDefinitionResponse: Codable {
    let word: String
    let metadata: WordMetadata
    let content: WordContent
}

struct WordMetadata: Codable {
    let wordFrequency: String  // "High", "Medium", "Low", "Rare"
    let relatedConcepts: [String]
}

struct WordContent: Codable {
    let difficulty: String  // CEFR等级 "A1"-"C2"
    let phoneticSymbols: [PhoneticSymbol]
    let coreDefinition: String
    let contextualExplanation: ContextualExplanation
    let usageExamples: [UsageExample]
    let usageScenarios: [UsageScenario]
    let collocations: [Collocation]
    let usageNotes: [UsageNote]
    let synonyms: [Synonym]
}
```

---

### [FC-02]: Cloudflare Worker API端点处理器

- **职责**: 接收HTTP请求，验证参数，调用核心服务，返回标准化响应
- **函数签名**: `handleWordQuery(request: Request, env: Env): Promise<Response>`
- **所在文件**: `cloudflare/workers/api/src/index.ts`

>>>>> **输入 (Input)**: `Request`

```typescript
// GET /api/v1/word/{wordName}?lang={langCode}
// Headers: { 'X-Static-API-Key': '...' }
interface RequestContext {
    params: { wordName: string }
    query: { lang: string }
    headers: { 'X-Static-API-Key': string }
}
```

<<<<< **输出 (Output)**: `Response`

```typescript
// HTTP 200 Success Response
interface SuccessResponse {
    word: string
    metadata: {
        wordFrequency: 'High' | 'Medium' | 'Low' | 'Rare'
        relatedConcepts: string[]
    }
    content: WordContentBE  // 完整的单词内容对象
}

// HTTP 4xx/5xx Error Response
interface ErrorResponse {
    error: {
        code: 'INVALID_WORD' | 'AI_GENERATION_FAILED' | 'DATABASE_ERROR'
        message: string
    }
}
```

---

### [FC-03]: 数据库查询服务

- **职责**: 查询D1数据库，检查指定单词和语言的定义是否已存在
- **函数签名**: `findWordDefinition(db: D1Database, word: string, language: string): Promise<WordDefinitionRecord | null>`
- **所在文件**: `cloudflare/workers/api/src/services/word.service.ts`

>>>>> **输入 (Input)**: `DatabaseQueryParams`

```typescript
interface DatabaseQueryParams {
    word: string      // "progressive"
    language: string  // "zh"
}
```

<<<<< **输出 (Output)**: `WordDefinitionRecord | null`

```typescript
// 数据库记录结构（如果找到）
interface WordDefinitionRecord {
    word: string
    language: string
    contentJson: string              // JSON字符串形式的完整内容
    feedbackScore: number           // 用户反馈评分
    isHumanReviewed: number         // 0或1，是否人工审核
    ttsStatus: string               // 'pending' | 'completed' | 'failed'
    difficulty: string              // CEFR等级
    frequency: string               // 词频等级
    relatedConcepts: string         // JSON字符串形式的相关概念数组
    promptVersion: string           // 提示词版本
    createdAt: string              // ISO时间戳
    updatedAt: string              // ISO时间戳
}

// 如果未找到则返回 null
```

---

### [FC-04]: Gemini AI内容生成服务

- **职责**: 调用Gemini AI模型生成单词的完整教学内容和元数据
- **函数签名**: `generateWordContent(word: string, language: string): Promise<AIGeneratedContent>`
- **所在文件**: `cloudflare/workers/api/src/services/ai.service.ts`
- **AI模型**: `gemini-2.5-flash-lite-preview-06-17`
- **API端点**: `https://generativelanguage.googleapis.com/v1beta/models/{MODEL_ID}:streamGenerateContent`

>>>>> **输入 (Input)**: `AIGenerationRequest`

```typescript
interface AIGenerationRequest {
    word: string                    // "progressive"
    targetAudienceLanguage: string  // "zh"
}

// 语言代码映射表
interface LanguageMapping {
    [key: string]: string
    zh: "Chinese"
    ja: "Japanese"
    de: "German"
    fr: "French"
    es: "Spanish"
    // 更多语言映射...
}

// Gemini API请求格式
interface GeminiRequest {
    contents: Array<{
        role: "user"
        parts: Array<{
            text: string  // 包含处理后的SenseWord v7.0提示词和单词信息
        }>
    }>
    generationConfig: {
        thinkingConfig: {
            thinkingBudget: 0
        }
        responseMimeType: "application/json"
    }
}

// 提示词处理流程
interface PromptProcessing {
    originalPrompt: string      // 从senseword_ai-v7.md读取的原始提示词
    languageCode: string        // "zh"
    languageName: string        // "Chinese" (通过映射表获得)
    processedPrompt: string     // 替换{{targetAudienceLanguage}}后的提示词
    finalInput: string          // 组合提示词和单词信息的最终输入
}
```

<<<<< **输出 (Output)**: `AIGeneratedContent`

```typescript
// AI生成的完整内容结构
interface AIGeneratedContent {
    word: string
    metadata: {
        wordFrequency: 'High' | 'Medium' | 'Low' | 'Rare'
        relatedConcepts: string[]
    }
    content: {
        difficulty: string  // CEFR等级
        phoneticSymbols: Array<{
            type: 'BrE' | 'NAmE'
            symbol: string
        }>
        coreDefinition: string
        contextualExplanation: {
            nativeSpeakerIntent: string
            emotionalResonance: string
            vividImagery: string
            etymologicalEssence: string
        }
        usageExamples: Array<{
            category: string
            examples: Array<{
                english: string
                translation: string
                phraseBreakdown: Array<{
                    phrase: string
                    translation: string
                }>
            }>
        }>
        usageScenarios: Array<{
            category: string
            relevance: string
            context: string
        }>
        collocations: Array<{
            type: string
            pattern: string
            examples: Array<{
                collocation: string
                translation: string
            }>
        }>
        usageNotes: Array<{
            aspect: string
            explanation: string
            examples: Array<{
                sentence: string
                translation: string
            }>
        }>
        synonyms: Array<{
            word: string
            explanation: string
            examples: Array<{
                sentence: string
                translation: string
            }>
        }>
    }
}
```

---

### [FC-04.1]: 提示词预处理服务

- **职责**: 从Assets静态资源读取提示词文件，处理语言代码映射和提示词占位符替换
- **函数签名**: `processPromptForGemini(word: string, languageCode: string, env: Env): Promise<string>`
- **所在文件**: `cloudflare/workers/api/src/services/ai.service.ts`
- **依赖资源**: `cloudflare/workers/api/assets/prompts/senseword_ai-v7.md`

>>>>> **输入 (Input)**: `PromptProcessingRequest`

```typescript
interface PromptProcessingRequest {
    word: string         // "progressive"
    languageCode: string // "zh"
    env: Env            // Worker环境，包含ASSETS绑定
}
```

<<<<< **输出 (Output)**: `string`

```typescript
// 处理后的完整提示词，包含：
// 1. 从 env.ASSETS.fetch('/prompts/senseword_ai-v7.md') 读取的原始提示词
// 2. 将{{targetAudienceLanguage}}替换为实际语言名称（如"Chinese"）
// 3. 在提示词末尾添加具体的单词查询JSON
//
// Assets配置要求：
// - wrangler.toml中配置assets目录绑定
// - 提示词文件位于 assets/prompts/senseword_ai-v7.md
//
// 示例输出：
// "# Final Prompt: The SenseWord AI Engine...(完整提示词)...
//  请分析以下单词：{\"word\": \"progressive\", \"targetAudienceLanguage\": \"Chinese\"}"
```

---

### [FC-06]: 数据库写入服务

- **职责**: 将AI生成的内容组装并保存到D1数据库
- **函数签名**: `saveWordDefinition(db: D1Database, aiContent: AIGeneratedContent): Promise<boolean>`
- **所在文件**: `cloudflare/workers/api/src/services/word.service.ts`

>>>>> **输入 (Input)**: `AIGeneratedContent`

与[FC-04]的输出相同，AI生成的完整内容结构。

<<<<< **输出 (Output)**: `boolean`

```typescript
// 返回保存操作是否成功
true  // 成功保存到数据库
false // 保存失败
```

---

### [FC-07]: 响应格式化服务

- **职责**: 将数据库记录或AI生成内容转换为标准API响应格式
- **函数签名**: `formatWordResponse(source: WordDefinitionRecord | AIGeneratedContent): WordDefinitionResponse`
- **所在文件**: `cloudflare/workers/api/src/services/word.service.ts`

>>>>> **输入 (Input)**: `WordDefinitionRecord | AIGeneratedContent`

可以是数据库记录（已存在的单词）或AI生成内容（新生成的单词）。

<<<<< **输出 (Output)**: `WordDefinitionResponse`

```typescript
// 标准化的API响应格式
interface WordDefinitionResponse {
    word: string
    metadata: {
        wordFrequency: 'High' | 'Medium' | 'Low' | 'Rare'
        relatedConcepts: string[]
    }
    content: {
        difficulty: string
        phoneticSymbols: Array<{
            type: 'BrE' | 'NAmE'
            symbol: string
        }>
        coreDefinition: string
        contextualExplanation: {
            nativeSpeakerIntent: string
            emotionalResonance: string
            vividImagery: string
            etymologicalEssence: string
        }
        usageExamples: Array<{
            category: string
            examples: Array<{
                english: string
                translation: string
                phraseBreakdown: Array<{
                    phrase: string
                    translation: string
                }>
            }>
        }>
        usageScenarios: Array<{
            category: string
            relevance: string
            context: string
        }>
        collocations: Array<{
            type: string
            pattern: string
            examples: Array<{
                collocation: string
                translation: string
            }>
        }>
        usageNotes: Array<{
            aspect: string
            explanation: string
            examples: Array<{
                sentence: string
                translation: string
            }>
        }>
        synonyms: Array<{
            word: string
            explanation: string
            examples: Array<{
                sentence: string
                translation: string
            }>
        }>
    }
}
```

### [FC-08]: 用户反馈API端点处理器

- **职责**: 接收用户对单词解析的反馈（赞/踩），验证参数和认证，调用数据库更新服务
- **函数签名**: `handleFeedback(request: Request, env: Env): Promise<Response>`
- **所在文件**: `cloudflare/workers/api/src/index.ts`

>>>>> **输入 (Input)**: `Request`

```typescript
// POST /api/v1/feedback
// Headers: { 
//   'Content-Type': 'application/json',
//   'X-Static-API-Key': '...',
//   'X-Dynamic-Key': '...' 
// }
interface FeedbackRequestContext {
    body: FeedbackRequest
    headers: { 
        'X-Static-API-Key': string
        'X-Dynamic-Key': string
    }
}

interface FeedbackRequest {
    word: string                    // 要反馈的单词，如 "progressive"
    language: string               // 语言代码，如 "zh"
    action: 'like' | 'dislike'    // 反馈动作：赞或踩
}
```

<<<<< **输出 (Output)**: `Response`

```typescript
// HTTP 200 Success Response
interface FeedbackSuccessResponse {
    success: true
    newScore: number              // 更新后的反馈分数
    message: string              // 成功消息
}

// HTTP 4xx/5xx Error Response
interface FeedbackErrorResponse {
    error: {
        code: 'INVALID_WORD' | 'INVALID_ACTION' | 'DATABASE_ERROR' | 'AUTHENTICATION_REQUIRED'
        message: string
    }
}
```

---

### [FC-09]: 用户反馈数据库更新服务

- **职责**: 根据用户反馈更新数据库中的feedbackScore字段
- **函数签名**: `updateFeedbackScore(db: D1Database, word: string, language: string, action: 'like' | 'dislike'): Promise<number | null>`
- **所在文件**: `cloudflare/workers/api/src/services/word.service.ts`

>>>>> **输入 (Input)**: `FeedbackUpdateParams`

```typescript
interface FeedbackUpdateParams {
    word: string                    // "progressive"
    language: string               // "zh"
    action: 'like' | 'dislike'    // 反馈动作
}
```

<<<<< **输出 (Output)**: `number | null`

```typescript
// 返回更新后的反馈分数，如果记录不存在或更新失败则返回null
// 
// 更新逻辑：
// - 'like': feedbackScore + 1
// - 'dislike': feedbackScore - 1
// 
// 示例：
// 如果当前分数为5，用户点赞，则返回6
// 如果当前分数为3，用户点踩，则返回2
// 如果单词记录不存在，则返回null
```

---

## 5. AI Agent 需要了解的文件上下文

<context_files>
cloudflare/d1/migrations/0001_create_word_definitions.sql
cloudflare/workers/api/src/index.ts
cloudflare/workers/api/src/types/word-types.ts
cloudflare/workers/api/src/services/word.service.ts
cloudflare/workers/api/src/services/ai.service.ts
cloudflare/workers/api/package.json
cloudflare/workers/api/wrangler.toml
cloudflare/workers/api/assets/prompts/senseword_ai-v7.md
iOS/Packages/SharedModels/Sources/SharedModels/WordModels.swift
iOS/Packages/SharedModels/Sources/SharedModels/APIService.swift
iOS/Packages/SharedModels/Package.swift
.env.local
</context_files>

## 7. 环境变量配置

### Cloudflare Worker环境变量 (wrangler.toml)
```toml
[env.production.vars]
GEMINI_API_KEY = "your-gemini-api-key"
GEMINI_MODEL_ID = "gemini-2.5-flash-lite-preview-06-17"
STATIC_API_KEY = "your-static-api-key"

[env.development.vars]
GEMINI_API_KEY = "your-dev-gemini-api-key"
GEMINI_MODEL_ID = "gemini-2.5-flash-lite-preview-06-17"
STATIC_API_KEY = "your-dev-static-api-key"

# Assets配置：静态资源绑定
[assets]
directory = "./assets"
binding = "ASSETS"
```

### 本地开发环境变量 (.env.local)
```env
GEMINI_API_KEY=your-gemini-api-key
GEMINI_MODEL_ID=gemini-2.5-flash-lite-preview-06-17
STATIC_API_KEY=your-static-api-key
```

## 6. 核心业务流程伪代码

### 6.1. 单词查询/生成流程

```typescript
async function handleWordQuery(request: Request, env: Env): Promise<Response> {
    // [FC-02] 解析请求参数
    const { wordName } = request.params
    const { lang } = request.query

    // [FC-03] 查询数据库
    const existingRecord = await findWordDefinition(env.DB, wordName, lang)

    if (existingRecord) {
        // 如果存在，直接格式化返回
        const response = formatWordResponse(existingRecord)
        return new Response(JSON.stringify(response), { status: 200 })
    }

    // [FC-04.1] 预处理提示词，处理语言映射
    const processedPrompt = await processPromptForGemini(wordName, lang, env)

    // [FC-04] 调用Gemini AI生成内容
    const aiContent = await generateWordContent(processedPrompt, env)

    // [FC-06] 保存到数据库
    const saved = await saveWordDefinition(env.DB, aiContent)
    if (!saved) {
        return new Response(JSON.stringify({
            error: { code: 'DATABASE_ERROR', message: 'Failed to save word definition' }
        }), { status: 500 })
    }

    // [FC-07] 格式化响应
    const response = formatWordResponse(aiContent)
    return new Response(JSON.stringify(response), { status: 200 })
}
```

### 6.2. 用户反馈处理流程

```typescript
async function handleFeedback(request: Request, env: Env): Promise<Response> {
    // [FC-08] 解析和验证请求
    const feedbackData = await request.json() as FeedbackRequest
    const { word, language, action } = feedbackData

    // 验证静态API密钥和动态密钥
    const staticKey = request.headers.get('X-Static-API-Key')
    const dynamicKey = request.headers.get('X-Dynamic-Key')
    
    if (!staticKey || staticKey !== env.STATIC_API_KEY) {
        return new Response(JSON.stringify({
            error: { code: 'AUTHENTICATION_REQUIRED', message: 'Invalid static API key' }
        }), { status: 401 })
    }

    // TODO: 验证动态密钥（暂时跳过，等待设备认证接口实现）

    // 验证输入参数
    if (!word || !language || !['like', 'dislike'].includes(action)) {
        return new Response(JSON.stringify({
            error: { code: 'INVALID_ACTION', message: 'Invalid feedback parameters' }
        }), { status: 400 })
    }

    // [FC-09] 更新反馈分数
    const newScore = await updateFeedbackScore(env.DB, word, language, action)
    
    if (newScore === null) {
        return new Response(JSON.stringify({
            error: { code: 'INVALID_WORD', message: 'Word definition not found' }
        }), { status: 404 })
    }

    // 返回成功响应
    const response: FeedbackSuccessResponse = {
        success: true,
        newScore: newScore,
        message: `Feedback recorded successfully. New score: ${newScore}`
    }
    
    return new Response(JSON.stringify(response), { status: 200 })
}
```