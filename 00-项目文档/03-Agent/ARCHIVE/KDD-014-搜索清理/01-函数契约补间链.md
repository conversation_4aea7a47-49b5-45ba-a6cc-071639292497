# 需求："清理废弃搜索建议端点" 的函数契约补间链 (V1.0 - 极简清理版)

## 0. 依赖关系与影响分析

- [删除] `iOS/Sources/Shared/Services/SearchService.swift`: 完整删除约300行的废弃搜索服务代码
- [检查] 相关数据模型文件：检查是否有独立的搜索建议数据模型需要清理
- [保留] `iOS/Packages/CoreDataDomain/Sources/CoreDataDomain/LocalSearchService.swift`: 保留本地搜索实现
- [保留] `iOS/Documentation/LocalSearchGuide.md`: 保留本地搜索文档
- [确认] 后端 `GET /api/v1/suggestions` 端点：已确认未实现，无需清理

## 1. 项目文件结构概览 (Project File Structure Overview)

```
iOS/
├── Sources/
│   └── Shared/
│       └── Services/
│           └── SearchService.swift                    # [删除] 废弃的远程搜索服务
├── Packages/
│   ├── SharedModels/
│   │   └── Sources/
│   │       └── SharedModels/
│   │           ├── SearchTypes.swift                  # [检查] 可能包含废弃的搜索数据模型
│   │           └── APIService.swift                   # [检查] 确认无相关引用
│   └── CoreDataDomain/
│       └── Sources/
│           └── CoreDataDomain/
│               ├── LocalSearchService.swift           # [保留] 本地搜索实现
│               └── LocalSearchManager.swift           # [保留] 本地搜索管理器
└── Documentation/
    └── LocalSearchGuide.md                            # [保留] 本地搜索文档
```

## 2. 分支策略建议

- 建议的特性分支名称: `chore/cleanup-deprecated-search-api`
- 建议的 git worktree 文件路径：/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/04-search-cleanup
- 基础分支: `dev`
- 分支创建模拟命令行:
    ```bash
    # 概念性命令，用于记录和指导
    # git checkout dev
    # git pull origin dev
    # git worktree add /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/04-search-cleanup -b chore/cleanup-deprecated-search-api dev
    ```

## 3. Commit 规划概要 (Commit Plan Summary & Status)

- [x] chore(search): 删除废弃的远程搜索服务 SearchService.swift
- [x] chore(models): 清理废弃的搜索建议数据模型 SearchModels.swift
- [x] docs(search): 更新文档确认本地搜索为唯一实现方案

## 4. 函数契约补间链 (Function Contract Tweening Chain)

### [FC-01]: 代码库分析器

- 职责: 分析项目中与废弃搜索端点相关的所有代码文件，确认删除范围和影响
- 函数签名: `analyzeDeprecatedSearchCode() -> CodeAnalysisResult`
- 所在文件: 分析工具

>>>>> 输入 (Input): 项目代码库状态

```typescript
interface ProjectCodebase {
  searchServiceFile: "iOS/Sources/Shared/Services/SearchService.swift"
  relatedModelFiles: string[]
  documentationFiles: string[]
  testFiles: string[]
}
```

<<<<< 输出 (Output): CodeAnalysisResult

```typescript
interface CodeAnalysisResult {
  filesToDelete: string[]
  filesToCheck: string[]
  filesToKeep: string[]
  dependencies: {
    hasExternalReferences: boolean
    referencingFiles: string[]
  }
}
```

---

### [FC-02]: 文件删除执行器

- 职责: 安全删除已确认的废弃文件，确保不影响现有功能
- 函数签名: `deleteDeprecatedFiles(filesToDelete: string[]) -> DeletionResult`
- 所在文件: 文件系统操作

>>>>> 输入 (Input): 待删除文件列表

```typescript
interface FilesToDelete {
  primaryFiles: [
    "iOS/Sources/Shared/Services/SearchService.swift"
  ]
  relatedFiles: string[]  // 如果发现相关的废弃数据模型
}
```

<<<<< 输出 (Output): DeletionResult

```typescript
interface DeletionResult {
  deletedFiles: string[]
  preservedFiles: string[]
  cleanupComplete: boolean
  codebaseStatus: "clean" | "needs_review"
}
```

## 5. AI Agent 需要了解的文件上下文

<context_files>
iOS/Sources/Shared/Services/SearchService.swift
iOS/Packages/SharedModels/Sources/SharedModels/SearchTypes.swift
iOS/Packages/SharedModels/Sources/SharedModels/APIService.swift
iOS/Packages/CoreDataDomain/Sources/CoreDataDomain/LocalSearchService.swift
iOS/Packages/CoreDataDomain/Sources/CoreDataDomain/LocalSearchManager.swift
iOS/Documentation/LocalSearchGuide.md
0-KDD - 关键帧驱动开发/01-Public/02.5-API接口能力可视化.md
</context_files>

## 6. 核心业务流程伪代码

```typescript
async function cleanupDeprecatedSearchAPI(): Promise<void> {
    // [FC-01] 分析代码库，确认删除范围
    const analysisResult = await analyzeDeprecatedSearchCode()

    // 验证没有外部引用
    if (analysisResult.dependencies.hasExternalReferences) {
        throw new Error(`发现外部引用: ${analysisResult.dependencies.referencingFiles}`)
    }

    // [FC-02] 执行文件删除
    const deletionResult = await deleteDeprecatedFiles(analysisResult.filesToDelete)

    // 验证清理完成
    if (!deletionResult.cleanupComplete) {
        throw new Error("清理未完成，需要手动检查")
    }

    // 更新文档
    await updateDocumentation("本地搜索为唯一实现方案")

    console.log("✅ 废弃搜索API清理完成")
}
```