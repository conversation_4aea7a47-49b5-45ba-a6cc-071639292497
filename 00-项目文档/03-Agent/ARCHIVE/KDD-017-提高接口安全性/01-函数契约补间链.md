# 需求："提高API接口安全性" 的函数契约补间链 (V1.0 - 安全加固版)

## 0. 依赖关系与影响分析

- [修改] `cloudflare/workers/auth/src/controllers/auth.controller.ts`: Apple登录API需要添加静态密钥验证
- [修改] `cloudflare/workers/auth/src/controllers/logout.controller.ts`: 登出相关API需要添加静态密钥验证
- [修改] `cloudflare/workers/auth/src/controllers/account.controller.ts`: 账号管理API需要添加静态密钥验证
- [修改] `cloudflare/workers/auth/src/controllers/purchase.controller.ts`: 购买验证API需要添加静态密钥验证
- [修改] `cloudflare/workers/api/src/index.ts`: 每日一词API需要添加静态密钥验证
- [修改] `cloudflare/workers/api/src/controllers/bookmark.controller.ts`: 生词本健康检查需要添加静态密钥验证
- [修改] `cloudflare/workers/quality/src/index.ts`: 质量管理触发器需要添加静态密钥验证
- [新增] `cloudflare/workers/shared/middleware/static-auth.middleware.ts`: 统一的静态密钥验证中间件
- [修改] 各Worker的`wrangler.toml`: 添加STATIC_API_KEY环境变量配置

## 1. 项目文件结构概览 (Project File Structure Overview)

```
cloudflare/
├── workers/
│   ├── shared/
│   │   └── middleware/
│   │       └── static-auth.middleware.ts    # [新增] 统一静态密钥验证中间件
│   ├── auth/
│   │   ├── src/
│   │   │   ├── controllers/
│   │   │   │   ├── auth.controller.ts       # [修改] 添加静态密钥验证
│   │   │   │   ├── logout.controller.ts     # [修改] 添加静态密钥验证
│   │   │   │   ├── account.controller.ts    # [修改] 添加静态密钥验证
│   │   │   │   └── purchase.controller.ts   # [修改] 添加静态密钥验证
│   │   │   └── index.ts                     # [修改] 集成静态密钥中间件
│   │   └── wrangler.toml                    # [修改] 添加STATIC_API_KEY配置
│   ├── api/
│   │   ├── src/
│   │   │   ├── controllers/
│   │   │   │   └── bookmark.controller.ts   # [修改] 健康检查添加静态密钥
│   │   │   └── index.ts                     # [修改] 每日一词添加静态密钥
│   │   └── wrangler.toml                    # [修改] 添加STATIC_API_KEY配置
│   └── quality/
│       ├── src/
│       │   └── index.ts                     # [修改] 触发器添加静态密钥
│       └── wrangler.toml                    # [修改] 添加STATIC_API_KEY配置
└── 0-KDD - 关键帧驱动开发/
    └── 01-Public/
        └── 02-API接口能力.md                # [修改] 更新所有API认证要求
```

## 2. 分支策略建议

- 建议的特性分支名称: `feature/security/api-static-key-protection`
- 建议的 git worktree 文件路径：/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/04-api-security-enhancement
- 基础分支: `dev`
- 分支创建模拟命令行:
    ```bash
    # 概念性命令，用于记录和指导
    # git checkout dev
    # git pull origin dev
    # git worktree add /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/04-api-security-enhancement -b feature/security/api-static-key-protection dev
    ```

## 3. Commit 规划概要 (Commit Plan Summary & Status)

- [ ] feat(middleware): create unified static API key validation middleware
- [ ] security(auth): add static key protection to Apple login endpoint
- [ ] security(auth): add static key protection to logout endpoints
- [ ] security(auth): add static key protection to account management endpoints
- [ ] security(auth): add static key protection to purchase verification endpoints
- [ ] security(api): add static key protection to daily word endpoint
- [ ] security(api): add static key protection to bookmark health check endpoint
- [ ] security(quality): add static key protection to trigger endpoint
- [ ] config(workers): update wrangler.toml files with STATIC_API_KEY environment variable
- [ ] docs(api): update API documentation with corrected authentication requirements

## 4. 函数契约补间链 (Function Contract Tweening Chain)

### [FC-01]: 统一静态密钥验证中间件

- **职责**: 提供可重用的静态API密钥验证逻辑，确保所有需要保护的端点都有统一的安全检查
- **函数签名**: `validateStaticApiKey(request: Request, env: WorkerEnv): Promise<{ isValid: boolean; error?: string }>`
- **所在文件**: `cloudflare/workers/shared/middleware/static-auth.middleware.ts`

>>>>> **输入 (Input)**: HTTP请求和环境变量

```typescript
interface StaticKeyValidationInput {
  request: Request;           // HTTP请求对象
  env: WorkerEnv;            // Cloudflare Worker环境变量
}

interface WorkerEnv {
  STATIC_API_KEY: string;    // 静态API密钥
  [key: string]: any;        // 其他环境变量
}
```

<<<<< **输出 (Output)**: 验证结果

```typescript
interface StaticKeyValidationResult {
  isValid: boolean;          // 验证是否通过
  error?: string;           // 错误信息（验证失败时）
}

// 成功时
{
  isValid: true
}

// 失败时
{
  isValid: false,
  error: "Missing or invalid X-Static-API-Key header"
}
```

---

### [FC-02]: Apple登录端点安全加固

- **职责**: 在Apple登录流程开始前验证静态API密钥，确保只有合法的客户端应用可以发起登录请求
- **函数签名**: `handleAppleLogin(request: Request, env: AuthWorkerEnv): Promise<Response>`
- **所在文件**: `cloudflare/workers/auth/src/controllers/auth.controller.ts`

>>>>> **输入 (Input)**: 带有静态密钥的Apple登录请求

```typescript
interface SecureAppleLoginRequest {
  method: 'POST';
  url: '/api/v1/auth/login';
  headers: {
    'X-Static-API-Key': string;     // 新增：静态API密钥
    'Content-Type': 'application/json';
  };
  body: {
    idToken: string;                // Apple ID Token
    userInfo?: {
      familyName?: string;
      givenName?: string;
    };
  };
}
```

<<<<< **输出 (Output)**: 登录响应或安全错误

```typescript
// 成功响应（密钥验证通过且登录成功）
interface SecureLoginSuccessResponse {
  success: true;
  session: {
    sessionId: string;
    user: {
      id: string;
      email: string;
      displayName: string;
      isPro: boolean;
    };
  };
}

// 安全错误响应（密钥验证失败）
interface SecurityErrorResponse {
  success: false;
  error: 'INVALID_API_KEY' | 'MISSING_API_KEY';
  message: string;
  timestamp: string;
}
```

---

### [FC-03]: 用户身份相关API双重认证加固

- **职责**: 为所有用户身份相关的API（登出、账号管理、购买验证）添加静态密钥验证，实现双重认证保护
- **函数签名**: `validateDualAuthentication(request: Request, env: AuthWorkerEnv): Promise<{ isValid: boolean; user?: UserInfo; error?: string }>`
- **所在文件**: `cloudflare/workers/auth/src/middleware/dual-auth.middleware.ts`

>>>>> **输入 (Input)**: 需要双重认证的请求

```typescript
interface DualAuthRequest {
  method: 'POST' | 'DELETE' | 'GET';
  headers: {
    'X-Static-API-Key': string;           // 第一层：静态API密钥
    'Authorization': string;              // 第二层：Bearer <sessionId>
    'Content-Type'?: 'application/json';
  };
  body?: any;                            // 请求体（可选）
}
```

<<<<< **输出 (Output)**: 双重认证验证结果

```typescript
interface DualAuthResult {
  isValid: boolean;                      // 双重认证是否通过
  user?: {                              // 认证通过时的用户信息
    id: string;
    email: string;
    displayName: string;
    isPro: boolean;
  };
  error?: string;                       // 认证失败时的错误信息
}

// 成功时
{
  isValid: true,
  user: {
    id: "usr_abc123",
    email: "<EMAIL>",
    displayName: "John Doe",
    isPro: true
  }
}

// 失败时（静态密钥无效）
{
  isValid: false,
  error: "Invalid or missing X-Static-API-Key header"
}

// 失败时（Session无效）
{
  isValid: false,
  error: "Invalid or expired session"
}
```

---

### [FC-04]: 每日一词API静态密钥保护

- **职责**: 为每日一词API添加静态密钥验证，防止未授权访问
- **函数签名**: `handleDailyWordAPI(request: Request, env: Env, corsHeaders: Record<string, string>): Promise<Response>`
- **所在文件**: `cloudflare/workers/api/src/index.ts`

>>>>> **输入 (Input)**: 带有静态密钥的每日一词请求

```typescript
interface SecureDailyWordRequest {
  method: 'GET';
  url: '/api/v1/daily-word';
  headers: {
    'X-Static-API-Key': string;     // 新增：静态API密钥验证
  };
}
```

<<<<< **输出 (Output)**: 每日一词响应或安全错误

```typescript
// 成功响应（密钥验证通过）
interface SecureDailyWordResponse {
  word: string;                     // 今日推荐单词
  date: string;                     // 日期 (YYYY-MM-DD)
}

// 安全错误响应（密钥验证失败）
interface DailyWordSecurityError {
  error: {
    code: 'INVALID_API_KEY' | 'MISSING_API_KEY';
    message: string;
  };
}
```

---

### [FC-05]: 健康检查端点安全加固

- **职责**: 为所有健康检查端点添加静态密钥验证，防止未授权的系统状态探测
- **函数签名**: `handleHealthCheck(request: Request, env: WorkerEnv): Promise<Response>`
- **所在文件**: 各Worker的健康检查控制器

>>>>> **输入 (Input)**: 带有静态密钥的健康检查请求

```typescript
interface SecureHealthCheckRequest {
  method: 'GET';
  url: '/api/v1/auth/health' | '/api/v1/bookmarks/health';
  headers: {
    'X-Static-API-Key': string;     // 新增：静态API密钥验证
  };
}
```

<<<<< **输出 (Output)**: 健康状态响应或安全错误

```typescript
// 成功响应（密钥验证通过）
interface SecureHealthResponse {
  status: 'healthy' | 'unhealthy';
  service: string;                  // 服务名称
  timestamp: string;               // 检查时间
  environment: string;             // 环境信息
  version: string;                 // 版本信息
}

// 安全错误响应（密钥验证失败）
interface HealthCheckSecurityError {
  error: {
    code: 'INVALID_API_KEY' | 'MISSING_API_KEY';
    message: string;
  };
}
```

---

### [FC-06]: 质量管理触发器安全加固

- **职责**: 为质量管理触发器添加静态密钥验证，防止未授权的系统操作
- **函数签名**: `handleTrigger(request: Request, env: QualityWorkerEnv): Promise<Response>`
- **所在文件**: `cloudflare/workers/quality/src/index.ts`

>>>>> **输入 (Input)**: 带有静态密钥的触发器请求

```typescript
interface SecureTriggerRequest {
  method: 'POST';
  url: '/trigger';
  headers: {
    'X-Static-API-Key': string;     // 新增：静态API密钥验证
    'Content-Type': 'application/json';
  };
  body?: {
    action?: string;                // 触发的操作类型
    parameters?: Record<string, any>; // 操作参数
  };
}
```

<<<<< **输出 (Output)**: 触发器响应或安全错误

```typescript
// 成功响应（密钥验证通过且操作执行）
interface SecureTriggerResponse {
  success: true;
  message: string;                 // 操作结果描述
  taskId?: string;                // 任务ID（如果是异步操作）
  timestamp: string;              // 执行时间
}

// 安全错误响应（密钥验证失败）
interface TriggerSecurityError {
  success: false;
  error: {
    code: 'INVALID_API_KEY' | 'MISSING_API_KEY';
    message: string;
  };
  timestamp: string;
}
```

## 5. AI Agent 需要了解的文件上下文

<context_files>
cloudflare/workers/shared/middleware/static-auth.middleware.ts
cloudflare/workers/auth/src/controllers/auth.controller.ts
cloudflare/workers/auth/src/controllers/logout.controller.ts
cloudflare/workers/auth/src/controllers/account.controller.ts
cloudflare/workers/auth/src/controllers/purchase.controller.ts
cloudflare/workers/auth/src/middleware/dual-auth.middleware.ts
cloudflare/workers/auth/src/index.ts
cloudflare/workers/auth/wrangler.toml
cloudflare/workers/api/src/index.ts
cloudflare/workers/api/src/controllers/bookmark.controller.ts
cloudflare/workers/api/wrangler.toml
cloudflare/workers/quality/src/index.ts
cloudflare/workers/quality/wrangler.toml
0-KDD - 关键帧驱动开发/01-Public/02-API接口能力.md
</context_files>

## 6. 核心业务流程伪代码

```typescript
// 统一静态密钥验证中间件
async function validateStaticApiKey(request: Request, env: WorkerEnv): Promise<ValidationResult> {
    // [FC-01] 提取静态API密钥
    const apiKey = request.headers.get('X-Static-API-Key');

    if (!apiKey) {
        return { isValid: false, error: 'Missing X-Static-API-Key header' };
    }

    if (apiKey !== env.STATIC_API_KEY) {
        return { isValid: false, error: 'Invalid X-Static-API-Key' };
    }

    return { isValid: true };
}

// Apple登录安全加固流程
async function handleSecureAppleLogin(request: Request, env: AuthWorkerEnv): Promise<Response> {
    // [FC-02] 第一步：验证静态API密钥
    const keyValidation = await validateStaticApiKey(request, env);
    if (!keyValidation.isValid) {
        return new Response(JSON.stringify({
            success: false,
            error: 'INVALID_API_KEY',
            message: keyValidation.error,
            timestamp: new Date().toISOString()
        }), { status: 401 });
    }

    // 第二步：继续原有的Apple登录流程
    return await originalAppleLoginHandler(request, env);
}

// 双重认证验证流程
async function validateDualAuthentication(request: Request, env: AuthWorkerEnv): Promise<DualAuthResult> {
    // [FC-03] 第一层：验证静态API密钥
    const keyValidation = await validateStaticApiKey(request, env);
    if (!keyValidation.isValid) {
        return { isValid: false, error: keyValidation.error };
    }

    // 第二层：验证Session
    const sessionValidation = await authenticateSession(request, env);
    if (!sessionValidation.isValid) {
        return { isValid: false, error: 'Invalid or expired session' };
    }

    return { isValid: true, user: sessionValidation.user };
}

// 每日一词API安全加固流程
async function handleSecureDailyWord(request: Request, env: Env): Promise<Response> {
    // [FC-04] 验证静态API密钥
    const keyValidation = await validateStaticApiKey(request, env);
    if (!keyValidation.isValid) {
        return new Response(JSON.stringify({
            error: {
                code: 'INVALID_API_KEY',
                message: keyValidation.error
            }
        }), { status: 401 });
    }

    // 继续原有的每日一词逻辑
    return await originalDailyWordHandler(request, env);
}

// 健康检查安全加固流程
async function handleSecureHealthCheck(request: Request, env: WorkerEnv): Promise<Response> {
    // [FC-05] 验证静态API密钥
    const keyValidation = await validateStaticApiKey(request, env);
    if (!keyValidation.isValid) {
        return new Response(JSON.stringify({
            error: {
                code: 'INVALID_API_KEY',
                message: keyValidation.error
            }
        }), { status: 401 });
    }

    // 返回健康状态
    return new Response(JSON.stringify({
        status: 'healthy',
        service: 'auth-worker',
        timestamp: new Date().toISOString(),
        environment: env.NODE_ENV,
        version: '1.0.0'
    }), { status: 200 });
}
```