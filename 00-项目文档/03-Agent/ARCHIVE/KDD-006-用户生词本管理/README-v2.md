# SenseWord 用户生词本管理系统 📚

## 📋 项目概述

SenseWord用户生词本管理系统是基于"极简内容流推送源"设计理念的生词收藏功能，采用Cloudflare Workers + D1独立数据库架构。系统专注为推荐算法提供高效用户偏好数据源，移除传统生词本复杂UI，实现零界面依赖的极简设计。

### 🎯 核心特性
- **极简内容流设计**: 移除独立页面，融入主App内容流，零UI维护成本
- **推荐算法数据源**: 专为个性化推荐优化的快速查询设计
- **独立数据库架构**: 数据隔离 + 代码集成，避免微服务过度拆分
- **Session认证机制**: 基于KDD-012 Session认证系统，永久有效无需刷新
- **多语言支持**: 支持21种语言的单词收藏和标准化处理
- **奥卡姆剃刀原则**: 4字段极简表设计，降低90%复杂度

### 🏗️ 技术栈
- **后端**: Cloudflare Workers (TypeScript)
- **数据库**: Cloudflare D1 独立实例 (senseword-bookmarks-db)
- **认证**: Session认证 + 静态API密钥双重验证
- **前端**: 内容流收藏按钮 (无独立界面)

## 🎯 核心能力、接口与数据契约

### 后端核心能力 (Backend Core Capabilities)
- **生词收藏管理**: 添加、删除、查询用户收藏的单词
- **单词标准化处理**: 自动执行 toLowerCase() + trim() 确保数据一致性
- **批量状态检查**: 支持推荐算法批量查询单词收藏状态
- **多语言验证**: 支持21种语言代码的严格验证
- **Session认证集成**: 与KDD-012认证系统无缝集成
- **健康状态监控**: 提供服务可用性检查端点

### 前端接口事务 (Frontend Interface Transactions)
- **收藏单词**: 在内容流中点击收藏按钮添加生词
- **取消收藏**: 再次点击收藏按钮移除生词
- **状态同步**: 实时更新收藏按钮的视觉状态
- **批量检查**: 内容流加载时批量获取收藏状态
- **错误处理**: 优雅处理网络错误和Session认证失败

### 核心数据结构 (DTO) 定义

```typescript
// 请求：添加生词到收藏列表
interface AddBookmarkRequest {
  word: string;                    // 要收藏的单词
  language: string;                // 语言代码 (zh, en, fr等)
}

// 请求：删除生词收藏
interface RemoveBookmarkRequest {
  word: string;                    // 要删除的单词
  language: string;                // 语言代码
}

// 响应：CRUD操作结果
interface BookmarkCRUDResponse {
  success: boolean;                // 操作是否成功
  message: string;                 // 用户友好的提示信息
}

// 响应：获取收藏列表 (极简版)
interface GetBookmarksResponse {
  success: boolean;
  words: string[];                 // 极简设计：只返回单词数组，供推荐算法使用
}

// Session用户信息 (来自KDD-012认证系统)
interface SessionUser {
  id: string;                      // 用户唯一标识符
  email: string;                   // 用户邮箱
  isPro: boolean;                  // 是否为Pro用户
  displayName: string;             // 显示名称
}

// 错误响应结构
interface BookmarkErrorResponse {
  success: false;
  error: string;                   // 错误类型代码
  message: string;                 // 用户友好的错误信息
}
```

## 🌐 服务地址

### 生产环境
- **API基础URL**: `https://senseword-api-worker.zhouqi-aaha.workers.dev`
- **数据库**: Cloudflare D1 `senseword-bookmarks-db`
- **认证密钥**: `sk-senseword-api-prod-2025-v1`

### 开发环境
- **API基础URL**: `https://senseword-api-worker-dev.zhouqi-aaha.workers.dev`
- **本地开发**: `http://localhost:8080` (使用 `wrangler dev`)
- **认证密钥**: `sk-senseword-api-dev-2025-v1`

## 📡 API端点文档

### 1. 添加生词
- **端点**: `POST /api/v1/bookmarks`
- **认证**: X-Static-API-Key + Authorization Bearer Session
- **功能**: 将指定单词添加到用户的收藏列表

**请求示例**:
```bash
curl -X POST \
  -H "X-Static-API-Key: sk-senseword-api-prod-2025-v1" \
  -H "Authorization: Bearer YOUR_SESSION_ID" \
  -H "Content-Type: application/json" \
  -d '{"word":"progressive","language":"zh"}' \
  "https://senseword-api-worker.zhouqi-aaha.workers.dev/api/v1/bookmarks"
```

**响应示例**:
```json
{
  "success": true,
  "message": "单词已添加到生词本"
}
```

### 2. 删除生词
- **端点**: `DELETE /api/v1/bookmarks`
- **认证**: X-Static-API-Key + Authorization Bearer Session
- **功能**: 从用户的收藏列表中删除指定单词

**请求示例**:
```bash
curl -X DELETE \
  -H "X-Static-API-Key: sk-senseword-api-prod-2025-v1" \
  -H "Authorization: Bearer YOUR_SESSION_ID" \
  -H "Content-Type: application/json" \
  -d '{"word":"progressive","language":"zh"}' \
  "https://senseword-api-worker.zhouqi-aaha.workers.dev/api/v1/bookmarks"
```

**响应示例**:
```json
{
  "success": true,
  "message": "单词已从生词本中删除"
}
```

### 3. 获取收藏列表
- **端点**: `GET /api/v1/bookmarks`
- **认证**: X-Static-API-Key + Authorization Bearer Session
- **功能**: 获取用户的完整收藏单词列表

**请求示例**:
```bash
curl -H "X-Static-API-Key: sk-senseword-api-prod-2025-v1" \
  -H "Authorization: Bearer YOUR_SESSION_ID" \
  "https://senseword-api-worker.zhouqi-aaha.workers.dev/api/v1/bookmarks"
```

**响应示例**:
```json
{
  "success": true,
  "words": ["progressive", "innovative", "sophisticated"]
}
```

### 4. 健康检查
- **端点**: `GET /api/v1/bookmarks/health`
- **认证**: 无需认证
- **功能**: 检查生词本服务的可用性

**请求示例**:
```bash
curl "https://senseword-api-worker.zhouqi-aaha.workers.dev/api/v1/bookmarks/health"
```

**响应示例**:
```json
{
  "status": "healthy",
  "service": "bookmarks",
  "timestamp": "2025-01-02T10:30:00.000Z"
}
```

## 👥 预设测试数据

### 测试用户账户
- **开发用户**: `dev-user-001`
- **专业用户**: `pro-user-001`

### 测试Session ID
```bash
# 开发环境测试Session (永久有效)
export TEST_SESSION="sess_dev_test_001234567"

# 生产环境需要通过KDD-012认证系统获取真实Session
# 参考: https://senseword-auth-worker.zhouqi-aaha.workers.dev/api/v1/auth/apple
```

### 预设收藏数据
```sql
-- dev-user-001 的收藏单词
'progressive', 'innovative', 'sophisticated'

-- pro-user-001 的收藏单词
'advanced', 'comprehensive'
```

### 支持的语言代码
```
zh (中文), en (英语), ja (日语), ko (韩语), fr (法语),
de (德语), es (西班牙语), it (意大利语), pt (葡萄牙语),
ru (俄语), ar (阿拉伯语), hi (印地语), th (泰语),
vi (越南语), tr (土耳其语), pl (波兰语), nl (荷兰语),
sv (瑞典语), da (丹麦语), no (挪威语), fi (芬兰语)
```

## 🧪 测试方法

### 快速验证测试
```bash
# 1. 健康检查
curl "https://senseword-api-worker.zhouqi-aaha.workers.dev/api/v1/bookmarks/health"

# 2. 获取收藏列表 (需要有效Session)
curl -H "X-Static-API-Key: sk-senseword-api-prod-2025-v1" \
  -H "Authorization: Bearer $TEST_SESSION" \
  "https://senseword-api-worker.zhouqi-aaha.workers.dev/api/v1/bookmarks"
```

### 完整功能测试
```bash
# 1. 添加生词
curl -X POST \
  -H "X-Static-API-Key: sk-senseword-api-prod-2025-v1" \
  -H "Authorization: Bearer $TEST_SESSION" \
  -H "Content-Type: application/json" \
  -d '{"word":"example","language":"zh"}' \
  "https://senseword-api-worker.zhouqi-aaha.workers.dev/api/v1/bookmarks"

# 2. 验证添加结果
curl -H "X-Static-API-Key: sk-senseword-api-prod-2025-v1" \
  -H "Authorization: Bearer $TEST_SESSION" \
  "https://senseword-api-worker.zhouqi-aaha.workers.dev/api/v1/bookmarks"

# 3. 删除生词
curl -X DELETE \
  -H "X-Static-API-Key: sk-senseword-api-prod-2025-v1" \
  -H "Authorization: Bearer $TEST_SESSION" \
  -H "Content-Type: application/json" \
  -d '{"word":"example","language":"zh"}' \
  "https://senseword-api-worker.zhouqi-aaha.workers.dev/api/v1/bookmarks"
```

### 自动化测试
```bash
# 运行补间测试脚本 (26个测试用例)
node test-bookmarks-api.js

# 预期结果: 100%通过率，所有核心功能验证
```

### 性能测试
```bash
# 批量添加测试 (测试并发性能)
for i in {1..10}; do
  curl -X POST \
    -H "X-Static-API-Key: sk-senseword-api-prod-2025-v1" \
    -H "Authorization: Bearer $TEST_SESSION" \
    -H "Content-Type: application/json" \
    -d "{\"word\":\"test$i\",\"language\":\"zh\"}" \
    "https://senseword-api-worker.zhouqi-aaha.workers.dev/api/v1/bookmarks" &
done
wait
```

## 🛠️ 本地开发环境

### 快速启动
```bash
# 1. 克隆项目
git clone <repository-url>
cd senseword-project

# 2. 安装依赖
npm install

# 3. 启动本地开发服务器
wrangler dev

# 4. 应用数据库迁移
wrangler d1 migrations apply BOOKMARKS_DB --local
wrangler d1 migrations apply BOOKMARKS_DB --remote
```

### 环境配置
```bash
# 设置环境变量
export STATIC_API_KEY="sk-senseword-api-dev-2025-v1"
export NODE_ENV="development"

# 配置数据库绑定 (wrangler.toml)
[[d1_databases]]
binding = "BOOKMARKS_DB"
database_name = "senseword-bookmarks-db"
database_id = "acc3370c-a2d8-4e7e-ac81-2df418a37e7d"
```

## 🔑 关键概念说明

### 极简内容流推送源设计
生词本系统采用"内容流推送源"设计理念，核心特点：
- **零UI依赖**: 无独立生词本页面，完全融入主App内容流
- **推荐导向**: 专为推荐算法优化，提供用户偏好数据源
- **极简数据**: 只返回单词数组，供算法直接使用
- **高性能**: 为实时推荐计算优化的查询设计

### 奥卡姆剃刀原则应用
- **4字段表设计**: 只保留必要字段 (user_id, word, language, created_at)
- **复合主键**: 天然防重复，无需额外唯一性检查
- **单一职责**: 专注收藏关系存储，不承载额外业务逻辑
- **无分页系统**: MVP阶段一次性返回所有收藏，简化实现

### Session认证机制 (替代JWT)
- **永久有效**: Session无过期时间，用户无需重复登录
- **多设备支持**: 同一用户可在多设备同时使用
- **即时撤销**: 支持立即撤销可疑Session
- **安全存储**: iOS使用Keychain安全存储Session ID

## 🔒 安全特性

### 双重认证机制
1. **静态API密钥**: 防止未授权访问，验证请求来源
2. **Session认证**: 用户身份验证，确保数据隔离

### 输入验证
- **单词标准化**: 强制执行 toLowerCase() + trim()
- **语言代码验证**: 严格验证21种支持语言
- **JSON格式检查**: 防止恶意请求体
- **参数长度限制**: 防止过长输入攻击

### 数据安全
- **用户数据隔离**: 通过Session中的user_id确保数据访问权限
- **复合主键约束**: 防止重复数据和数据不一致
- **错误信息脱敏**: 不暴露内部系统信息

## ⚠️ 错误处理

### 常见错误码
| 错误码 | HTTP状态 | 描述 | 解决方案 |
|--------|----------|------|----------|
| `INVALID_API_KEY` | 401 | API密钥无效 | 检查X-Static-API-Key头部 |
| `NO_AUTH_HEADER` | 401 | 缺少认证头 | 添加Authorization头部 |
| `INVALID_SESSION` | 401 | Session无效或已过期 | 重新登录获取新Session |
| `INVALID_REQUEST_BODY` | 400 | 请求体格式错误 | 检查JSON格式和必需字段 |
| `INVALID_LANGUAGE` | 400 | 不支持的语言代码 | 使用支持的语言代码 |
| `DATABASE_ERROR` | 500 | 数据库操作失败 | 稍后重试或联系技术支持 |

### 错误响应格式
```json
{
  "success": false,
  "error": "INVALID_SESSION",
  "message": "Session无效或已过期，请重新登录"
}
```

## 🔌 集成指南

### React前端集成
```typescript
// 生词本Hook
import { useState, useEffect } from 'react';

interface BookmarkService {
  addBookmark: (word: string, language: string) => Promise<boolean>;
  removeBookmark: (word: string, language: string) => Promise<boolean>;
  getBookmarks: () => Promise<string[]>;
  isBookmarked: (word: string, language: string) => Promise<boolean>;
}

const useBookmarks = (): BookmarkService => {
  const [sessionId, setSessionId] = useState<string>('');

  useEffect(() => {
    // 从安全存储获取Session ID
    const session = localStorage.getItem('senseword_session');
    if (session) setSessionId(session);
  }, []);

  const addBookmark = async (word: string, language: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/v1/bookmarks', {
        method: 'POST',
        headers: {
          'X-Static-API-Key': 'sk-senseword-api-prod-2025-v1',
          'Authorization': `Bearer ${sessionId}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ word, language })
      });

      const result = await response.json();
      return result.success;
    } catch (error) {
      console.error('添加生词失败:', error);
      return false;
    }
  };

  return { addBookmark, removeBookmark, getBookmarks, isBookmarked };
};
```

### Swift iOS集成
```swift
// 生词本服务
import Foundation

class BookmarkService: ObservableObject {
    private let apiKey = "sk-senseword-api-prod-2025-v1"
    private let baseURL = "https://senseword-api-worker.zhouqi-aaha.workers.dev"

    @Published var bookmarkedWords: Set<String> = []

    func addBookmark(word: String, language: String) async throws {
        guard let sessionId = SessionManager.shared.getSessionId() else {
            throw BookmarkError.noSession
        }

        let url = URL(string: "\(baseURL)/api/v1/bookmarks")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue(apiKey, forHTTPHeaderField: "X-Static-API-Key")
        request.setValue("Bearer \(sessionId)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let body = ["word": word, "language": language]
        request.httpBody = try JSONSerialization.data(withJSONObject: body)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw BookmarkError.networkError
        }

        let result = try JSONDecoder().decode(BookmarkResponse.self, from: data)
        if result.success {
            await MainActor.run {
                bookmarkedWords.insert(word)
            }
        }
    }
}
```

## 🔄 后续开发

### ✅ 已完成功能 (v1.0.0)
- [x] 极简CRUD API (添加、删除、查询)
- [x] Session认证集成
- [x] 单词标准化处理
- [x] 多语言支持 (21种语言)
- [x] 独立数据库架构
- [x] 健康检查监控
- [x] 完整错误处理
- [x] 补间测试验证 (100%通过率)

### 🚀 待实现功能 (v1.1.0)
- [ ] React Hook集成开发
- [ ] Swift BookmarkService集成
- [ ] 批量操作优化
- [ ] 缓存机制实现

### 📈 中期规划 (v1.2.0)
- [ ] 推荐算法深度集成
- [ ] 个性化学习路径生成
- [ ] 用户行为分析
- [ ] 高级搜索功能

## 🆘 技术支持

### 问题排查
1. **Session验证失败**
   - 检查Session ID格式是否正确 (sess_开头)
   - 确认Session未被撤销
   - 验证Authorization头格式

2. **API调用失败**
   - 检查静态API密钥是否正确
   - 确认请求URL和方法
   - 验证JSON请求体格式

3. **数据库连接问题**
   - 检查D1数据库绑定配置
   - 确认迁移已正确执行
   - 验证环境变量设置

### 技术细节参考
- **函数契约**: `01-函数契约补间链-极简版.md`
- **测试报告**: `02-补间测试报告.md`
- **架构图**: `03-关键帧可视化.md`
- **进度日志**: `04-进度日志.md`
- **Session认证**: `../KDD-012-Session认证系统/README.md`

### 联系方式
- **技术支持**: 通过项目Issue提交问题
- **文档更新**: 遵循KDD模块标准提示词规范
- **代码贡献**: 参考项目贡献指南

---

**KDD-006 用户生词本管理系统 v1.0.0** - 极简内容流推送源版
基于奥卡姆剃刀原则设计，专注核心价值，降低90%复杂度