# 需求："iOS前端架构清理重构" 的函数契约补间链 (V1.0 - 极简重构版)

## 0. 依赖关系与影响分析

- [保留] `iOS/SenseWordApp.swift` - 仅保留MinimalGradientHomeView主页面渐变背景
- [删除] `iOS/Packages/UIComponents/` - 删除所有UI组件包
- [删除] `iOS/Packages/SharedModels/` - 删除数据模型层
- [删除] `iOS/Packages/CoreDataDomain/` - 删除CoreData相关服务
- [删除] `iOS/Packages/AuthDomain/` - 删除认证服务
- [删除] `iOS/Packages/PaymentDomain/` - 删除支付服务
- [删除] `iOS/Packages/UserProfileDomain/` - 删除用户配置服务
- [删除] `iOS/Packages/AnalyticsDomain/` - 删除分析服务
- [删除] `iOS/Packages/PersistenceDomain/` - 删除持久化服务
- [删除] `iOS/App/Persistence.swift` - 删除CoreData持久化
- [删除] `iOS/SensewordApp/MockDataService.swift` - 删除模拟数据服务
- [删除] `iOS/SensewordApp/LearningView.swift` - 删除学习视图
- [删除] `iOS/SensewordApp/WordLearningCoordinator.swift` - 删除学习协调器
- [删除] `iOS/Sources/Learning/` - 删除学习模块
- [删除] `iOS/Sources/SensewordAppDependencies/` - 删除依赖管理
- [删除] 所有View、Model、Controller、Network相关代码

## 1. 项目文件结构概览 (Project File Structure Overview)

### 当前混乱状态
```
iOS/
├── SenseWordApp.swift                          # [保留] 仅保留主页渐变背景
├── App/
│   └── Persistence.swift                       # [删除] CoreData遗留代码
├── SensewordApp/
│   ├── LearningView.swift                      # [删除] 学习视图
│   ├── MockDataService.swift                  # [删除] 模拟数据服务
│   └── WordLearningCoordinator.swift          # [删除] 学习协调器
├── Sources/
│   ├── Learning/                               # [删除] 整个学习模块
│   └── SensewordAppDependencies/              # [删除] 依赖管理
├── Packages/
│   ├── UIComponents/                           # [删除] UI组件包
│   ├── SharedModels/                           # [删除] 数据模型层
│   ├── CoreDataDomain/                         # [删除] CoreData服务
│   ├── AuthDomain/                             # [删除] 认证服务
│   ├── PaymentDomain/                          # [删除] 支付服务
│   ├── UserProfileDomain/                      # [删除] 用户服务
│   ├── AnalyticsDomain/                        # [删除] 分析服务
│   └── PersistenceDomain/                      # [删除] 持久化服务
```

### 目标极简架构
```
iOS/
├── SenseWordApp.swift                          # [保留] 仅主页渐变背景
└── Package.swift                               # [保留] 项目配置
```

## 2. 分支策略建议

- 建议的特性分支名称: `feature/ios/architecture-cleanup`
- 建议的 git worktree 文件路径：/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/06-ios-cleanup
- 基础分支: `dev`
- 分支创建模拟命令行:
    ```bash
    # 概念性命令，用于记录和指导
    # git checkout dev
    # git pull origin dev
    # git worktree add /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/06-ios-cleanup -b feature/ios/architecture-cleanup dev
    ```

## 3. Commit 规划概要 (Commit Plan Summary & Status)

- [ ] refactor(ios): 删除所有UI组件包和相关依赖
- [ ] refactor(ios): 删除所有学习视图和协调器
- [ ] refactor(ios): 删除所有数据模型和服务层
- [ ] refactor(ios): 删除CoreData和持久化相关代码
- [ ] refactor(ios): 删除认证、支付、分析等服务包
- [ ] refactor(ios): 删除模拟数据服务和依赖管理
- [ ] refactor(ios): 清理项目配置和依赖引用
- [ ] refactor(ios): 保留仅主页渐变背景的极简结构
- [ ] test(ios): 验证项目编译和主页显示正常

## 4. 函数契约补间链 (Function Contract Tweening Chain)

### [FC-01]: 极简化代码清理器

- 职责: 删除除主页渐变背景外的所有View、Model、Controller、Network代码
- 函数签名: `performRadicalCleanup() -> RadicalCleanupResult`
- 执行范围: iOS项目根目录

>>>>> 输入 (Input): RadicalCleanupSpec

```swift
struct RadicalCleanupSpec {
    let projectPath: String                     // iOS项目根路径
    let preservedFiles: [String]                // 仅保留的文件列表
    let targetDeletionAreas: [DeletionArea]     // 目标删除区域
}

enum DeletionArea {
    case allUIComponents                       // 所有UI组件包
    case allDataModels                         // 所有数据模型
    case allServiceLayers                      // 所有服务层
    case allNetworkLayers                      // 所有网络层
    case allControllers                        // 所有控制器
    case allViewModels                         // 所有视图模型
    case allDependencyManagement               // 所有依赖管理
}
```

<<<<< 输出 (Output): RadicalCleanupResult

```swift
struct RadicalCleanupResult {
    let deletedPackages: [String]              // 已删除的包列表
    let deletedFiles: [String]                 // 已删除的文件列表
    let preservedFiles: [String]               // 保留的文件列表
    let cleanupSummary: RadicalCleanupSummary  // 清理摘要
}

struct RadicalCleanupSummary {
    let totalPackagesDeleted: Int              // 删除包总数
    let totalFilesDeleted: Int                 // 删除文件总数
    let totalSizeFreed: Int                    // 释放空间大小(KB)
    let remainingStructure: ProjectStructure   // 剩余项目结构
}
```

---

### [FC-02]: 主页面背景保留器

- 职责: 从SenseWordApp.swift中提取并保留仅主页渐变背景功能
- 函数签名: `preserveMinimalHomePage() -> HomePagePreservationResult`
- 所在文件: `iOS/SenseWordApp.swift`

>>>>> 输入 (Input): HomePagePreservationSpec

```swift
struct HomePagePreservationSpec {
    let currentAppFile: String                  // 当前应用文件路径
    let backgroundComponents: [String]          // 需要保留的背景组件
    let removalTargets: [RemovalTarget]         // 需要移除的目标
}

enum RemovalTarget {
    case debugMode                             // 调试模式相关代码
    case developerMode                         // 开发者模式相关代码
    case externalDependencies                  // 外部依赖引用
    case complexInteractions                   // 复杂交互逻辑
    case dataBindings                          // 数据绑定
}
```

<<<<< 输出 (Output): HomePagePreservationResult

```swift
struct HomePagePreservationResult {
    let preservedComponents: [String]          // 保留的组件列表
    let removedFeatures: [String]              // 移除的功能列表
    let simplifiedStructure: AppStructure     // 简化后的应用结构
    let compilationStatus: CompilationStatus   // 编译状态
}

struct AppStructure {
    let mainView: String                       // 主视图名称
    let backgroundView: String                 // 背景视图名称
    let dependencies: [String]                 // 剩余依赖列表
    let codeLines: Int                         // 代码行数
}
```

---

### [FC-03]: 项目配置清理器

- 职责: 清理项目配置文件，移除所有包依赖和引用
- 函数签名: `cleanupProjectConfiguration() -> ConfigCleanupResult`
- 执行范围: `iOS/Package.swift` 和 Xcode项目配置

>>>>> 输入 (Input): ConfigCleanupSpec

```swift
struct ConfigCleanupSpec {
    let projectConfigFiles: [String]           // 项目配置文件列表
    let packageDependencies: [String]          // 需要移除的包依赖
    let targetConfigurations: [String]         // 目标配置
}

struct PackageDependency {
    let name: String                           // 包名
    let path: String                           // 包路径
    let type: DependencyType                   // 依赖类型
}

enum DependencyType {
    case localPackage                          // 本地包
    case externalPackage                       // 外部包
    case systemFramework                       // 系统框架
}
```

<<<<< 输出 (Output): ConfigCleanupResult

```swift
struct ConfigCleanupResult {
    let cleanedConfigFiles: [String]           // 清理后的配置文件
    let removedDependencies: [String]          // 移除的依赖列表
    let remainingDependencies: [String]        // 剩余的依赖列表
    let compilationTest: CompilationTestResult // 编译测试结果
}

struct CompilationTestResult {
    let success: Bool                          // 编译是否成功
    let errors: [String]                       // 编译错误列表
    let warnings: [String]                     // 编译警告列表
    let buildTime: TimeInterval                // 编译时间
}
```

## 5. AI Agent 需要了解的文件上下文

<context_files>
iOS/SenseWordApp.swift
iOS/Packages/UIComponents/Package.swift
iOS/Packages/SharedModels/Package.swift
iOS/Packages/CoreDataDomain/Package.swift
iOS/Packages/AuthDomain/Package.swift
iOS/Packages/PaymentDomain/Package.swift
iOS/App/Persistence.swift
iOS/SensewordApp/MockDataService.swift
iOS/SensewordApp/LearningView.swift
iOS/Sources/Learning/LearningView.swift
iOS/Sources/SensewordAppDependencies/DependencyManager.swift
cloudflare/workers/api/src/types/word-types.ts
cloudflare/workers/auth/src/types/auth-types.ts
</context_files>

## 6. 详细清理报告

### 6.1 遗留代码识别清单

#### 重复实现文件
1. **学习视图重复**:
   - `iOS/SensewordApp/LearningView.swift` (重复实现1)
   - `iOS/Sources/Learning/LearningView.swift` (重复实现2)
   - 问题: 两个文件功能相似但略有差异，造成维护困难

2. **依赖管理重复**:
   - `iOS/App/DependencyManager.swift`
   - `iOS/Sources/SensewordAppDependencies/DependencyManager.swift`
   - 问题: 依赖注入逻辑分散，难以统一管理

#### CoreData遗留代码
1. **持久化相关**:
   - `iOS/App/Persistence.swift` - CloudKit集成代码
   - `iOS/App/SensewordApp.xcdatamodeld` - CoreData模型文件
   - `iOS/Packages/PersistenceDomain/` - 整个持久化领域包
   - 问题: 项目主要使用API和缓存，CoreData成为负担

2. **CoreData服务**:
   - `iOS/Packages/CoreDataDomain/` - 整个CoreData领域包
   - 问题: 与当前API驱动架构不匹配

#### 模拟数据服务
1. **测试数据服务**:
   - `iOS/SensewordApp/MockDataService.swift`
   - 问题: 生产代码中包含模拟数据，不符合清洁架构

#### 过时的API配置
1. **占位符配置**:
   - `APIService.swift` 中的 `staticAPIKey: "your-static-api-key"`
   - 问题: 包含占位符配置，可能导致运行时错误

#### 未完成的服务实现
1. **认证服务**:
   - `iOS/Packages/AuthDomain/Sources/AuthDomain/AuthService.swift`
   - 问题: 大部分方法只抛出 "Not implemented" 错误

2. **支付服务**:
   - `iOS/Packages/PaymentDomain/`
   - 问题: 实现不完整，与当前需求不匹配

### 6.2 架构问题分析

#### 层次混乱
1. **视图层侵入业务层**: UI组件直接调用多个服务
2. **服务层职责不清**: 同一功能分散在多个包中
3. **数据层冗余**: CoreData、API、缓存三套数据处理方案并存

#### 依赖关系复杂
1. **循环依赖**: 多个Domain包之间存在相互依赖
2. **过度抽象**: 简单功能被过度包装成复杂的协议层次
3. **测试困难**: Mock和真实实现混合，难以进行单元测试

### 6.3 与后端能力不匹配

#### API能力已完备
- 后端已实现完整的单词查询、用户认证、支付验证API
- 前端却保留大量本地数据处理逻辑

#### 数据同步策略过时
- 后端支持增量同步，前端却使用复杂的CoreData同步
- 简单的缓存策略即可满足需求

## 7. 重构目标架构

### 7.1 精简原则
1. **单一职责**: 每个包只负责一个明确的功能域
2. **依赖倒置**: 高层模块不依赖低层模块，都依赖抽象
3. **接口隔离**: 客户端不应依赖它不需要的接口
4. **开闭原则**: 对扩展开放，对修改关闭

### 7.2 新架构设计
```
iOS/
├── SenseWordApp.swift                    # 应用入口
├── Packages/
│   ├── UIComponents/                     # [保留] UI组件
│   ├── SharedModels/                     # [重建] 核心数据模型
│   │   ├── WordModels.swift             # 单词相关模型
│   │   ├── UserModels.swift             # 用户相关模型
│   │   ├── APIModels.swift              # API通用模型
│   │   └── ErrorModels.swift            # 错误处理模型
│   ├── NetworkLayer/                     # [新建] 网络服务
│   │   ├── APIClient.swift              # API客户端
│   │   ├── RequestBuilder.swift         # 请求构建器
│   │   └── ResponseHandler.swift        # 响应处理器
│   ├── AuthenticationService/            # [新建] 认证服务
│   │   ├── AuthManager.swift            # 认证管理器
│   │   ├── SessionManager.swift         # 会话管理器
│   │   └── AppleSignIn.swift            # Apple登录
│   ├── PaymentService/                   # [新建] 支付服务
│   │   ├── StoreKitManager.swift        # StoreKit管理器
│   │   └── PurchaseValidator.swift      # 购买验证器
│   └── LocalStorage/                     # [新建] 本地存储
│       ├── CacheManager.swift           # 缓存管理器
│       ├── UserDefaults+Extensions.swift # 用户偏好
│       └── KeychainManager.swift        # 钥匙串管理
```

### 7.3 数据流设计
```
UI Layer (UIComponents)
    ↓
Business Layer (ViewModels)
    ↓
Service Layer (各种Service包)
    ↓
Network Layer (APIClient)
    ↓
Backend APIs
```

## 8. 实施计划

### 阶段1: 清理阶段
1. 删除所有标记为[删除]的文件和目录
2. 备份需要保留的配置信息
3. 更新项目文件引用

### 阶段2: 重建阶段
1. 重建SharedModels包，只包含核心数据模型
2. 创建新的服务层包
3. 实现基础的网络和认证功能

### 阶段3: 集成阶段
1. 更新UI组件以使用新的服务层
2. 添加单元测试和集成测试
3. 性能优化和错误处理完善

### 阶段4: 验证阶段
1. 功能测试确保所有特性正常工作
2. 性能测试确保响应速度
3. 代码审查确保架构清洁