# KDD-005: 用户认证注册系统 - 补间测试报告

## 测试概览

### 测试执行状态
- **测试开始时间**: 待执行
- **测试完成时间**: 待执行
- **总测试用例数**: 42个
- **通过测试数**: 0个 (待执行)
- **失败测试数**: 0个 (待执行)
- **测试覆盖率**: 0% (待执行)

### 函数契约测试汇总

| 函数契约 | 补间测试 | 变体测试 | 总计 | 状态 |
|---------|---------|---------|------|------|
| FC-01: handleAppleSignIn | 5个 | 3个 | 8个 | 待执行 |
| FC-02: signInWithApple | 6个 | 4个 | 10个 | 待执行 |
| FC-03: handleAppleLogin | 5个 | 3个 | 8个 | 待执行 |
| FC-04: verifyAppleIdToken | 4个 | 4个 | 8个 | 待执行 |
| FC-05: findOrCreateUser | 3个 | 2个 | 5个 | 待执行 |
| FC-06: generateTokens | 2个 | 1个 | 3个 | 待执行 |
| **总计** | **25个** | **17个** | **42个** | **待执行** |

## 详细测试计划

### FC-01: 前端Apple登录触发器测试

#### 补间测试 (Tweening Tests) - 核心功能路径验证

**测试目标**: 验证Apple登录触发器在各种正常场景下的稳定性和正确性

##### T1.1: 标准Apple登录流程测试
```swift
func testStandardAppleSignInFlow() async throws {
    // 测试数据
    let mockCredential = createMockAppleCredential(
        user: "001234.567890abcdef.1234",
        email: "<EMAIL>",
        fullName: PersonNameComponents(givenName: "John", familyName: "Doe")
    )

    // 执行测试
    let authService = AuthService()
    let result = try await authService.signInWithApple(credential: mockCredential)

    // 验证结果
    XCTAssertNotNil(result.user.id)
    XCTAssertEqual(result.user.email, "<EMAIL>")
    XCTAssertNotNil(result.tokens.accessToken)
    XCTAssertNotNil(result.tokens.refreshToken)
}
```

##### T1.2: 无邮箱Apple登录测试
```swift
func testAppleSignInWithoutEmail() async throws {
    // 测试数据 - Apple可能不提供邮箱
    let mockCredential = createMockAppleCredential(
        user: "001234.567890abcdef.1234",
        email: nil,  // 无邮箱
        fullName: nil
    )

    // 执行测试
    let authService = AuthService()
    let result = try await authService.signInWithApple(credential: mockCredential)

    // 验证结果 - 应该生成私有邮箱
    XCTAssertNotNil(result.user.id)
    XCTAssertTrue(result.user.email.contains("@privaterelay.appleid.com"))
    XCTAssertEqual(result.user.displayName, "SenseWord用户")
}
```

##### T1.3: 重复登录用户测试
```swift
func testRepeatedUserSignIn() async throws {
    // 第一次登录
    let credential1 = createMockAppleCredential(
        user: "001234.567890abcdef.1234",
        email: "<EMAIL>"
    )
    let result1 = try await authService.signInWithApple(credential: credential1)

    // 第二次登录 (相同用户)
    let credential2 = createMockAppleCredential(
        user: "001234.567890abcdef.1234",
        email: "<EMAIL>"
    )
    let result2 = try await authService.signInWithApple(credential: credential2)

    // 验证结果 - 应该返回相同用户但新的令牌
    XCTAssertEqual(result1.user.id, result2.user.id)
    XCTAssertEqual(result1.user.email, result2.user.email)
    XCTAssertNotEqual(result1.tokens.accessToken, result2.tokens.accessToken)
}
```

##### T1.4: 长邮箱地址处理测试
```swift
func testLongEmailAddressHandling() async throws {
    // 测试数据 - 超长邮箱地址
    let longEmail = "<EMAIL>"
    let mockCredential = createMockAppleCredential(
        user: "001234.567890abcdef.1234",
        email: longEmail
    )

    // 执行测试
    let result = try await authService.signInWithApple(credential: mockCredential)

    // 验证结果
    XCTAssertEqual(result.user.email, longEmail)
    XCTAssertTrue(result.user.displayName.count <= 20) // 显示名应该被截断
}
```

##### T1.5: 特殊字符邮箱测试
```swift
func testSpecialCharacterEmailHandling() async throws {
    // 测试数据 - 包含特殊字符的邮箱
    let specialEmail = "<EMAIL>"
    let mockCredential = createMockAppleCredential(
        user: "001234.567890abcdef.1234",
        email: specialEmail
    )

    // 执行测试
    let result = try await authService.signInWithApple(credential: mockCredential)

    // 验证结果
    XCTAssertEqual(result.user.email, specialEmail)
    XCTAssertNotNil(result.user.displayName)
}
```

#### 变体测试 (Variant Tests) - 边界与异常情况验证

**测试目标**: 验证系统在异常输入和边界条件下的健壮性

##### V1.1: 无效Token测试
```swift
func testInvalidAppleToken() async {
    // 测试数据 - 无效的Token
    let invalidCredential = createMockAppleCredential(
        user: "",  // 空用户ID
        email: "invalid-email"  // 无效邮箱格式
    )

    // 执行测试并验证异常
    do {
        let _ = try await authService.signInWithApple(credential: invalidCredential)
        XCTFail("应该抛出异常")
    } catch AuthError.invalidToken {
        // 预期的异常
        XCTAssertTrue(true)
    } catch {
        XCTFail("意外的异常类型: \(error)")
    }
}
```

##### V1.2: 网络连接失败测试
```swift
func testNetworkConnectionFailure() async {
    // 模拟网络连接失败
    let mockCredential = createMockAppleCredential(
        user: "001234.567890abcdef.1234",
        email: "<EMAIL>"
    )

    // 配置网络失败模拟
    MockNetworkService.simulateNetworkFailure = true

    // 执行测试并验证异常
    do {
        let _ = try await authService.signInWithApple(credential: mockCredential)
        XCTFail("应该抛出网络异常")
    } catch AuthError.networkError {
        XCTAssertTrue(true)
    } catch {
        XCTFail("意外的异常类型: \(error)")
    }

    // 清理
    MockNetworkService.simulateNetworkFailure = false
}
```

##### V1.3: 服务器错误响应测试
```swift
func testServerErrorResponse() async {
    // 模拟服务器返回500错误
    let mockCredential = createMockAppleCredential(
        user: "001234.567890abcdef.1234",
        email: "<EMAIL>"
    )

    // 配置服务器错误模拟
    MockNetworkService.simulateServerError = true

    // 执行测试并验证异常
    do {
        let _ = try await authService.signInWithApple(credential: mockCredential)
        XCTFail("应该抛出服务器异常")
    } catch AuthError.serverError {
        XCTAssertTrue(true)
    } catch {
        XCTFail("意外的异常类型: \(error)")
    }

    // 清理
    MockNetworkService.simulateServerError = false
}
```

### FC-04: Apple ID Token验证服务测试

#### 补间测试 (Tweening Tests)

##### T4.1: 有效Apple Token验证测试
```typescript
describe('Apple ID Token验证服务 - 补间测试', () => {
  test('T4.1: 有效Apple Token验证', async () => {
    // 测试数据 - 有效的Apple ID Token
    const validToken = generateMockAppleToken({
      sub: '001234.567890abcdef.1234',
      email: '<EMAIL>',
      email_verified: true,
      iss: 'https://appleid.apple.com',
      aud: 'com.senseword.app',
      exp: Math.floor(Date.now() / 1000) + 3600, // 1小时后过期
      iat: Math.floor(Date.now() / 1000)
    });

    // 执行测试
    const result = await verifyAppleIdToken(validToken, mockEnv);

    // 验证结果
    expect(result.id).toBe('001234.567890abcdef.1234');
    expect(result.email).toBe('<EMAIL>');
    expect(result.provider).toBe('apple');
    expect(result.emailVerified).toBe(true);
  });

  test('T4.2: 无邮箱Token验证', async () => {
    // 测试数据 - 不包含邮箱的Token
    const tokenWithoutEmail = generateMockAppleToken({
      sub: '001234.567890abcdef.1234',
      // email字段缺失
      iss: 'https://appleid.apple.com',
      aud: 'com.senseword.app',
      exp: Math.floor(Date.now() / 1000) + 3600,
      iat: Math.floor(Date.now() / 1000)
    });

    // 执行测试
    const result = await verifyAppleIdToken(tokenWithoutEmail, mockEnv);

    // 验证结果 - 应该生成私有邮箱
    expect(result.id).toBe('001234.567890abcdef.1234');
    expect(result.email).toContain('@privaterelay.appleid.com');
    expect(result.provider).toBe('apple');
    expect(result.emailVerified).toBe(false);
  });

  test('T4.3: 边界时间Token验证', async () => {
    // 测试数据 - 刚好在有效期内的Token
    const now = Math.floor(Date.now() / 1000);
    const borderlineToken = generateMockAppleToken({
      sub: '001234.567890abcdef.1234',
      email: '<EMAIL>',
      iss: 'https://appleid.apple.com',
      aud: 'com.senseword.app',
      exp: now + 60, // 1分钟后过期
      iat: now - 60  // 1分钟前签发
    });

    // 执行测试
    const result = await verifyAppleIdToken(borderlineToken, mockEnv);

    // 验证结果
    expect(result.id).toBe('001234.567890abcdef.1234');
    expect(result.email).toBe('<EMAIL>');
  });

  test('T4.4: 多种邮箱格式验证', async () => {
    const emailFormats = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    for (const email of emailFormats) {
      const token = generateMockAppleToken({
        sub: `user-${email.replace(/[^a-zA-Z0-9]/g, '')}`,
        email: email,
        iss: 'https://appleid.apple.com',
        aud: 'com.senseword.app',
        exp: Math.floor(Date.now() / 1000) + 3600,
        iat: Math.floor(Date.now() / 1000)
      });

      const result = await verifyAppleIdToken(token, mockEnv);
      expect(result.email).toBe(email);
    }
  });
});
```

#### 变体测试 (Variant Tests)

##### V4.1: 过期Token测试
```typescript
describe('Apple ID Token验证服务 - 变体测试', () => {
  test('V4.1: 过期Token验证失败', async () => {
    // 测试数据 - 已过期的Token
    const expiredToken = generateMockAppleToken({
      sub: '001234.567890abcdef.1234',
      email: '<EMAIL>',
      iss: 'https://appleid.apple.com',
      aud: 'com.senseword.app',
      exp: Math.floor(Date.now() / 1000) - 3600, // 1小时前过期
      iat: Math.floor(Date.now() / 1000) - 7200  // 2小时前签发
    });

    // 执行测试并验证异常
    await expect(verifyAppleIdToken(expiredToken, mockEnv))
      .rejects
      .toThrow('Token expired');
  });

  test('V4.2: 无效签发者Token', async () => {
    // 测试数据 - 错误的签发者
    const invalidIssuerToken = generateMockAppleToken({
      sub: '001234.567890abcdef.1234',
      email: '<EMAIL>',
      iss: 'https://fake-issuer.com', // 错误的签发者
      aud: 'com.senseword.app',
      exp: Math.floor(Date.now() / 1000) + 3600,
      iat: Math.floor(Date.now() / 1000)
    });

    // 执行测试并验证异常
    await expect(verifyAppleIdToken(invalidIssuerToken, mockEnv))
      .rejects
      .toThrow('Invalid issuer');
  });

  test('V4.3: 错误受众Token', async () => {
    // 测试数据 - 错误的受众
    const wrongAudienceToken = generateMockAppleToken({
      sub: '001234.567890abcdef.1234',
      email: '<EMAIL>',
      iss: 'https://appleid.apple.com',
      aud: 'com.wrong.app', // 错误的Bundle ID
      exp: Math.floor(Date.now() / 1000) + 3600,
      iat: Math.floor(Date.now() / 1000)
    });

    // 执行测试并验证异常
    await expect(verifyAppleIdToken(wrongAudienceToken, mockEnv))
      .rejects
      .toThrow('Invalid audience');
  });

  test('V4.4: 格式错误Token', async () => {
    const malformedTokens = [
      '', // 空字符串
      'invalid.token', // 缺少部分
      'header.payload.signature.extra', // 多余部分
      'not-a-jwt-token', // 完全不是JWT格式
      null, // null值
      undefined // undefined值
    ];

    for (const token of malformedTokens) {
      await expect(verifyAppleIdToken(token as any, mockEnv))
        .rejects
        .toThrow();
    }
  });
});
```

### FC-05: 用户查找或创建服务测试

#### 补间测试 (Tweening Tests)

##### T5.1: 新用户创建测试
```typescript
describe('用户查找或创建服务 - 补间测试', () => {
  test('T5.1: 新用户创建', async () => {
    // 测试数据
    const userInfo: ValidatedUserInfo = {
      id: 'new-user-123',
      email: '<EMAIL>',
      displayName: 'New User',
      provider: 'apple',
      emailVerified: true
    };

    // 执行测试
    const result = await findOrCreateUser(userInfo, mockEnv);

    // 验证结果
    expect(result.id).toBe('new-user-123');
    expect(result.email).toBe('<EMAIL>');
    expect(result.displayName).toBe('New User');
    expect(result.provider).toBe('apple');
    expect(result.isPro).toBe(false); // 新用户默认非Pro
    expect(result.subscription_expires_at).toBeNull();
    expect(result.createdAt).toBeDefined();
    expect(result.updatedAt).toBeDefined();
  });

  test('T5.2: 现有用户查找', async () => {
    // 预先创建用户
    const existingUser = await createTestUser({
      id: 'existing-user-456',
      email: '<EMAIL>',
      displayName: 'Existing User',
      provider: 'apple'
    });

    const userInfo: ValidatedUserInfo = {
      id: 'existing-user-456',
      email: '<EMAIL>',
      displayName: 'Existing User',
      provider: 'apple',
      emailVerified: true
    };

    // 执行测试
    const result = await findOrCreateUser(userInfo, mockEnv);

    // 验证结果 - 应该返回现有用户
    expect(result.id).toBe('existing-user-456');
    expect(result.email).toBe('<EMAIL>');
    expect(result.updatedAt).not.toBe(existingUser.updatedAt); // 更新时间应该改变
  });

  test('T5.3: Pro用户状态计算', async () => {
    // 创建有有效订阅的用户
    const futureDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30天后
    await createTestUser({
      id: 'pro-user-789',
      email: '<EMAIL>',
      subscription_expires_at: futureDate.toISOString()
    });

    const userInfo: ValidatedUserInfo = {
      id: 'pro-user-789',
      email: '<EMAIL>',
      displayName: 'Pro User',
      provider: 'apple',
      emailVerified: true
    };

    // 执行测试
    const result = await findOrCreateUser(userInfo, mockEnv);

    // 验证结果 - 应该是Pro用户
    expect(result.isPro).toBe(true);
    expect(result.subscription_expires_at).toBe(futureDate.toISOString());
  });
});
```

#### 变体测试 (Variant Tests)

##### V5.1: 数据库连接失败测试
```typescript
describe('用户查找或创建服务 - 变体测试', () => {
  test('V5.1: 数据库连接失败', async () => {
    // 模拟数据库连接失败
    mockEnv.DB.prepare = jest.fn().mockImplementation(() => {
      throw new Error('Database connection failed');
    });

    const userInfo: ValidatedUserInfo = {
      id: 'test-user',
      email: '<EMAIL>',
      displayName: 'Test User',
      provider: 'apple',
      emailVerified: true
    };

    // 执行测试并验证异常
    await expect(findOrCreateUser(userInfo, mockEnv))
      .rejects
      .toThrow('Database connection failed');
  });

  test('V5.2: 无效用户信息', async () => {
    const invalidUserInfos = [
      { id: '', email: '<EMAIL>', displayName: 'Test', provider: 'apple', emailVerified: true },
      { id: 'test', email: '', displayName: 'Test', provider: 'apple', emailVerified: true },
      { id: 'test', email: 'invalid-email', displayName: 'Test', provider: 'apple', emailVerified: true },
      null,
      undefined
    ];

    for (const userInfo of invalidUserInfos) {
      await expect(findOrCreateUser(userInfo as any, mockEnv))
        .rejects
        .toThrow();
    }
  });
});
```

### FC-06: JWT令牌生成服务测试

#### 补间测试 (Tweening Tests)

##### T6.1: 标准令牌生成测试
```typescript
describe('JWT令牌生成服务 - 补间测试', () => {
  test('T6.1: 标准令牌生成', async () => {
    // 测试数据
    const user: UserWithComputedProps = {
      id: 'test-user-123',
      email: '<EMAIL>',
      displayName: 'Test User',
      provider: 'apple',
      subscription_expires_at: null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isPro: false
    };

    // 执行测试
    const tokens = await generateTokens(user, mockEnv);

    // 验证结果
    expect(tokens.accessToken).toBeDefined();
    expect(tokens.refreshToken).toBeDefined();
    expect(typeof tokens.accessToken).toBe('string');
    expect(typeof tokens.refreshToken).toBe('string');

    // 验证Token格式 (JWT应该有3个部分)
    expect(tokens.accessToken.split('.').length).toBe(3);
    expect(tokens.refreshToken.split('.').length).toBe(3);
  });

  test('T6.2: Pro用户令牌生成', async () => {
    // 测试数据 - Pro用户
    const proUser: UserWithComputedProps = {
      id: 'pro-user-456',
      email: '<EMAIL>',
      displayName: 'Pro User',
      provider: 'apple',
      subscription_expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isPro: true
    };

    // 执行测试
    const tokens = await generateTokens(proUser, mockEnv);

    // 解析访问令牌验证Pro状态
    const accessPayload = parseJWT(tokens.accessToken);
    expect(accessPayload.isPro).toBe(true);
    expect(accessPayload.sub).toBe('pro-user-456');
    expect(accessPayload.email).toBe('<EMAIL>');
  });
});
```

#### 变体测试 (Variant Tests)

##### V6.1: 无效用户数据测试
```typescript
describe('JWT令牌生成服务 - 变体测试', () => {
  test('V6.1: 无效用户数据', async () => {
    const invalidUsers = [
      null,
      undefined,
      { id: '', email: '<EMAIL>' }, // 缺少必需字段
      { id: 'test', email: '' }, // 空邮箱
      {} // 空对象
    ];

    for (const user of invalidUsers) {
      await expect(generateTokens(user as any, mockEnv))
        .rejects
        .toThrow();
    }
  });
});
```

## 测试执行指南

### 前端测试 (iOS)

#### 环境准备
```bash
# 1. 安装依赖
cd iOS
swift package resolve

# 2. 运行测试
xcodebuild test -scheme AuthDomain -destination 'platform=iOS Simulator,name=iPhone 15'
```

#### 测试配置
```swift
// Tests/AuthDomainTests/TestConfiguration.swift
class TestConfiguration {
    static let mockBackendURL = "https://mock-api.senseword.test"
    static let testBundleID = "com.senseword.app.test"

    static func setupMockEnvironment() {
        // 配置模拟网络服务
        MockNetworkService.configure()

        // 配置模拟Apple认证
        MockAppleAuthService.configure()
    }
}
```

### 后端测试 (Cloudflare Workers)

#### 环境准备
```bash
# 1. 安装依赖
cd cloudflare/workers/api
npm install

# 2. 运行测试
npm test

# 3. 运行覆盖率测试
npm run test:coverage
```

#### 测试配置
```typescript
// tests/setup.ts
import { beforeAll, afterAll } from 'vitest';

beforeAll(async () => {
  // 设置测试数据库
  await setupTestDatabase();

  // 配置模拟环境变量
  process.env.JWT_SECRET = 'test-secret-key';
  process.env.APPLE_BUNDLE_ID = 'com.senseword.app.test';
});

afterAll(async () => {
  // 清理测试数据
  await cleanupTestDatabase();
});
```

## 测试数据管理

### 模拟数据生成器

#### Apple Token生成器
```typescript
// tests/utils/mockTokenGenerator.ts
export function generateMockAppleToken(payload: Partial<AppleIdTokenPayload>): string {
  const defaultPayload = {
    iss: 'https://appleid.apple.com',
    aud: 'com.senseword.app.test',
    exp: Math.floor(Date.now() / 1000) + 3600,
    iat: Math.floor(Date.now() / 1000),
    sub: 'default-user-id',
    email: '<EMAIL>',
    email_verified: true,
    ...payload
  };

  return createMockJWT(defaultPayload, 'test-secret');
}
```

#### 用户数据生成器
```typescript
// tests/utils/mockUserGenerator.ts
export function createMockUser(overrides: Partial<UserWithComputedProps> = {}): UserWithComputedProps {
  return {
    id: 'mock-user-' + Math.random().toString(36).substr(2, 9),
    email: '<EMAIL>',
    displayName: 'Mock User',
    provider: 'apple',
    subscription_expires_at: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    isPro: false,
    ...overrides
  };
}
```

## 测试执行计划

### 阶段1: 单元测试 (预计2天)
- [ ] FC-01 前端Apple登录触发器测试
- [ ] FC-02 前端认证服务处理器测试
- [ ] FC-04 Apple ID Token验证服务测试
- [ ] FC-05 用户查找或创建服务测试
- [ ] FC-06 JWT令牌生成服务测试

### 阶段2: 集成测试 (预计1天)
- [ ] FC-03 后端认证API端点测试
- [ ] FC-07 认证中间件测试
- [ ] 端到端认证流程测试

### 阶段3: 性能测试 (预计0.5天)
- [ ] 并发认证请求测试
- [ ] 数据库性能测试
- [ ] JWT生成性能测试

### 阶段4: 安全测试 (预计0.5天)
- [ ] Token安全性验证
- [ ] 认证绕过尝试测试
- [ ] 数据泄露防护测试

## 预期测试结果

### 成功标准
- **所有补间测试通过率**: 100%
- **变体测试通过率**: ≥95%
- **代码覆盖率**: ≥90%
- **性能基准**: 认证响应时间 <2秒

### 质量指标
- **可靠性**: 无关键功能失败
- **安全性**: 通过所有安全测试
- **可维护性**: 测试代码清晰易懂
- **扩展性**: 支持未来功能扩展

---

**测试报告状态**: 待执行
**最后更新**: 2025-06-23
**下次更新**: 测试执行完成后