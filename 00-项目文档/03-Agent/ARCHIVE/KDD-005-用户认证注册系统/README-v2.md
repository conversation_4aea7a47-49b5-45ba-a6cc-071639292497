# 📋 KDD-005: 用户认证注册系统 (Session认证版)

## 🎯 项目概述

用户认证注册系统是 SenseWord 应用的核心认证服务，基于 Apple Sign In 实现安全的用户身份验证和自动注册功能。采用微服务架构，支持永久Session会话管理，实现"登录一次，永远有效"的用户体验。

### 🏗️ 架构特点
- **微服务架构**: 独立的认证服务 (auth.senseword.com)，与核心API分离
- **Apple认证**: 基于Apple Sign In，无需密码，安全可靠
- **永久Session**: 替代JWT机制，无过期时间，用户无需重复登录
- **多设备支持**: 同一用户可在多设备同时使用，每设备独立Session
- **即时撤销**: 支持管理员或用户主动撤销任何Session

### 🛠️ 技术栈
- **后端**: Cloudflare Workers + Hono框架
- **数据库**: Cloudflare D1 (SQLite) - users表 + sessions表
- **认证**: Apple Sign In + Session管理
- **客户端**: iOS Swift + Keychain安全存储

## 🔑 核心能力、接口与数据契约

### 后端核心能力 (Backend Core Capabilities)
- **Apple ID 认证验证**: 验证Apple ID Token，获取用户身份信息
- **用户管理**: 自动创建新用户或查找现有用户
- **Session生成**: 为已验证用户创建永久有效的Session ID
- **Session验证**: 验证Session ID有效性，获取用户信息
- **Session撤销**: 支持单个或批量撤销Session
- **设备管理**: 记录和管理用户设备信息
- **安全审计**: 完整的认证日志和错误处理

### 前端接口事务 (Frontend Interface Transactions)
- **Apple登录认证**: 发起Apple Sign In流程，获取Session ID
- **用户信息获取**: 基于有效Session获取当前用户详细信息
- **Session管理**: 安全存储Session ID，无需刷新机制
- **错误处理**: 网络异常、认证失败、Session失效的优雅处理
- **多设备同步**: 支持同一用户在多设备间的永久会话

### 核心数据结构 (DTO) 定义

#### 后端数据结构

```typescript
// 请求：Apple登录认证
interface LoginRequestBE {
  idToken: string;                    // Apple ID Token (JWT格式)
  provider: 'apple';                  // 认证提供方 (固定为apple)
}

// 响应：登录成功返回 (Session版本)
interface SessionLoginSuccessResponseBE {
  success: true;
  session: {
    sessionId: string;                // 永久有效的Session ID
    user: {
      id: string;                     // 用户ID
      email: string;                  // 用户邮箱
      displayName: string;            // 显示名称
      isPro: boolean;                 // Pro状态
    };
  };
}

// 响应：用户信息
interface UserProfileResponseBE {
  success: true;
  user: {
    id: string;                       // 用户唯一标识符
    email: string;                    // 用户邮箱
    displayName: string;              // 显示名称
    isPro: boolean;                   // Pro状态
    createdAt: string;                // 创建时间 (ISO 8601)
  };
}

// 响应：错误信息
interface ErrorResponseBE {
  success: false;
  error: 'INVALID_TOKEN' | 'USER_CREATION_FAILED' | 'SYSTEM_ERROR' | 'UNAUTHORIZED' | 'INVALID_SESSION';
  message: string;
  timestamp: string;                  // ISO 8601格式
}

// Session记录结构 (数据库表对应)
interface SessionRecordBE {
  session_id: string;                 // Session唯一标识符 (格式: "sess_" + 20位随机字符)
  user_id: string;                    // 关联的用户ID
  created_at: string;                 // ISO 8601格式的Session创建时间
  last_active_at: string;             // ISO 8601格式的最后活跃时间
  is_active: boolean;                 // Session是否有效
  device_info?: string;               // 设备信息 (可选)
}

// 健康检查响应
interface HealthCheckResponseBE {
  status: 'healthy' | 'unhealthy';
  service: 'auth-worker';
  timestamp: string;                  // ISO 8601格式
  environment: string;                // 环境标识
  version: string;                    // 服务版本
}
```

#### 前端数据结构

```typescript
// 认证会话 (前端存储) - Session版本
interface AuthSessionFE {
  sessionId: string;                  // 永久有效的Session ID
  user: UserProfileFE;
}

// 用户信息 (前端模型)
interface UserProfileFE {
  id: string;                         // 用户唯一标识符
  email: string;                      // 用户邮箱
  displayName: string;                // 显示名称
  isPro: boolean;                     // Pro状态
  createdAt: string;                  // 创建时间
}

// Apple认证结果 (iOS原生)
interface AppleAuthResultFE {
  identityToken: string;              // Apple ID Token
  authorizationCode?: string;         // 授权码 (可选)
  user: string;                       // 用户唯一标识符
  fullName?: string;                  // 用户姓名 (可选)
  email?: string;                     // 用户邮箱 (可选)
}
```

## 🌐 服务地址

### 生产环境
- **认证服务**: `https://senseword-auth-worker.zhouqi-aaha.workers.dev`
- **健康检查**: `https://senseword-auth-worker.zhouqi-aaha.workers.dev/api/v1/auth/health`

### 本地开发环境
- **认证服务**: `http://localhost:8787`
- **健康检查**: `http://localhost:8787/api/v1/auth/health`

## 📡 API端点列表

### 🔐 认证相关端点

#### POST /api/v1/auth/login
Apple登录认证端点

**请求示例**:
```bash
curl -X POST "https://senseword-auth-worker.zhouqi-aaha.workers.dev/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "idToken": "mock-apple-token-dev-user",
    "provider": "apple"
  }'
```

**成功响应** (HTTP 200):
```json
{
  "success": true,
  "session": {
    "sessionId": "sess_abc123def456ghi789jk",
    "user": {
      "id": "dev-user-001",
      "email": "<EMAIL>",
      "displayName": "SenseWord开发用户",
      "isPro": false
    }
  }
}
```

#### GET /api/v1/users/me
获取当前用户信息 (需要Session认证)

**请求示例**:
```bash
curl -X GET "https://senseword-auth-worker.zhouqi-aaha.workers.dev/api/v1/users/me" \
  -H "Authorization: Bearer sess_abc123def456ghi789jk"
```

**成功响应** (HTTP 200):
```json
{
  "success": true,
  "user": {
    "id": "dev-user-001",
    "email": "<EMAIL>",
    "displayName": "SenseWord开发用户",
    "isPro": false,
    "createdAt": "2025-01-02T10:30:00.000Z"
  }
}
```

### 🛠️ 开发工具端点

#### GET /api/v1/auth/mock-login
开发环境模拟登录 (仅开发环境可用)

**请求示例**:
```bash
curl -X GET "https://senseword-auth-worker.zhouqi-aaha.workers.dev/api/v1/auth/mock-login?userId=dev-user-001"
```

#### GET /api/v1/auth/health
健康检查端点

**请求示例**:
```bash
curl -X GET "https://senseword-auth-worker.zhouqi-aaha.workers.dev/api/v1/auth/health"
```

**响应示例**:
```json
{
  "status": "healthy",
  "service": "auth-worker",
  "timestamp": "2025-01-02T10:30:00.000Z",
  "environment": "production",
  "version": "1.0.0"
}
```

## 🧪 预设测试数据

### 测试账号

#### 1. 开发用户 (免费账号)
- **用户ID**: `dev-user-001`
- **邮箱**: `<EMAIL>`
- **显示名**: `SenseWord开发用户`
- **Pro状态**: `false`
- **模拟Token**: `mock-apple-token-dev-user`

#### 2. Pro用户 (付费账号)
- **用户ID**: `pro-user-001`
- **邮箱**: `<EMAIL>`
- **显示名**: `SenseWord Pro用户`
- **Pro状态**: `true`
- **模拟Token**: `mock-apple-token-pro-user`

### 预设Session ID示例
- **开发用户Session**: `sess_dev001_sample_session`
- **Pro用户Session**: `sess_pro001_sample_session`

## 🧪 测试方法

### 方法1: 预设模拟Token测试 (最简单)
使用预定义的模拟Token进行认证测试

```bash
# 测试开发用户登录
curl -X POST "https://senseword-auth-worker.zhouqi-aaha.workers.dev/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "idToken": "mock-apple-token-dev-user",
    "provider": "apple"
  }'

# 测试Pro用户登录
curl -X POST "https://senseword-auth-worker.zhouqi-aaha.workers.dev/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "idToken": "mock-apple-token-pro-user",
    "provider": "apple"
  }'
```

### 方法2: 模拟登录端点测试 (快速获取Session)
通过GET请求快速生成测试Session

```bash
# 获取开发用户Session
curl -X GET "https://senseword-auth-worker.zhouqi-aaha.workers.dev/api/v1/auth/mock-login?userId=dev-user-001"

# 获取Pro用户Session
curl -X GET "https://senseword-auth-worker.zhouqi-aaha.workers.dev/api/v1/auth/mock-login?userId=pro-user-001"
```

### 方法3: 完整认证流程测试 (最完整)
获取Session后测试用户信息获取

```bash
# 步骤1: 获取Session
RESPONSE=$(curl -s -X GET "https://senseword-auth-worker.zhouqi-aaha.workers.dev/api/v1/auth/mock-login?userId=dev-user-001")
SESSION_ID=$(echo $RESPONSE | jq -r '.session.sessionId')

# 步骤2: 使用Session获取用户信息
curl -X GET "https://senseword-auth-worker.zhouqi-aaha.workers.dev/api/v1/users/me" \
  -H "Authorization: Bearer $SESSION_ID"

# 步骤3: 验证Session永久有效性 (无过期时间)
echo "Session ID: $SESSION_ID 永久有效，无需刷新"
```

## 🔧 本地开发环境

### 环境要求
- **Node.js**: v18+
- **npm**: v8+
- **Cloudflare CLI**: `npm install -g wrangler`

### 设置步骤

#### 1. 克隆项目并安装依赖
```bash
cd cloudflare/workers/auth
npm install
```

#### 2. 配置环境变量
创建 `.dev.vars` 文件：
```bash
# Apple认证配置
APPLE_BUNDLE_ID=com.senseword.app.dev
NODE_ENV=development

# 数据库配置 (本地开发使用远程D1)
# DB 绑定在 wrangler.toml 中配置
```

#### 3. 数据库设置
```bash
# 应用数据库迁移
npx wrangler d1 migrations apply senseword-users-db --local

# 验证数据库结构
npx wrangler d1 execute senseword-users-db --local --command="SELECT name FROM sqlite_master WHERE type='table';"
```

#### 4. 启动开发服务器
```bash
npm run dev
# 或
npx wrangler dev --local
```

#### 5. 验证服务运行
```bash
# 健康检查
curl http://localhost:8787/api/v1/auth/health

# 模拟登录测试
curl "http://localhost:8787/api/v1/auth/mock-login?userId=dev-user-001"
```

### 开发工具

#### 数据库管理
```bash
# 查看用户表
npx wrangler d1 execute senseword-users-db --local --command="SELECT * FROM users;"

# 查看Session表
npx wrangler d1 execute senseword-users-db --local --command="SELECT * FROM sessions;"

# 清理测试Session
npx wrangler d1 execute senseword-users-db --local --command="DELETE FROM sessions WHERE device_info LIKE '%test%';"
```

#### 日志监控
```bash
# 实时查看日志
npx wrangler tail

# 过滤认证相关日志
npx wrangler tail --format=pretty | grep "认证"
```

## 💡 关键概念说明

### Session认证系统优势

#### 与JWT对比
| 特性 | JWT方案 | Session方案 |
|------|---------|-------------|
| **用户体验** | 需要定期重新登录 | 永远不需要重新登录 |
| **安全控制** | 无法撤销未过期令牌 | 可以即时撤销任何Session |
| **服务器状态** | 无状态（但实际需要黑名单） | 有状态（但简单直接） |
| **实现复杂度** | 需要刷新机制、过期处理 | 简单的数据库查询 |
| **性能** | 每次验证需要解密和验证签名 | 简单的数据库索引查询 |
| **密钥管理** | 需要硬编码JWT_SECRET | 完全不需要任何密钥 |

#### 核心设计原则
1. **永久有效**: Session无过期时间，用户登录一次即可永久使用
2. **即时撤销**: 管理员可通过设置`is_active = false`立即撤销任何Session
3. **多设备支持**: 同一用户可在多个设备上同时登录，每个设备一个Session
4. **简单验证**: Session验证就是简单的数据库查询，无需加密解密

### 微服务架构优势

#### 安全策略差异化
- **auth-worker**: 绝不缓存、严格CORS、最高安全级别
- **api-worker**: 宽松缓存策略、高频读取优化

#### 独立部署能力
- **认证服务**: 专注身份验证，可独立扩展和维护
- **业务服务**: 专注核心功能，通过Session验证获取用户信息

#### 容错隔离
- **认证故障**: 不影响已登录用户的业务功能使用
- **业务故障**: 不影响新用户的登录注册流程

## 🛡️ 安全特性

### 认证安全
- **Apple官方认证**: 委托Apple处理身份验证，无需存储密码
- **Token验证**: 严格验证Apple ID Token的签名和有效性
- **用户隔离**: 每个用户的Session完全独立，无法跨用户访问

### 传输安全
- **HTTPS强制**: 所有API端点强制使用HTTPS
- **CORS配置**: 严格的跨域资源共享策略
- **安全头**: 完整的安全响应头配置

### 数据安全
- **Session撤销**: 支持即时撤销可疑或泄露的Session
- **设备追踪**: 记录设备信息，便于安全审计
- **活跃度监控**: 追踪Session活跃时间，清理僵尸Session

### 审计日志
- **完整日志**: 记录所有认证尝试和结果
- **错误追踪**: 详细的错误信息和堆栈跟踪
- **性能监控**: 响应时间和成功率统计

## ❌ 错误处理

### 常见错误码

#### INVALID_TOKEN
- **原因**: Apple ID Token无效或格式错误
- **解决方案**: 检查Token格式，确保从Apple获取的Token完整

#### INVALID_SESSION
- **原因**: Session ID无效、已撤销或不存在
- **解决方案**: 重新登录获取新的Session ID

#### USER_CREATION_FAILED
- **原因**: 数据库创建用户失败
- **解决方案**: 检查数据库连接和用户数据格式

#### SYSTEM_ERROR
- **原因**: 服务器内部错误
- **解决方案**: 查看服务器日志，联系技术支持

### 错误响应格式
```json
{
  "success": false,
  "error": "INVALID_SESSION",
  "message": "Session无效或已过期",
  "timestamp": "2025-01-02T10:30:00.000Z"
}
```

### 问题排查步骤

#### 1. 认证失败
```bash
# 检查健康状态
curl https://senseword-auth-worker.zhouqi-aaha.workers.dev/api/v1/auth/health

# 使用模拟登录测试
curl "https://senseword-auth-worker.zhouqi-aaha.workers.dev/api/v1/auth/mock-login?userId=dev-user-001"
```

#### 2. Session验证失败
```bash
# 检查Session格式 (应该以"sess_"开头)
echo "Session ID格式: sess_xxxxxxxxxxxxxx"

# 测试Session有效性
curl -X GET "https://senseword-auth-worker.zhouqi-aaha.workers.dev/api/v1/users/me" \
  -H "Authorization: Bearer YOUR_SESSION_ID"
```

#### 3. 网络连接问题
```bash
# 测试网络连通性
ping senseword-auth-worker.zhouqi-aaha.workers.dev

# 检查DNS解析
nslookup senseword-auth-worker.zhouqi-aaha.workers.dev
```

## 🔗 集成指南

### Swift集成示例

#### SessionManager (替代TokenManager)
```swift
import Foundation
import Security

class SessionManager {
    private let keychainService = "com.senseword.app.session"
    private let sessionIdKey = "sessionId"

    // 存储Session ID
    func saveSession(_ sessionId: String) {
        let data = sessionId.data(using: .utf8)!

        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: sessionIdKey,
            kSecValueData as String: data
        ]

        SecItemDelete(query as CFDictionary)
        SecItemAdd(query as CFDictionary, nil)
    }

    // 获取Session ID
    func getSessionId() -> String? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: sessionIdKey,
            kSecReturnData as String: true
        ]

        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)

        guard status == errSecSuccess,
              let data = result as? Data,
              let sessionId = String(data: data, encoding: .utf8) else {
            return nil
        }

        return sessionId
    }

    // 清除Session
    func clearSession() {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: sessionIdKey
        ]

        SecItemDelete(query as CFDictionary)
    }
}
```

#### AuthService (Session版本)
```swift
import AuthenticationServices

class AuthService: ObservableObject {
    private let sessionManager = SessionManager()
    private let baseURL = "https://senseword-auth-worker.zhouqi-aaha.workers.dev"

    @Published var currentUser: UserProfile?
    @Published var isAuthenticated = false

    // Apple Sign In
    func signInWithApple() async throws {
        let request = ASAuthorizationAppleIDProvider().createRequest()
        request.requestedScopes = [.fullName, .email]

        let controller = ASAuthorizationController(authorizationRequests: [request])
        // ... Apple认证流程

        // 获取到Apple ID Token后
        try await authenticateWithBackend(idToken: appleIdToken)
    }

    // 后端认证
    private func authenticateWithBackend(idToken: String) async throws {
        let url = URL(string: "\(baseURL)/api/v1/auth/login")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let body = [
            "idToken": idToken,
            "provider": "apple"
        ]
        request.httpBody = try JSONSerialization.data(withJSONObject: body)

        let (data, _) = try await URLSession.shared.data(for: request)
        let response = try JSONDecoder().decode(SessionLoginResponse.self, from: data)

        // 保存Session ID
        sessionManager.saveSession(response.session.sessionId)
        currentUser = response.session.user
        isAuthenticated = true
    }

    // 获取用户信息
    func getCurrentUser() async throws {
        guard let sessionId = sessionManager.getSessionId() else {
            throw AuthError.noSession
        }

        let url = URL(string: "\(baseURL)/api/v1/users/me")!
        var request = URLRequest(url: url)
        request.setValue("Bearer \(sessionId)", forHTTPHeaderField: "Authorization")

        let (data, _) = try await URLSession.shared.data(for: request)
        let response = try JSONDecoder().decode(UserProfileResponse.self, from: data)

        currentUser = response.user
        isAuthenticated = true
    }

    // 登出
    func signOut() {
        sessionManager.clearSession()
        currentUser = nil
        isAuthenticated = false
    }
}
```

### 微服务中间件集成

#### 方法1: 直接Session验证
```typescript
// 在其他微服务中验证Session
import { authenticateSessionRequest } from './middleware/session-auth.middleware';

app.get('/api/v1/protected-endpoint', async (c) => {
  try {
    // 验证Session并获取用户信息
    const user = await authenticateSessionRequest(
      c.req.raw,
      'https://senseword-auth-worker.zhouqi-aaha.workers.dev'
    );

    // 使用用户信息执行业务逻辑
    const result = await someBusinessLogic(user.id, user.isPro);

    return c.json({ success: true, data: result });
  } catch (error) {
    return c.json({ success: false, error: error.message }, 401);
  }
});
```

#### 方法2: 中间件模式
```typescript
// 创建认证中间件
const authMiddleware = async (c: Context, next: Next) => {
  try {
    const user = await authenticateSessionRequest(c.req.raw);
    c.set('user', user);
    await next();
  } catch (error) {
    return c.json({ success: false, error: 'Unauthorized' }, 401);
  }
};

// 应用中间件
app.use('/api/v1/protected/*', authMiddleware);

app.get('/api/v1/protected/data', async (c) => {
  const user = c.get('user');
  // 使用已验证的用户信息
  return c.json({ message: `Hello ${user.displayName}` });
});
```

## 📈 后续开发

### 已完成功能 ✅
- [x] **Apple Sign In集成**: 完整的Apple ID Token验证
- [x] **Session管理系统**: 永久Session生成、验证、撤销
- [x] **用户自动注册**: 基于Apple认证的用户创建
- [x] **微服务架构**: 独立的auth-worker服务
- [x] **数据库设计**: users表和sessions表结构
- [x] **安全防护**: CORS、安全头、错误处理
- [x] **开发工具**: 模拟登录、健康检查、测试账号
- [x] **API文档**: 完整的使用指南和示例

### 待实现功能 🔄
- [ ] **iOS前端集成**: Swift认证服务和UI界面
- [ ] **Session管理界面**: 用户查看和管理自己的设备Session
- [ ] **管理员工具**: 批量Session管理和用户管理
- [ ] **监控仪表板**: 认证成功率、性能指标、错误统计
- [ ] **自动清理**: 长期不活跃Session的自动清理机制

### 性能优化计划 🚀
- [ ] **数据库索引优化**: 基于实际查询模式优化索引
- [ ] **缓存策略**: 用户信息缓存减少数据库查询
- [ ] **连接池**: 数据库连接池优化
- [ ] **CDN集成**: 静态资源和API响应缓存

### 安全增强计划 🛡️
- [ ] **设备指纹**: 更详细的设备识别和异常检测
- [ ] **地理位置**: IP地理位置验证和异常登录提醒
- [ ] **速率限制**: 更精细的API速率限制策略
- [ ] **安全审计**: 完整的安全事件日志和分析

## 🔧 技术支持

### 常见问题解答

#### Q: Session ID的格式是什么？
A: Session ID格式为 `sess_` + 20位随机字符，例如：`sess_abc123def456ghi789jk`

#### Q: Session会过期吗？
A: 不会。Session是永久有效的，除非被主动撤销或用户登出。

#### Q: 如何撤销Session？
A: 可以通过数据库操作设置 `is_active = false` 来撤销Session，或调用相应的API端点。

#### Q: 支持多设备登录吗？
A: 支持。同一用户可以在多个设备上同时登录，每个设备有独立的Session。

#### Q: 如何处理Apple认证失败？
A: 检查Apple ID Token格式，确保网络连接正常，查看错误日志获取详细信息。

### 监控和健康检查

#### 健康检查脚本
```bash
#!/bin/bash
# health-check.sh

AUTH_URL="https://senseword-auth-worker.zhouqi-aaha.workers.dev"

echo "🔍 检查认证服务健康状态..."
HEALTH_RESPONSE=$(curl -s "$AUTH_URL/api/v1/auth/health")
echo "健康检查响应: $HEALTH_RESPONSE"

echo "🧪 测试模拟登录..."
MOCK_RESPONSE=$(curl -s "$AUTH_URL/api/v1/auth/mock-login?userId=dev-user-001")
echo "模拟登录响应: $MOCK_RESPONSE"

echo "✅ 健康检查完成"
```

#### 关键指标监控
- **认证成功率**: 目标 >95%
- **平均响应时间**: 目标 <2秒
- **Session验证性能**: 目标 <100ms
- **错误率**: 目标 <5%

### 问题报告

如遇到问题，请提供以下信息：
1. **错误信息**: 完整的错误响应和状态码
2. **请求详情**: 请求URL、方法、头部、请求体
3. **环境信息**: 生产环境/开发环境、时间戳
4. **重现步骤**: 详细的操作步骤
5. **期望结果**: 预期的正确行为

### 技术文档参考
- **KDD-005函数契约补间链**: 详细的技术实现规范
- **KDD-012 Session认证系统**: Session机制的完整设计文档
- **Apple Sign In文档**: Apple官方认证集成指南
- **Cloudflare Workers文档**: 部署和配置指南

---

**文档版本**: v2.0 (Session认证版)
**最后更新**: 2025-01-02
**状态**: ✅ 生产环境就绪
**支持特性**: 微服务架构、Apple Sign In、永久Session管理