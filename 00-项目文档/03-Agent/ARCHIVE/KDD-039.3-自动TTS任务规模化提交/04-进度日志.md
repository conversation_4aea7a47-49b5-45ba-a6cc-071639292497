# KDD-039.3 自动TTS任务规模化提交 - 进度日志

## 📅 项目时间线
- **开始时间**: 2025-07-16
- **核心功能完成**: 2025-07-16 23:30
- **架构重构完成**: 2025-07-17 00:30
- **关键问题修复**: 2025-07-17 03:30
- **当前状态**: 单词级循环架构，积压任务监控正常，系统稳定运行
- **最后更新**: 2025-07-17 03:30

## 🎯 项目目标完成情况

### ✅ 已完成的核心功能

#### **1. 积压任务概念澄清与实现**
- [x] **概念澄清**: 积压任务 = 所有非completed任务（pending + processing + failed + submitted）
- [x] **Worker API修改**: 添加backlog字段直接返回积压任务数量
- [x] **数据结构更新**: 将pending改为backlog，语义更清晰
- [x] **计算逻辑**: backlog = total - completed

#### **2. 异步解耦架构优化**
- [x] **同步提交**: 02脚本立即获得写入结果，无需等待音频处理
- [x] **异步处理**: Worker通过定时任务自行管理Azure TTS调用
- [x] **状态透明**: 提交时返回实时系统状态和积压任务数量
- [x] **完全解耦**: 提交速度不受Azure API响应时间影响

#### **3. 基于积压任务绝对数量的四级流量控制**
- [x] **涡轮模式**: 积压 < 200，间隔0.5秒，快速补充任务队列
- [x] **正常模式**: 积压 200-499，间隔2.0秒，维持稳定流量
- [x] **减速模式**: 积压 500-999，间隔5.0秒，给系统喘息时间
- [x] **暂停模式**: 积压 ≥ 1000，等待60秒，暂停提交等待消化
- [x] **成本保护**: 成本达到95%时立即停止，等待手动切换API Key

#### **4. 生产级日志系统**
- [x] **双重输出**: 详细日志写入文件 + 终端简洁显示
- [x] **日志目录**: `/logs/coordinator_YYYYMMDD_HHMMSS.log`
- [x] **自动创建**: 目录不存在时自动创建
- [x] **分级日志**: INFO级别详细信息 + WARNING级别终端显示
- [x] **完整记录**: 启动配置、批次处理、流量控制评估、错误信息

#### **5. 任务选择逻辑优化**
- [x] **查询条件修复**: 只选择pending和failed状态任务，避免重复提交
- [x] **状态同步**: 确保本地数据库查询与云端Worker状态一致
- [x] **积压监控准确**: 积压任务数量准确反映系统实际状态
- [x] **解析逻辑优化**: 使用正则表达式精确解析系统状态信息

#### **5. 文件命名规范化**
- [x] **统一前缀**: 所有脚本使用`tts_`前缀 + 数字序号
- [x] **可导入性**: 解决Python模块名不能以数字开头的问题
- [x] **工作流程**: 保持数字序号表示执行顺序
- [x] **文件重命名**:
  - `02_submit_tts_tasks_optimized.py` → `tts_02_submit_tasks_optimized.py`
  - `03_migrate_tts_assets_structure.py` → `tts_03_migrate_assets_structure.py`
  - `04_large_scale_tts_submitter.py` → `tts_04_large_scale_coordinator.py`

## 🔧 技术实现细节

### **Worker API层修改**
```typescript
// 类型定义更新
system_stats?: {
  total: number;
  completed: number;
  backlog: number;  // 新增：积压任务数
  completion_rate: string;
}

// 实际返回数据
systemStats = {
  total: coreStats.total,
  completed: coreStats.completed,
  backlog: coreStats.total - coreStats.completed,  // 积压任务数
  completion_rate: completion_rate
};
```

### **02脚本显示优化**
```python
# 新的显示格式
print(f"📈 系统状态: {total}总任务, {completed}已完成, {backlog}积压 ({completion_rate})")
```

### **04脚本流量控制核心逻辑**
```python
# 基于积压任务绝对数量的四级流量控制
if backlog_tasks >= 1000:      # 暂停模式
    time.sleep(60)
elif backlog_tasks >= 500:     # 减速模式
    time.sleep(5.0)
elif backlog_tasks >= 200:     # 正常模式
    time.sleep(2.0)
else:                          # 涡轮模式
    time.sleep(0.5)
```

### **日志系统架构**
```python
# 文件handler - 详细日志
file_handler = logging.FileHandler(log_file, encoding='utf-8')
file_handler.setLevel(logging.INFO)

# 控制台handler - 简洁输出
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.WARNING)  # 只显示警告和错误
```

## 📊 系统性能优化成果

### **流量控制效果**
- **目标导向**: 始终保持200个积压任务，让Worker满负荷运行
- **动态调节**: 根据积压情况自动在四种模式间切换
- **精确匹配**: 提交速度与Worker处理能力精确匹配
- **成本效率**: 在API Key允许范围内实现最大处理速度

### **用户体验提升**
- **终端简洁**: 只显示关键进度信息
- **详细日志**: 完整的调试和监控信息写入文件
- **状态透明**: 实时显示积压任务数和流量模式
- **错误分级**: 成本问题停止程序，积压问题自动重试

## 🚀 当前系统运行效果

### **终端输出示例**
```bash
🚀 TTS智能协调器启动
📝 日志文件: /path/to/logs/coordinator_20250716_230000.log
🔧 配置: 3 项配置加载完成
📊 开始处理 35000 个优先单词
📈 进度: 1/700 (0.1%) | 积压: 150 | 模式: turbo
📈 进度: 2/700 (0.3%) | 积压: 180 | 模式: turbo
⏸️ 积压过多(1200)，等待1分钟...
📈 进度: 3/700 (0.4%) | 积压: 450 | 模式: slow
✅ 协调完成: 34950 成功, 50 失败 (99.9%)
```

### **日志文件内容示例**
```
2025-07-16 23:00:00,123 - INFO - TTS智能协调器启动
2025-07-16 23:00:00,124 - INFO - 成本健康阈值: 95.0%
2025-07-16 23:00:00,125 - INFO - 目标积压任务数: 200
2025-07-16 23:00:01,200 - INFO - 协调批次 1/700: 50 个单词
2025-07-16 23:00:01,201 - INFO - 批次单词: ['word1', 'word2', ...]
2025-07-16 23:00:02,300 - INFO - 流量控制评估:
2025-07-16 23:00:02,301 - INFO -    积压任务数: 150 (目标: 200)
2025-07-16 23:00:02,302 - INFO -    流量模式: turbo
2025-07-16 23:00:02,303 - INFO -    建议间隔: 0.5秒
```

## 📋 配置参数完整列表

### **tts_04_large_scale_coordinator.py 参数**
- `--cost-threshold`: 成本健康阈值百分比 (默认: 95)
- `--target-backlog`: 目标积压任务数 (默认: 200)
- `--max-backlog`: 最大积压任务数 (默认: 500)
- `--critical-backlog`: 临界积压任务数 (默认: 1000)
- `--priority-words`: 优先处理的单词数量 (默认: 35000)
- `--batch-size`: 批量大小 (默认: 50)
- `--turbo-interval`: 涡轮模式间隔(秒) (默认: 0.5)
- `--normal-interval`: 正常间隔(秒) (默认: 2.0)
- `--slow-interval`: 减速间隔(秒) (默认: 5.0)
- `--config`: 配置文件路径

## 🔄 下一步规划

### **待优化功能**
- [ ] 日志文件轮转和清理机制
- [ ] 日志级别动态配置
- [ ] 更详细的性能监控指标
- [ ] 批处理结果统计和分析

### **可能的扩展功能**
- [ ] 多API Key自动轮换
- [ ] 更智能的积压任务预测
- [ ] 系统负载自适应调节
- [ ] 实时监控仪表板

## 💡 关键技术决策记录

### **1. 为什么选择积压任务绝对数量而不是比例？**
- **精确控制**: 绝对数量能更精确地反映系统负载
- **Worker匹配**: 与Worker实际处理能力直接对应
- **简化逻辑**: 避免比例计算的复杂性和不准确性

### **2. 为什么选择四级流量控制？**
- **渐进调节**: 避免突然的速度变化影响系统稳定性
- **目标导向**: 始终朝着200个目标积压调节
- **成本效率**: 在不同负载下都能保持最优效率

### **3. 为什么选择手动API Key管理？**
- **可靠性**: 避免自动化部署失败导致服务中断
- **精确控制**: 可以在合适的时机手动切换
- **简化架构**: 减少复杂的自动化逻辑和潜在故障点

## 🎉 项目成果总结

本次开发成功实现了：
1. **概念澄清**: 明确了积压任务的定义和计算方式
2. **架构优化**: 实现了真正的异步解耦架构
3. **流量控制**: 基于积压任务绝对数量的智能四级流量控制
4. **日志系统**: 生产级的双重输出日志管理
5. **规范化**: 统一的文件命名和模块导入规范

系统现在能够：
- 🚀 **智能调节**: 根据积压情况自动调节提交速度
- 📊 **实时监控**: 详细的系统状态和性能指标
- 🔒 **成本保护**: 自动检测成本上限并停止处理
- 📝 **完整日志**: 便于调试和监控的详细日志记录
- 🎯 **目标导向**: 始终保持最优的系统负载状态

---

## 🚀 2025-07-17 重大架构重构

### ✅ 单词级循环架构重构完成

#### **架构优化目标**
- **问题**: 之前04协调器批次循环导致流量控制响应延迟79秒
- **解决方案**: 将循环从02脚本移到04协调器，实现单词级精确流量控制
- **效果**: 流量控制响应速度提升50倍 (79秒 → 1.58秒)

#### **重构内容**
- [x] **04协调器改为单词级循环**: 逐个单词处理，每个单词后立即流量控制
- [x] **02脚本简化**: 从批次处理改为单个单词处理
- [x] **call_02_script方法优化**: 参数从多个单词改为单个单词
- [x] **流量控制精确化**: 每个单词处理后立即评估系统健康状态
- [x] **错误处理优化**: 单词级错误隔离，失败不影响其他单词
- [x] **进度显示优化**: 实时显示单词级处理进度

#### **性能测试结果**
```
测试单词: butterfly, elephant, rainbow
平均响应时间: 1.58秒/单词
响应时间稳定性: ±0.21秒
提交成功率: 100%
任务入库成功率: 100%
```

#### **架构决策文档**
- [x] **002-线性提交架构设计决策文档**: 详细记录保持线性架构的技术决策
- [x] **可视化文档更新**: 完整反映单词级循环的架构变化
- [x] **Mermaid图表修复**: 修复语法错误，确保正确渲染

### ✅ batch_size参数灵活配置完成

#### **功能恢复目标**
- **需求**: 支持不同规模的测试和运行场景
- **实现**: 重新添加batch_size参数，支持灵活的单词数量控制

#### **配置选项**
- [x] **测试模式**: `--batch-size 10` (约16秒，快速功能验证)
- [x] **小规模测试**: `--batch-size 100` (约2.6分钟，性能测试)
- [x] **API Key消耗预估**: `--batch-size 1000` (约26分钟，成本预算)
- [x] **持续运行模式**: `--batch-size 100000000` (无限制，生产环境)

#### **实现细节**
- [x] **HealthConfig更新**: 添加batch_size字段，默认100000000
- [x] **循环逻辑优化**: 支持batch_size限制的单词级循环
- [x] **命令行参数**: 重新添加--batch-size参数
- [x] **日志显示**: 显示计划处理数量和数据库总数对比
- [x] **进度计算**: 基于实际处理数量计算进度百分比

### 🎯 架构优势总结

#### **实时流量控制**
- **响应速度**: 从79秒降到1.58秒，提升50倍
- **控制精度**: 每个单词后立即评估和调节
- **系统健康**: 实时感知积压任务变化

#### **灵活配置支持**
- **测试友好**: 支持10-1000个单词的不同测试规模
- **成本可控**: 可预估API Key消耗和处理时间
- **生产就绪**: 支持无限制的持续运行

#### **架构清晰性**
- **职责分离**: 04协调器负责流量控制，02脚本负责单词处理
- **错误隔离**: 单词级错误处理，不影响整体流程
- **维护简单**: 保持线性架构，易于理解和调试

---

## 🐛 2025-07-17 关键问题发现与修复

### ❌ 发现的关键问题：积压任务显示异常

#### **问题现象**
```
第一次测试: 积压从 0 → 136 (异常：新任务积压不应该为0)
第二次测试: 积压始终保持 62 (异常：积压完全不变)
```

#### **问题分析**
- **根本原因**: 04协调器查询条件包含了`'submitted'`状态的任务
- **异常行为**: 选择已提交的任务重复提交，Worker拒绝处理
- **积压不变**: 没有新任务进入系统，积压任务数保持不变

#### **技术细节**
```sql
-- 问题代码 (错误)
WHERE status IN ('pending', 'processing', 'failed', 'submitted')

-- 修复代码 (正确)
WHERE status IN ('pending', 'failed')
```

### ✅ 问题修复与验证

#### **修复内容**
- [x] **查询逻辑修复**: 移除`'submitted'`和`'processing'`状态，只选择真正需要处理的任务
- [x] **输出解析优化**: 使用正则表达式精确解析系统状态信息
- [x] **重复提交避免**: 确保只处理pending和failed状态的任务

#### **修复验证结果**
```
修复前测试 (异常):
📈 进度: 1/10 (10.0%) | 积压: 0 | 模式: turbo    ❌ 积压为0异常
📈 进度: 2/10 (20.0%) | 积压: 62 | 模式: turbo   ❌ 积压不变异常

修复后测试 (正常):
📈 进度: 1/10 (10.0%) | 积压: 289 | 模式: normal  ✅ 积压正常
📈 进度: 10/10 (100.0%) | 积压: 449 | 模式: normal ✅ 积压增长正常
```

#### **系统行为验证**
- [x] **积压任务正常增长**: 从289增长到449，符合新任务提交预期
- [x] **流量控制正确切换**: 积压>200时自动切换到normal模式(2秒间隔)
- [x] **任务选择逻辑**: 只选择pending和failed状态，避免重复提交
- [x] **Worker响应正常**: 新任务正常进入积压队列，系统健康运行

### 🎯 修复效果总结

#### **技术改进**
- ✅ **查询精确性**: 只选择真正需要处理的任务，避免重复提交
- ✅ **状态同步**: 本地数据库查询与云端Worker状态保持一致
- ✅ **监控准确性**: 积压任务数量准确反映系统实际状态
- ✅ **流量控制有效**: 基于真实积压任务数进行智能流量调节

#### **用户体验提升**
- ✅ **进度显示准确**: 实时进度反映真实的任务处理状态
- ✅ **系统状态透明**: 积压任务数量准确显示系统健康度
- ✅ **测试结果可信**: batch_size测试结果真实反映系统性能

---

**Commit 建议**:
```
fix: 修复04协调器任务选择逻辑和积压任务显示异常

- 修复查询条件，移除submitted/processing状态避免重复提交
- 使用正则表达式优化系统状态解析，提高解析准确性
- 验证积压任务正常增长(289→449)和流量控制模式切换
- 确保只处理pending/failed状态任务，与Worker状态保持同步
- 完善batch_size测试流程，验证单词级循环架构正确性
```