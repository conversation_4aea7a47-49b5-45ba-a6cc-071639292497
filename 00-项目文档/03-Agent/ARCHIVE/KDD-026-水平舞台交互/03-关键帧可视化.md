# WordResultView 水平舞台交互重构 - 关键帧可视化

## 概述

本文档使用 Mermaid 图表可视化 WordResultView 水平舞台交互重构的关键帧数据结构生命周期变化和用户交互流程。

## 1. 关键帧数据结构生命周期图

以下图表展示了从输入数据到最终渲染输出的完整数据流转过程：

### 数据结构转换流程

```mermaid
graph TB
    %% 输入数据层
    subgraph "🔄 KF-01: 初始输入数据"
        A1[WordDefinitionResponse<br/>📝 word: string<br/>📊 metadata: WordMetadata<br/>📚 content: WordContent]
        A2[WordContent<br/>🎯 coreDefinition<br/>🎭 contextualExplanation<br/>📖 usageExamples<br/>🎬 usageScenarios<br/>📝 usageNotes<br/>🔗 synonyms]
        A3[UsageExample<br/>🇺🇸 english: string<br/>🇨🇳 translation: string<br/>🎵 audioUrl?: string<br/>🧩 phraseBreakdown?: PhraseBreakdown[]]
    end

    %% 状态管理层
    subgraph "⚡ KF-02: 水平舞台状态管理"
        B1[HorizontalStageState<br/>📍 currentSection: SectionType<br/>🎛️ sectionStates: SectionStates<br/>🎬 globalAnimationState]
        B2[ExampleStageState<br/>📊 currentCategoryIndex<br/>📖 currentExampleIndex<br/>🧩 isInPhraseMode<br/>🎯 currentPhraseIndex<br/>💨 showBreathingHint]
        B3[DeepContextState<br/>👁️ isVisible: boolean<br/>📝 contentType: definition|imagery|etymology]
        B4[ScenarioCarouselState<br/>📊 currentIndex<br/>🎬 transitionAnimation<br/>🔄 isLooping]
    end

    %% 交互处理层
    subgraph "🎭 KF-03: 短语分解交互状态"
        C1[PhraseBreakdownState<br/>⚡ isActive: boolean<br/>📖 currentExample<br/>🧩 phrases: PhraseBreakdown[]<br/>🎯 currentPhraseIndex<br/>✨ highlightedPhrase]
        C2[AudioPlaybackState<br/>▶️ isPlaying: boolean<br/>🎵 currentAudioType<br/>📊 currentAudioIndex]
        C3[HighlightedTextSegment<br/>📝 text: string<br/>✨ isHighlighted: boolean<br/>👻 opacity: number]
    end

    %% 输出渲染层
    subgraph "🎨 输出渲染层"
        D1[DeepContextView<br/>📝 title: string<br/>📚 content: string<br/>🎬 transitionAnimation]
        D2[ExampleDisplayContent<br/>🏷️ categoryTitle<br/>📖 currentExample<br/>🧩 phraseBreakdown?<br/>💨 breathingHintVisible]
        D3[PhraseBreakdownDisplay<br/>✨ highlightedText<br/>🎯 currentPhrase<br/>🇨🇳 translationText<br/>🎵 audioUrl?]
        D4[AudioHapticOutput<br/>🎵 audioPlaybackCommand<br/>📳 hapticFeedbackCommand<br/>👁️ visualFeedback]
    end

    %% 数据流向
    A1 --> A2
    A2 --> A3
    A1 -.->|FC-01: 数据转换| B1
    B1 --> B2
    B1 --> B3
    B1 --> B4
    
    B2 -.->|FC-03: 例句交互| C1
    C1 --> C2
    C1 --> C3
    
    B3 -.->|FC-02: 深思语境| D1
    B2 -.->|FC-03: 例句舞台| D2
    C1 -.->|FC-04: 短语分解| D3
    C2 -.->|FC-07: 音频触觉| D4

    %% 样式定义
    classDef inputData fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000
    classDef stateManagement fill:#f3e5f5,stroke:#4a148c,stroke-width:3px,color:#000
    classDef interaction fill:#fff3e0,stroke:#e65100,stroke-width:3px,color:#000
    classDef output fill:#e8f5e8,stroke:#1b5e20,stroke-width:3px,color:#000

    class A1,A2,A3 inputData
    class B1,B2,B3,B4 stateManagement  
    class C1,C2,C3 interaction
    class D1,D2,D3,D4 output
```

### 图表说明

- **🔄 蓝色区域**: 输入数据层，包含从API获取的原始数据结构
- **⚡ 紫色区域**: 状态管理层，负责水平舞台的各种交互状态
- **🎭 橙色区域**: 交互处理层，专门处理短语分解等复杂交互
- **🎨 绿色区域**: 输出渲染层，最终呈现给用户的视图组件

## 2. 用户交互流程时序图

以下时序图展示了用户在水平舞台中的完整交互体验：

```mermaid
sequenceDiagram
    participant User as 👤 用户
    participant UI as 🎨 界面层
    participant VM as ⚡ 状态管理
    participant Audio as 🎵 音频系统
    participant Haptic as 📳 触觉反馈

    Note over User,Haptic: 🌟 深思语境阶段
    User->>UI: 垂直滚动到深思语境
    UI->>VM: 激活深思语境状态
    VM->>UI: 渲染释义内容
    User->>UI: 水平滑动
    UI->>VM: 切换到想象描述
    VM->>UI: 平滑过渡动画

    Note over User,Haptic: 🎭 例句水平舞台阶段  
    User->>UI: 垂直滚动到例句区域
    UI->>VM: 激活例句舞台状态
    VM->>UI: 显示呼吸动画提示
    UI->>User: 💨 呼吸动画暗示水平探索
    
    User->>UI: 水平左滑进入短语分解
    UI->>VM: 进入短语分解模式
    VM->>Audio: 播放完整例句音频
    VM->>Haptic: 触发进入反馈
    VM->>UI: 高亮第一个短语
    
    loop 短语分解学习循环
        User->>UI: 继续左滑
        UI->>VM: 切换到下一短语
        VM->>Audio: 播放短语音频
        VM->>Haptic: 触发切换反馈
        VM->>UI: 更新高亮显示
    end
    
    User->>UI: 最后一次左滑
    UI->>VM: 完成短语分解
    VM->>Audio: 重播完整例句
    VM->>UI: 恢复完整例句显示
    VM->>Haptic: 触发完成反馈

    Note over User,Haptic: 🎬 场景轮换阶段
    User->>UI: 垂直滚动到场景区域  
    UI->>VM: 激活场景轮换状态
    VM->>UI: 显示第一个场景
    
    User->>UI: 水平滑动切换场景
    UI->>VM: 更新场景索引
    VM->>UI: 轮换动画过渡
    VM->>Haptic: 触发切换反馈

    Note over User,Haptic: 🔄 用法和同义词阶段
    User->>UI: 继续垂直滚动
    UI->>VM: 激活用法轮换状态
    VM->>UI: 水平切换用法内容
    
    User->>UI: 最终滚动到同义词
    UI->>VM: 激活同义词轮换
    VM->>UI: 水平切换同义词
```

### 交互流程说明

1. **🌟 深思语境阶段**: 用户首先体验合并的释义、想象、词源内容
2. **🎭 例句水平舞台阶段**: 核心的短语分解深度交互体验
3. **🎬 场景轮换阶段**: 水平切换不同的使用场景
4. **🔄 用法和同义词阶段**: 继续水平探索用法注释和同义词

## 3. 状态机转换图

```mermaid
stateDiagram-v2
    [*] --> DeepContext: 初始加载

    state DeepContext {
        [*] --> Definition
        Definition --> Imagery: 水平滑动
        Imagery --> Etymology: 水平滑动
        Etymology --> Definition: 循环滑动
    }

    DeepContext --> ExampleStage: 垂直滚动

    state ExampleStage {
        [*] --> ShowingExample
        ShowingExample --> BreathingHint: 显示提示
        BreathingHint --> PhraseMode: 水平左滑

        state PhraseMode {
            [*] --> FirstPhrase
            FirstPhrase --> NextPhrase: 继续左滑
            NextPhrase --> LastPhrase: 继续左滑
            LastPhrase --> CompletedPhrase: 最后左滑
        }

        PhraseMode --> ShowingExample: 完成分解
        ShowingExample --> NextExample: 水平右滑
        NextExample --> ShowingExample: 循环
    }

    ExampleStage --> ScenarioCarousel: 垂直滚动

    state ScenarioCarousel {
        [*] --> FirstScenario
        FirstScenario --> NextScenario: 水平滑动
        NextScenario --> FirstScenario: 循环滑动
    }

    ScenarioCarousel --> UsageNotes: 垂直滚动
    ScenarioCarousel --> Synonyms: 垂直滚动

    UsageNotes --> Synonyms: 垂直滚动
    Synonyms --> [*]: 完成浏览
```

## 4. 组件架构图

```mermaid
graph LR
    subgraph "🎨 WordResultView 主容器"
        A[HorizontalStageContainer]

        subgraph "🌟 深思语境区域"
            B[DeepContextComponent]
            B1[DefinitionView]
            B2[ImageryView]
            B3[EtymologyView]
        end

        subgraph "🎭 例句舞台区域"
            C[ExampleStageComponent]
            C1[ExampleDisplayView]
            C2[PhraseBreakdownView]
            C3[BreathingHintView]
        end

        subgraph "🎬 轮换区域"
            D[ScenarioCarouselComponent]
            E[UsageNotesCarousel]
            F[SynonymsCarousel]
        end
    end

    subgraph "⚡ 状态管理"
        G[HorizontalStageViewModel]
        G1[DeepContextState]
        G2[ExampleStageState]
        G3[CarouselStates]
    end

    subgraph "🎵 音频触觉系统"
        H[AudioManager]
        I[HapticManager]
        J[AudioHapticCoordinator]
    end

    A --> B
    A --> C
    A --> D
    A --> E
    A --> F

    B --> B1
    B --> B2
    B --> B3

    C --> C1
    C --> C2
    C --> C3

    A --> G
    G --> G1
    G --> G2
    G --> G3

    C --> H
    C --> I
    H --> J
    I --> J
```

## 5. 数据流向详细图

```mermaid
flowchart TD
    subgraph "📥 输入层"
        A1[WordDefinitionResponse]
        A2[用户手势输入]
        A3[音频状态事件]
    end

    subgraph "🔄 转换层"
        B1[FC-01: 数据结构转换]
        B2[FC-03: 手势处理]
        B3[FC-07: 音频触觉协调]
    end

    subgraph "⚡ 状态层"
        C1[HorizontalStageState]
        C2[ExampleStageState]
        C3[PhraseBreakdownState]
        C4[AudioPlaybackState]
    end

    subgraph "🎨 渲染层"
        D1[DeepContextView]
        D2[ExampleDisplayContent]
        D3[PhraseBreakdownDisplay]
        D4[CarouselViews]
    end

    subgraph "📱 输出层"
        E1[视觉反馈]
        E2[音频播放]
        E3[触觉反馈]
        E4[动画效果]
    end

    A1 --> B1
    A2 --> B2
    A3 --> B3

    B1 --> C1
    B2 --> C2
    B2 --> C3
    B3 --> C4

    C1 --> D1
    C2 --> D2
    C3 --> D3
    C1 --> D4

    D1 --> E1
    D2 --> E1
    D3 --> E1
    D4 --> E1

    C4 --> E2
    C2 --> E3
    C3 --> E3

    D1 --> E4
    D2 --> E4
    D3 --> E4
    D4 --> E4
```

## 总结

这些可视化图表展示了 WordResultView 水平舞台交互重构的完整技术架构：

1. **数据结构生命周期**: 从输入到输出的完整数据转换流程
2. **用户交互流程**: 用户在不同阶段的交互体验路径
3. **状态机转换**: 各种交互状态之间的转换逻辑
4. **组件架构**: 各个UI组件和状态管理的组织结构
5. **数据流向**: 详细的数据处理和传递路径

这些图表为实现阶段提供了清晰的技术指导和架构参考。
