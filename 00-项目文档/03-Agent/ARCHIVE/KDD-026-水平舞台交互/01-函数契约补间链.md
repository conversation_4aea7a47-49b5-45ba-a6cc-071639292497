# WordResultView 水平舞台交互重构 - 函数契约补间链

## 项目概述

### 核心目标
将 WordResultView 从传统的垂直滚动模式重构为创新的"水平舞台"深度交互模式，实现多维度的单词学习体验。

### 关键创新点
1. **深思语境组件**：合并释义、想象、词源为统一体验
2. **例句水平舞台**：支持短语分解的深度交互
3. **内容轮换机制**：场景、用法、同义词的水平切换
4. **呼吸动画提示**：引导用户发现水平探索功能
5. **触觉音频反馈**：增强交互沉浸感

## 技术架构

### 数据流向
```
WordDefinitionResponse → HorizontalStageViewModel → 各组件状态管理 → UI渲染
```

### 核心组件
- `HorizontalStageContainer`: 水平舞台容器
- `DeepContextComponent`: 深思语境组件  
- `ExampleStageComponent`: 例句舞台组件
- `ScenarioCarouselComponent`: 场景轮换组件
- `PhraseBreakdownView`: 短语分解视图

## 关键帧数据结构定义

### 关键帧 KF-01: 初始状态
```typescript
// 输入数据结构 (>>>>>)
interface WordDefinitionResponse {
  word: string;
  metadata: WordMetadata;
  content: WordContent;
}

interface WordContent {
  difficulty: string;
  phoneticSymbols: PhoneticSymbol[];
  coreDefinition: string;
  contextualExplanation: ContextualExplanation;
  usageExamples: UsageExampleCategory[];
  usageScenarios: UsageScenario[];
  usageNotes: UsageNote[];
  synonyms: Synonym[];
}
```

### 关键帧 KF-02: 水平舞台状态管理
```typescript
// 水平舞台状态 (<<<<<)
interface HorizontalStageState {
  currentSection: SectionType;
  sectionStates: {
    deepContext: DeepContextState;
    examples: ExampleStageState;
    scenarios: ScenarioCarouselState;
    usageNotes: UsageNotesState;
    synonyms: SynonymsState;
  };
  globalAnimationState: AnimationState;
}

enum SectionType {
  DEEP_CONTEXT = "deep_context",
  EXAMPLES = "examples", 
  SCENARIOS = "scenarios",
  USAGE_NOTES = "usage_notes",
  SYNONYMS = "synonyms"
}

interface DeepContextState {
  isVisible: boolean;
  contentType: 'definition' | 'imagery' | 'etymology';
}

interface ExampleStageState {
  currentCategoryIndex: number;
  currentExampleIndex: number;
  isInPhraseMode: boolean;
  currentPhraseIndex: number;
  showBreathingHint: boolean;
}
```

### 关键帧 KF-03: 短语分解交互状态
```typescript
// 短语分解状态 (<<<<<)
interface PhraseBreakdownState {
  isActive: boolean;
  currentExample: UsageExample;
  phrases: PhraseBreakdown[];
  currentPhraseIndex: number;
  highlightedPhrase: string;
  audioPlaybackState: AudioPlaybackState;
}

interface AudioPlaybackState {
  isPlaying: boolean;
  currentAudioType: 'full_sentence' | 'phrase_breakdown';
  currentAudioIndex: number;
}
```

## 函数契约补间链

### [FC-01] 数据结构转换契约
**目标**: 将 WordDefinitionResponse 转换为 HorizontalStageState

**输入** (>>>>>):
```typescript
WordDefinitionResponse: {
  word: string;
  content: WordContent;
}
```

**输出** (<<<<<):
```typescript
HorizontalStageState: {
  currentSection: SectionType.DEEP_CONTEXT;
  sectionStates: {
    deepContext: { isVisible: true, contentType: 'definition' };
    examples: { currentCategoryIndex: 0, isInPhraseMode: false };
    // ... 其他状态初始化
  };
}
```

**实现要点**:
- 解析 WordContent 各字段
- 初始化所有区块状态
- 设置默认显示为深思语境

### [FC-02] 深思语境组件渲染契约
**目标**: 渲染合并的释义、想象、词源内容

**输入** (>>>>>):
```typescript
DeepContextData: {
  coreDefinition: string;
  vividImagery: string;
  etymologicalEssence: string;
  currentContentType: 'definition' | 'imagery' | 'etymology';
}
```

**输出** (<<<<<):
```typescript
DeepContextView: {
  title: string;
  content: string;
  transitionAnimation: TransitionConfig;
}
```

### [FC-03] 例句水平舞台交互契约
**目标**: 实现例句的水平滑动和短语分解功能

**输入** (>>>>>):
```typescript
ExampleStageInput: {
  usageExamples: UsageExampleCategory[];
  currentState: ExampleStageState;
  gestureInput: HorizontalSwipeGesture;
}
```

**输出** (<<<<<):
```typescript
ExampleStageOutput: {
  newState: ExampleStageState;
  displayContent: ExampleDisplayContent;
  audioTrigger?: AudioTriggerEvent;
  hapticFeedback?: HapticFeedbackEvent;
}

interface ExampleDisplayContent {
  categoryTitle: string;
  currentExample: UsageExample;
  phraseBreakdown?: PhraseBreakdownDisplay;
  breathingHintVisible: boolean;
}
```

### [FC-04] 短语分解模式契约
**目标**: 处理例句的短语分解深度交互

**输入** (>>>>>):
```typescript
PhraseBreakdownInput: {
  example: UsageExample;
  phraseBreakdowns: PhraseBreakdown[];
  currentPhraseIndex: number;
  swipeDirection: 'left' | 'right';
}
```

**输出** (<<<<<):
```typescript
PhraseBreakdownOutput: {
  highlightedText: HighlightedTextSegment[];
  currentPhrase: PhraseBreakdown;
  translationText: string;
  audioUrl?: string;
  isComplete: boolean;
}

interface HighlightedTextSegment {
  text: string;
  isHighlighted: boolean;
  opacity: number;
}
```

### [FC-05] 场景轮换交互契约
**目标**: 实现使用场景的水平切换

**输入** (>>>>>):
```typescript
ScenarioCarouselInput: {
  scenarios: UsageScenario[];
  currentIndex: number;
  swipeGesture: HorizontalSwipeGesture;
}
```

**输出** (<<<<<):
```typescript
ScenarioCarouselOutput: {
  newIndex: number;
  currentScenario: UsageScenario;
  transitionAnimation: CarouselTransition;
}
```

### [FC-06] 呼吸动画提示契约
**目标**: 生成引导用户水平探索的视觉提示

**输入** (>>>>>):
```typescript
BreathingHintInput: {
  shouldShow: boolean;
  animationPhase: 'inhale' | 'exhale' | 'pause';
  intensity: number; // 0.0 - 1.0
}
```

**输出** (<<<<<):
```typescript
BreathingHintOutput: {
  opacity: number;
  scale: number;
  glowRadius: number;
  animationDuration: number;
}
```

### [FC-07] 音频触觉反馈集成契约
**目标**: 在水平交互中集成音频播放和触觉反馈

**输入** (>>>>>):
```typescript
AudioHapticInput: {
  triggerEvent: InteractionEvent;
  audioUrl?: string;
  hapticType: HapticFeedbackType;
  audioType: 'word' | 'phrase' | 'sentence';
}
```

**输出** (<<<<<):
```typescript
AudioHapticOutput: {
  audioPlaybackCommand: AudioCommand;
  hapticFeedbackCommand: HapticCommand;
  visualFeedback: VisualFeedbackState;
}
```

## 实现计划

### Commit 规划

#### Commit 1: feat: 实现水平舞台核心数据结构和状态管理
- 创建 HorizontalStageViewModel
- 定义所有状态管理接口
- 实现 [FC-01] 数据转换逻辑

#### Commit 2: feat: 实现深思语境组件
- 创建 DeepContextComponent
- 实现 [FC-02] 内容渲染逻辑
- 集成到主视图

#### Commit 3: feat: 实现例句水平舞台核心交互
- 创建 ExampleStageComponent
- 实现 [FC-03] 水平滑动逻辑
- 添加呼吸动画提示

#### Commit 4: feat: 实现短语分解深度交互模式
- 创建 PhraseBreakdownView
- 实现 [FC-04] 短语高亮和切换
- 集成音频播放

#### Commit 5: feat: 实现场景和用法的水平轮换
- 实现 [FC-05] 场景轮换逻辑
- 扩展到用法注释和同义词
- 统一交互模式

#### Commit 6: feat: 集成音频触觉反馈系统
- 实现 [FC-06] 呼吸动画
- 实现 [FC-07] 音频触觉集成
- 优化用户体验

#### Commit 7: test: 完善水平舞台交互测试
- 添加单元测试
- 性能优化
- 用户体验调优

## Context Files

### 核心文件
- `iOS/SensewordApp/Views/WordResult/WordResultView.swift` - 当前实现
- `iOS/SensewordApp/Models/API/WordAPIModels.swift` - 数据模型
- `iOS/SensewordApp/Services/Audio/AutoAudioManager.swift` - 音频管理

### 相关组件
- `iOS/SensewordApp/Views/Main/MainContentView.swift` - 主视图集成
- `iOS/SensewordApp/Services/Adapters/AudioAPIAdapter.swift` - 音频适配器

## 技术要求

### 性能要求
- 水平滑动响应时间 < 16ms
- 音频播放延迟 < 100ms
- 动画帧率保持 60fps

### 兼容性要求
- 支持 iOS 15.0+
- 保持与现有音频系统兼容
- 向后兼容当前数据结构

### 用户体验要求
- 直观的手势交互
- 流畅的动画过渡
- 清晰的视觉反馈
- 准确的触觉反馈
