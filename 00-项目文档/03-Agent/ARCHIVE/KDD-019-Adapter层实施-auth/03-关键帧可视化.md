# KDD-019 认证模块Adapter层 - 关键帧可视化

> **数据结构生命周期变化图表**
> 基于函数契约补间链的完整数据流可视化
> 生成时间: 2025-06-26

---

## 1. 认证模块整体架构图

```mermaid
graph TB
    subgraph "iOS App Layer"
        UI[用户界面层]
        BL[业务逻辑层]
        AL[Adapter转译层]
    end

    subgraph "Network Layer"
        AC[APIClient]
        AE[APIError]
        ACF[APIConfig]
    end

    subgraph "Backend Services"
        AS[auth.senseword.app]
    end

    UI --> BL
    BL --> AL
    AL --> AC
    AC --> AS

    AC -.-> AE
    AC -.-> ACF

    style AL fill:#e1f5fe
    style AC fill:#f3e5f5
    style AS fill:#e8f5e8
```

## 2. 登录认证数据流 (FC-01)

```mermaid
sequenceDiagram
    participant UI as 用户界面
    participant BL as 业务逻辑
    participant AA as AuthAPIAdapter
    participant AC as APIClient
    participant BE as 后端服务

    Note over UI,BE: Apple ID 登录流程

    UI->>BL: Apple ID Token
    activate BL

    BL->>AA: login(idToken, provider)
    activate AA

    Note over AA: 关键帧A: 原始认证凭证
    Note right of AA: idToken: String<br/>provider: .apple

    AA->>AA: 构建LoginRequestBody
    Note over AA: 关键帧B: API请求体
    Note right of AA: {<br/>  "idToken": "eyJ...",<br/>  "provider": "apple"<br/>}

    AA->>AC: POST /api/v1/auth/login
    activate AC
    AC->>BE: HTTP Request
    activate BE

    BE-->>AC: HTTP 200 Response
    deactivate BE
    AC-->>AA: JSON Response
    deactivate AC

    Note over AA: 关键帧C: 后端响应
    Note right of AA: {<br/>  "success": true,<br/>  "session": {<br/>    "sessionId": "sess_...",<br/>    "user": {...}<br/>  }<br/>}

    AA->>AA: 解析SessionLoginSuccessResponse
    Note over AA: 关键帧D: 结构化响应
    Note right of AA: SessionLoginSuccessResponse<br/>├─ success: Bool<br/>└─ session: SessionInfo<br/>   ├─ sessionId: String<br/>   └─ user: UserInfo

    AA-->>BL: SessionLoginSuccessResponse
    deactivate AA
    BL-->>UI: 登录成功
    deactivate BL
```

## 3. 用户登出数据流 (FC-02)

```mermaid
sequenceDiagram
    participant BL as 业务逻辑
    participant AA as AuthAPIAdapter
    participant AC as APIClient
    participant BE as 后端服务

    Note over BL,BE: 用户登出流程

    BL->>AA: logout(sessionId, reason)
    activate AA

    Note over AA: 关键帧A: 登出请求参数
    Note right of AA: sessionId: String<br/>reason: LogoutReason?

    AA->>AA: 构建LogoutRequest
    Note over AA: 关键帧B: API请求体
    Note right of AA: {<br/>  "reason": "user_initiated"<br/>}

    AA->>AC: POST /api/v1/auth/logout<br/>(双重认证)
    activate AC
    AC->>BE: HTTP Request
    activate BE

    BE-->>AC: HTTP 200 Response
    deactivate BE
    AC-->>AA: JSON Response
    deactivate AC

    Note over AA: 关键帧C: 登出响应
    Note right of AA: {<br/>  "success": true,<br/>  "message": "已成功登出",<br/>  "sessionRevoked": true,<br/>  "timestamp": "2025-06-26T..."<br/>}

    AA-->>BL: LogoutSuccessResponse
    deactivate AA
```

## 4. 数据结构关键帧变化图

```mermaid
graph LR
    subgraph "FC-01: 登录认证"
        A1[Apple ID Token<br/>String] --> B1[LoginRequestBody<br/>Codable]
        B1 --> C1[HTTP JSON<br/>Data]
        C1 --> D1[Backend Response<br/>JSON]
        D1 --> E1[SessionLoginSuccessResponse<br/>Codable]
    end

    subgraph "FC-02: 用户登出"
        A2[Session ID<br/>String] --> B2[LogoutRequest<br/>Codable]
        B2 --> C2[HTTP JSON<br/>Data]
        C2 --> D2[Backend Response<br/>JSON]
        D2 --> E2[LogoutSuccessResponse<br/>Codable]
    end

    subgraph "FC-03: 全设备登出"
        A3[Session ID<br/>String] --> B3[LogoutAllRequest<br/>Codable]
        B3 --> C3[HTTP JSON<br/>Data]
        C3 --> D3[Backend Response<br/>JSON]
        D3 --> E3[LogoutAllSuccessResponse<br/>Codable]
    end

    subgraph "FC-04: 健康检查"
        A4[无输入参数] --> B4[GET Request<br/>Headers Only]
        B4 --> C4[Backend Response<br/>JSON]
        C4 --> D4[HealthCheckResponse<br/>Codable]
    end

    style A1 fill:#ffebee
    style A2 fill:#ffebee
    style A3 fill:#ffebee
    style A4 fill:#ffebee
    style E1 fill:#e8f5e8
    style E2 fill:#e8f5e8
    style E3 fill:#e8f5e8
    style D4 fill:#e8f5e8
```

## 5. 错误处理分支图

```mermaid
graph TD
    Start[API请求开始] --> Auth{认证检查}

    Auth -->|静态密钥无效| E1[APIError.invalidAPIKey]
    Auth -->|Session无效| E2[APIError.unauthorized]
    Auth -->|认证通过| Network[网络请求]

    Network -->|网络错误| E3[APIError.networkError]
    Network -->|请求成功| Response{响应处理}

    Response -->|JSON解析失败| E4[APIError.decodingError]
    Response -->|HTTP错误状态| E5[APIError.serverError]
    Response -->|解析成功| Success[返回结构化数据]

    style E1 fill:#ffcdd2
    style E2 fill:#ffcdd2
    style E3 fill:#ffcdd2
    style E4 fill:#ffcdd2
    style E5 fill:#ffcdd2
    style Success fill:#c8e6c9
```

## 6. 认证状态转换图

```mermaid
stateDiagram-v2
    [*] --> Unauthenticated: 应用启动

    Unauthenticated --> Authenticating: 调用login()
    Authenticating --> Authenticated: 登录成功
    Authenticating --> Unauthenticated: 登录失败

    Authenticated --> LoggingOut: 调用logout()
    LoggingOut --> Unauthenticated: 登出成功
    LoggingOut --> Authenticated: 登出失败

    Authenticated --> LoggingOutAll: 调用logoutAll()
    LoggingOutAll --> Unauthenticated: 全设备登出成功
    LoggingOutAll --> Authenticated: 全设备登出失败

    Authenticated --> Deleting: 调用deleteAccount()
    Deleting --> [*]: 账号删除成功
    Deleting --> Authenticated: 账号删除失败

    note right of Authenticated
        Session ID 有效
        可调用需要认证的API
    end note

    note right of Unauthenticated
        无有效Session
        仅可调用公开API
    end note
```

## 7. 双重认证机制图

```mermaid
graph TB
    subgraph "认证层级"
        L1[Level 1: 静态API密钥]
        L2[Level 2: Session认证]
    end

    subgraph "API端点分类"
        P1[公开端点<br/>login, healthCheck]
        P2[认证端点<br/>logout, getUserInfo, deleteAccount]
    end

    subgraph "头部构成"
        H1[X-Static-API-Key: sk-senseword-...]
        H2[Authorization: Bearer sess_...]
    end

    L1 --> P1
    L1 --> P2
    L2 --> P2

    P1 --> H1
    P2 --> H1
    P2 --> H2

    style L1 fill:#fff3e0
    style L2 fill:#e8f5e8
    style P1 fill:#e3f2fd
    style P2 fill:#fce4ec
```

## 8. 认证模块数据模型关系图

```mermaid
classDiagram
    class AuthProvider {
        <<enumeration>>
        +apple: String
    }

    class LoginRequestBody {
        +idToken: String
        +provider: AuthProvider
    }

    class LogoutRequest {
        +reason: LogoutReason?
    }

    class LogoutAllRequest {
        +reason: LogoutAllReason?
    }

    class LogoutReason {
        <<enumeration>>
        +userInitiated: String
        +security: String
        +admin: String
    }

    class LogoutAllReason {
        <<enumeration>>
        +securityConcern: String
        +deviceLost: String
        +userRequest: String
    }

    class SessionInfo {
        +sessionId: String
        +user: UserInfo
    }

    class UserInfo {
        +id: String
        +email: String
        +displayName: String
        +isPro: Bool
    }

    class SessionLoginSuccessResponse {
        +success: Bool
        +session: SessionInfo
    }

    class LogoutSuccessResponse {
        +success: Bool
        +message: String
        +sessionRevoked: Bool
        +timestamp: String
    }

    class LogoutAllSuccessResponse {
        +success: Bool
        +message: String
        +sessionsRevoked: Int
        +timestamp: String
    }

    class HealthCheckResponse {
        +status: String
        +service: String
        +timestamp: String
        +environment: String
        +version: String
    }

    LoginRequestBody --> AuthProvider
    LogoutRequest --> LogoutReason
    LogoutAllRequest --> LogoutAllReason
    SessionLoginSuccessResponse --> SessionInfo
    SessionInfo --> UserInfo
```