# 📋 KDD-012: Session认证系统

## 🎯 项目概述

Session认证系统是 SenseWord 应用的核心认证服务，采用永久Session机制替代传统JWT认证，实现"登录一次，永远有效"的用户体验。基于 Cloudflare Workers + D1 数据库构建，支持 Apple ID 登录和多设备同步。

### 🏗️ 架构特点
- **微服务架构**: 独立的认证服务，支持多个业务模块调用
- **永久Session**: 无过期时间，用户无需重复登录
- **多设备支持**: 同一用户可在多设备同时使用
- **安全机制**: 支持即时撤销、软删除、设备信息记录

### 🛠️ 技术栈
- **后端**: Cloudflare Workers + Hono框架
- **数据库**: Cloudflare D1 (SQLite)
- **认证**: Apple Sign In + Session管理
- **客户端**: iOS Swift + Keychain安全存储

## 🔑 核心能力、接口与数据契约

### 后端核心能力 (Backend Core Capabilities)
- **Apple ID 认证验证**: 验证Apple ID Token，获取用户身份信息
- **用户管理**: 自动创建新用户或查找现有用户
- **Session生成**: 为已验证用户创建永久有效的Session ID
- **Session验证**: 验证Session ID有效性，获取用户信息
- **Session撤销**: 支持单个或批量撤销Session
- **设备管理**: 记录和管理用户设备信息
- **安全审计**: 完整的认证日志和错误处理

### 前端接口事务 (Frontend Interface Transactions)
- **Apple登录流程**: 获取Apple凭据 → 发送到后端 → 获取Session → 存储到Keychain
- **Session认证**: 在API请求中携带Session ID进行身份验证
- **用户信息获取**: 通过Session获取当前用户详细信息
- **登出操作**: 清除本地Session数据（可选择撤销服务器Session）
- **多设备同步**: 同一用户在多设备间共享认证状态

### 核心数据结构 (DTO) 定义

```typescript
// 登录请求：Apple ID Token认证
interface LoginRequestBody {
  idToken: string;          // Apple ID Token
  provider: 'apple';        // 认证提供方
}

// 登录成功响应：返回Session信息
interface SessionLoginSuccessResponse {
  success: true;
  session: {
    sessionId: string;      // 永久有效的Session ID (格式: sess_xxxxxx)
    user: {
      id: string;          // 用户唯一标识
      email: string;       // 用户邮箱
      displayName: string; // 显示名称
      isPro: boolean;      // Pro订阅状态
    };
  };
}

// Session记录结构：数据库存储格式
interface SessionRecord {
  session_id: string;                 // Session唯一标识符
  user_id: string;                    // 关联的用户ID
  created_at: string;                 // ISO 8601格式的创建时间
  last_active_at: string;             // ISO 8601格式的最后活跃时间
  is_active: boolean;                 // Session是否有效
  device_info?: string;               // 设备信息 (可选)
}

// 用户信息响应：获取当前用户
interface UserProfileResponse {
  success: true;
  user: {
    id: string;
    email: string;
    displayName: string;
    isPro: boolean;
    createdAt: string;
  };
}

// 错误响应：统一错误格式
interface LoginErrorResponse {
  success: false;
  error: "INVALID_APPLE_TOKEN" | "USER_CREATION_FAILED" | "SESSION_CREATION_FAILED" | "UNAUTHORIZED";
  message: string;
  timestamp?: string;
}
```

## 🌐 服务地址

### 生产环境
- **Auth Worker**: `https://senseword-auth-worker-development.zhouqi-aaha.workers.dev`
- **域名**: `auth.senseword.app` (配置中)

### 本地开发环境
- **本地服务**: `http://localhost:8787`
- **开发命令**: `npm run dev` (在 `cloudflare/workers/auth` 目录)

## 📡 API端点列表

### 🔐 认证相关端点

#### 1. Apple登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "idToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "provider": "apple"
}
```

**成功响应 (200)**:
```json
{
  "success": true,
  "session": {
    "sessionId": "sess_1a2b3c4d5e6f7g8h9i0j",
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "displayName": "John Doe",
      "isPro": false
    }
  }
}
```

#### 2. 获取当前用户信息
```http
GET /api/v1/users/me
Authorization: Bearer sess_1a2b3c4d5e6f7g8h9i0j
```

**成功响应 (200)**:
```json
{
  "success": true,
  "user": {
    "id": "user_123",
    "email": "<EMAIL>",
    "displayName": "John Doe",
    "isPro": false,
    "createdAt": "2025-06-24T10:30:00.000Z"
  }
}
```

#### 3. 健康检查
```http
GET /api/v1/auth/health
```

#### 4. 开发环境模拟登录
```http
GET /api/v1/auth/mock-login?userId=dev-user-001&isPro=false
```

## 🧪 预设测试数据

### 开发环境测试账号
- **普通用户**: `userId=dev-user-001&isPro=false`
- **Pro用户**: `userId=pro-user-001&isPro=true`

### 测试Session ID
- **普通用户Session**: `sess_lyesmokr5hd8hbo68wky`
- **Pro用户Session**: `sess_92ctf7czpei1t5xjn8uo`

## 🔧 测试方法

### 1. 简单测试 - 健康检查
```bash
curl https://senseword-auth-worker-development.zhouqi-aaha.workers.dev/api/v1/auth/health
```

### 2. 中等测试 - 模拟登录
```bash
curl "https://senseword-auth-worker-development.zhouqi-aaha.workers.dev/api/v1/auth/mock-login?userId=test-001&isPro=false"
```

### 3. 完整测试 - Session认证流程
```bash
# 1. 获取Session
SESSION_RESPONSE=$(curl -s "https://senseword-auth-worker-development.zhouqi-aaha.workers.dev/api/v1/auth/mock-login?userId=test-001&isPro=false")
SESSION_ID=$(echo $SESSION_RESPONSE | jq -r '.session.sessionId')

# 2. 使用Session获取用户信息
curl -H "Authorization: Bearer $SESSION_ID" \
     https://senseword-auth-worker-development.zhouqi-aaha.workers.dev/api/v1/users/me
```

## 💻 本地开发环境

### 环境要求
- Node.js 18+
- npm 或 yarn
- Cloudflare账号和API Token

### 设置步骤

1. **安装依赖**
```bash
cd cloudflare/workers/auth
npm install
```

2. **配置环境变量**
```bash
# 复制配置文件
cp wrangler.toml.example wrangler.toml

# 配置必要的环境变量
# - DB: D1数据库绑定
# - NODE_ENV: development
```

3. **数据库迁移**
```bash
# 创建本地数据库
npx wrangler d1 create senseword-auth-db-dev

# 执行迁移
npx wrangler d1 migrations apply senseword-auth-db-dev --local
```

4. **启动开发服务器**
```bash
npm run dev
# 服务将在 http://localhost:8787 启动
```

## 🔐 关键概念说明

### Session ID 格式
- **格式**: `sess_` + 20位随机字符 (a-z, 0-9)
- **示例**: `sess_1a2b3c4d5e6f7g8h9i0j`
- **特点**: 永久有效，无过期时间

### LPLC原则 (Login-Persist-Last-Consistent)
- **Login**: 用户只需登录一次
- **Persist**: Session永久保存，无需刷新
- **Last**: 记录最后活跃时间
- **Consistent**: 跨设备状态一致

### 多设备支持策略
- 同一用户可创建多个Session
- 每个设备独立的Session ID
- 支持批量撤销所有设备Session

## 🛡️ 安全特性

### Session安全
- **随机生成**: 使用加密安全的随机数生成器
- **格式验证**: 严格的Session ID格式检查
- **即时撤销**: 支持立即撤销可疑Session
- **设备追踪**: 记录设备信息用于安全审计

### 传输安全
- **HTTPS强制**: 所有API调用必须使用HTTPS
- **CORS配置**: 严格的跨域访问控制
- **安全头**: 完整的HTTP安全响应头

### 存储安全
- **Keychain存储**: iOS客户端使用Keychain安全存储
- **数据库加密**: D1数据库自动加密
- **软删除**: Session撤销使用软删除，保留审计记录

## ❌ 错误处理

### 常见错误码

| 错误码 | HTTP状态 | 描述 | 解决方案 |
|--------|----------|------|----------|
| `INVALID_APPLE_TOKEN` | 401 | Apple ID Token无效 | 重新获取Apple凭据 |
| `SESSION_CREATION_FAILED` | 500 | Session创建失败 | 检查数据库连接 |
| `UNAUTHORIZED` | 401 | Session无效或过期 | 重新登录获取新Session |
| `USER_CREATION_FAILED` | 500 | 用户创建失败 | 检查用户数据格式 |

### 错误响应格式
```json
{
  "success": false,
  "error": "UNAUTHORIZED",
  "message": "认证失败或Session无效",
  "timestamp": "2025-06-24T16:53:50.760Z"
}
```

## 🔗 集成指南

### iOS客户端集成

1. **添加SessionManager**
```swift
import SharedModels

// 存储登录Session
try SessionManager.shared.storeSession(
    sessionId: response.session.sessionId,
    userInfo: response.session.user
)

// 检查认证状态
if SessionManager.shared.isAuthenticated() {
    // 用户已登录
}

// 获取Session ID用于API调用
let sessionId = try SessionManager.shared.getSessionId()
```

2. **API请求认证**
```swift
var request = URLRequest(url: apiURL)
request.setValue("Bearer \(sessionId)", forHTTPHeaderField: "Authorization")
```

### 其他服务集成

参考 API Worker 的 Session认证中间件实现：
```typescript
import { authenticateSessionRequestWithDevMode } from './middleware/session-auth.middleware';

// 在需要认证的端点中使用
const user = await authenticateSessionRequestWithDevMode(request, env);
```

## 📈 后续开发

### ✅ 已完成功能
- [x] Apple ID 登录认证
- [x] Session生成和验证
- [x] 用户信息管理
- [x] iOS SessionManager
- [x] 多设备支持
- [x] 安全撤销机制
- [x] 开发环境模拟登录
- [x] 完整的错误处理
- [x] API Worker集成

### 🚀 待实现功能
- [ ] Google登录支持
- [ ] Session自动清理机制
- [ ] 用户行为分析
- [ ] 高级安全策略 (IP限制、设备指纹)
- [ ] Session分片存储 (支持更大规模)

## 🆘 技术支持

### 问题排查

1. **Session验证失败**
   - 检查Session ID格式是否正确
   - 确认Session未被撤销
   - 验证Authorization头格式

2. **数据库连接问题**
   - 检查D1数据库绑定配置
   - 确认迁移已正确执行
   - 验证环境变量设置

3. **Apple登录失败**
   - 验证Apple ID Token有效性
   - 检查网络连接
   - 确认Apple服务状态

### 技术细节参考
- **函数契约**: `01-函数契约补间链.md`
- **测试报告**: `02-补间测试报告.md`
- **架构图**: `03-关键帧可视化.md`
- **开发日志**: `04-进度日志.md`

---

**文档版本**: v1.0  
**最后更新**: 2025年6月24日  
**维护团队**: SenseWord开发团队
