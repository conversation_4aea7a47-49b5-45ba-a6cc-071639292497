# 需求："Session认证系统" 的函数契约补间链 (V1.0 - 奥卡姆剃刀版)

## 0. 依赖关系与影响分析

- [重用] `cloudflare/workers/auth/src/auth/apple-auth.service.ts`: Apple ID Token验证逻辑完全保留
- [重用] `cloudflare/workers/auth/src/auth/auth.service.ts`: 用户查找和创建逻辑保留
- [重用] `cloudflare/workers/auth/migrations/0001_create_users_table.sql`: 现有用户表结构保持不变
- [删除] `cloudflare/workers/auth/src/auth/jwt.service.ts`: JWT生成逻辑将被Session生成逻辑完全替代
- [修改] `cloudflare/workers/auth/src/controllers/auth.controller.ts`: 登录响应改为返回sessionId而非JWT
- [修改] `cloudflare/workers/auth/src/middleware/auth.middleware.ts`: 认证中间件改为Session验证
- [新增] `cloudflare/workers/auth/migrations/0002_create_sessions_table.sql`: 新增Session表
- [修改] `iOS/Packages/SharedModels/Sources/SharedModels/TokenManager.swift`: 改为SessionManager，存储sessionId

## 1. 项目文件结构概览 (Project File Structure Overview)

```
cloudflare/workers/auth/
├── migrations/
│   ├── 0001_create_users_table.sql           # [已实现] 用户表
│   └── 0002_create_sessions_table.sql        # [新增] Session表
├── src/
│   ├── auth/
│   │   ├── apple-auth.service.ts             # [已实现] Apple认证验证
│   │   ├── auth.service.ts                   # [已实现] 用户查找创建
│   │   ├── jwt.service.ts                    # [删除] 改为session.service.ts
│   │   └── session.service.ts                # [新增] Session管理服务
│   ├── controllers/
│   │   └── auth.controller.ts                # [修改] 返回sessionId
│   ├── middleware/
│   │   └── auth.middleware.ts                # [修改] Session验证
│   └── types/
│       └── auth-types.ts                     # [修改] 添加Session类型

iOS/Packages/SharedModels/Sources/SharedModels/
├── TokenManager.swift                        # [删除] 改为SessionManager.swift
└── SessionManager.swift                      # [新增] Session管理
```

## 2. 分支策略建议

- 建议的特性分支名称: `feature/auth/session-system`
- 建议的 git worktree 文件路径：/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/03-session-auth-system
- 基础分支: `dev`
- 分支创建模拟命令行:
    ```bash
    # 概念性命令，用于记录和指导
    # git checkout dev
    # git pull origin dev
    # git worktree add /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/03-session-auth-system -b feature/auth/session-system dev
    ```

## 3. Commit 规划概要 (Commit Plan Summary & Status)

- [ ] feat(db): 创建sessions表结构
- [ ] feat(auth-service): 实现Session生成和验证服务
- [ ] feat(auth-controller): 修改登录端点返回sessionId
- [ ] feat(auth-middleware): 实现Session验证中间件
- [ ] feat(ios-session): 创建SessionManager替代TokenManager

## 4. 函数契约补间链 (Function Contract Tweening Chain)

### [FC-01]: 客户端Apple登录触发器

- 职责: 响应用户Apple登录，获取Apple ID Token并发送到后端
- 函数签名: `signInWithApple() async throws -> UserSession`
- 所在文件: `iOS/Packages/AuthDomain/Sources/AuthDomain/AuthService.swift`

>>>>> 输入 (Input): 无直接参数

用户点击Apple登录按钮触发：

```swift
// Apple Sign In 返回的凭据
struct AppleCredential {
    let identityToken: Data    // Apple ID Token
    let user: String          // 用户标识符
    let email: String?        // 用户邮箱（可选）
    let fullName: PersonNameComponents? // 用户姓名（可选）
}
```

<<<<< 输出 (Output): UserSession

成功登录后的用户会话：

```swift
struct UserSession {
    let sessionId: String     // 服务器生成的Session ID
    let user: UserProfile     // 用户基本信息
}

struct UserProfile {
    let id: String           // 用户ID
    let email: String        // 用户邮箱
    let displayName: String  // 显示名称
    let isPro: Bool         // Pro状态
}
```

---

### [FC-02]: 后端登录API端点

- 职责: 接收Apple ID Token，验证用户身份，创建Session并返回
- 函数签名: `handleAppleLogin(request: Request, env: AuthWorkerEnv): Promise<Response>`
- 所在文件: `cloudflare/workers/auth/src/controllers/auth.controller.ts`

>>>>> 输入 (Input): HTTP Request

客户端发送的登录请求：

```typescript
// HTTP请求体
interface LoginRequest {
  idToken: string;          // Apple ID Token
  provider: 'apple';        // 认证提供方
}

// HTTP Headers
{
  "Content-Type": "application/json"
}
```

<<<<< 输出 (Output): HTTP Response

标准HTTP响应，包含Session信息：

```typescript
// 成功响应 (HTTP 200)
interface LoginSuccessResponse {
  success: true;
  session: {
    sessionId: string;      // 永久有效的Session ID
    user: {
      id: string;          // 用户ID
      email: string;       // 用户邮箱
      displayName: string; // 显示名称
      isPro: boolean;      // Pro状态
    };
  };
}

// 失败响应 (HTTP 401)
interface LoginErrorResponse {
  success: false;
  error: "INVALID_APPLE_TOKEN" | "USER_CREATION_FAILED" | "SESSION_CREATION_FAILED";
  message: string;
}
```

---

### [FC-03]: Session创建和管理服务

- 职责: 为已验证用户创建永久Session，存储到数据库
- 函数签名: `createUserSession(user: UserWithComputedProps, env: AuthWorkerEnv): Promise<string>`
- 所在文件: `cloudflare/workers/auth/src/auth/session.service.ts`

>>>>> 输入 (Input): UserWithComputedProps

已验证的用户对象：

```typescript
interface UserWithComputedProps {
  id: string;                    // 用户ID
  email: string;                 // 用户邮箱
  provider: 'apple';             // 认证提供方
  displayName: string;           // 显示名称
  subscription_expires_at: string | null; // 订阅过期时间
  isPro: boolean;                // 计算得出的Pro状态
  createdAt: string;             // 创建时间
  updatedAt: string;             // 更新时间
}
```

<<<<< 输出 (Output): string (Session ID)

生成的Session ID字符串：

```typescript
// 返回随机生成的Session ID
const sessionId: string = "sess_1a2b3c4d5e6f7g8h9i0j" // 24字符随机字符串
```

---

### [FC-04]: Session验证中间件

- 职责: 验证API请求中的Session ID，获取用户信息
- 函数签名: `verifySession(sessionId: string, env: AuthWorkerEnv): Promise<UserWithComputedProps | null>`
- 所在文件: `cloudflare/workers/auth/src/middleware/auth.middleware.ts`

>>>>> 输入 (Input): string (Session ID)

从HTTP请求头中提取的Session ID：

```typescript
// 从Authorization header中提取
const sessionId: string = "sess_1a2b3c4d5e6f7g8h9i0j"
```

<<<<< 输出 (Output): UserWithComputedProps | null

验证成功返回用户信息，失败返回null：

```typescript
// 成功时返回完整用户信息
interface UserWithComputedProps {
  id: string;
  email: string;
  provider: 'apple';
  displayName: string;
  subscription_expires_at: string | null;
  isPro: boolean;
  createdAt: string;
  updatedAt: string;
}

// 失败时返回
null
```

## 5. AI Agent 需要了解的文件上下文

<context_files>
cloudflare/workers/auth/migrations/0001_create_users_table.sql
cloudflare/workers/auth/src/auth/apple-auth.service.ts
cloudflare/workers/auth/src/auth/auth.service.ts
cloudflare/workers/auth/src/auth/jwt.service.ts
cloudflare/workers/auth/src/controllers/auth.controller.ts
cloudflare/workers/auth/src/middleware/auth.middleware.ts
cloudflare/workers/auth/src/types/auth-types.ts
iOS/Packages/SharedModels/Sources/SharedModels/TokenManager.swift
cloudflare/workers/auth/wrangler.toml
</context_files>

## 6. 核心业务流程伪代码

```typescript
// 客户端登录流程
async function signInWithApple(): Promise<UserSession> {
    // [FC-01] 获取Apple凭据
    const credential = await getAppleCredential()

    // 发送到后端
    const response = await fetch('/api/v1/auth/apple', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            idToken: credential.identityToken,
            provider: 'apple'
        })
    })

    const result = await response.json()

    // 存储Session ID到Keychain
    await storeSessionId(result.session.sessionId)

    return result.session
}

// 服务器端登录处理
async function handleAppleLogin(request: Request, env: AuthWorkerEnv): Promise<Response> {
    try {
        // [FC-02] 解析请求
        const { idToken } = await request.json()

        // 验证Apple Token并获取/创建用户
        const user = await verifyAppleTokenAndGetUser(idToken, env)

        // [FC-03] 创建Session
        const sessionId = await createUserSession(user, env)

        return new Response(JSON.stringify({
            success: true,
            session: {
                sessionId,
                user: {
                    id: user.id,
                    email: user.email,
                    displayName: user.displayName,
                    isPro: user.isPro
                }
            }
        }), { status: 200 })

    } catch (error) {
        return new Response(JSON.stringify({
            success: false,
            error: 'LOGIN_FAILED',
            message: error.message
        }), { status: 401 })
    }
}

// Session验证中间件
async function authMiddleware(request: Request, env: AuthWorkerEnv): Promise<UserWithComputedProps | Response> {
    // [FC-04] 提取Session ID
    const sessionId = request.headers.get('Authorization')?.replace('Bearer ', '')

    if (!sessionId) {
        return new Response('Unauthorized', { status: 401 })
    }

    // 验证Session
    const user = await verifySession(sessionId, env)

    if (!user) {
        return new Response('Invalid session', { status: 401 })
    }

    return user
}
```
```
```