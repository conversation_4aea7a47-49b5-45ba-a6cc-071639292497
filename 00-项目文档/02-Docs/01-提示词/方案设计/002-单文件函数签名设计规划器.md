# 018 - 单文件函数签名设计规划器

## 🎯 提示词使命与背景

### 为什么需要基于函数签名的精确设计？

#### **传统功能讨论的根本问题**
1. **函数职责边界模糊**: 缺少明确的函数签名定义，容易导致职责不清和功能重叠
2. **数据结构设计不精确**: 没有具体的类型定义，难以评估数据处理的复杂度
3. **接口设计缺乏一致性**: 缺少统一的设计规范，容易产生不一致的API
4. **实现路径不明确**: 没有清晰的函数调用关系，难以评估实现的可行性

#### **函数签名级设计的解决方案**
通过**函数签名 + 数据结构**的精确设计方法，我们可以：
- ✅ **建立精确的功能边界**: 通过函数签名明确每个函数的职责和接口
- ✅ **设计完整的数据结构**: 定义精确的输入输出类型和数据转换
- ✅ **规划清晰的调用关系**: 展示函数间的依赖和协作模式
- ✅ **验证实现可行性**: 通过具体的签名和类型验证设计方案

---

## 📋 核心任务定义

你的任务是基于用户的功能需求，创建**单文件函数签名设计文档**，这种文档应该：

### 🎯 **提供完整的函数级设计方案**
- 设计合理的函数划分和职责分配
- 定义精确的函数签名和参数类型
- 规划完整的数据结构和类型定义
- 设计清晰的函数调用关系和数据流

### 🏗️ **使用一以贯之的可视化语言**
- 统一的马卡龙色彩系统和设计规范
- 语义化的颜色分配和图标使用
- 清晰的视觉层次和信息组织
- 高对比度的文字和边框设计

### 📊 **包含多个维度的设计**
- 函数关系设计图：函数调用与数据流
- 执行流程设计图：详细的处理步骤
- 数据转换设计图：类型定义与转换
- 错误处理设计图：异常情况和恢复

---

## 🛠️ 标准化文档结构模板

### 必需的文档结构
````markdown
# [编号] - [文件名]函数签名设计方案

## 📋 功能需求分析
[用户功能需求的理解和分析，核心目标和约束条件]

## 📁 文件架构设计
### 单文件在项目中的位置
```
project-root/
├── [相关模块]/
│   ├── [当前设计文件.ts]     # [新增] 本次设计的核心文件
│   ├── [依赖文件.ts]         # [引用] 需要导入的文件
│   └── [类型文件.ts]         # [共享] 类型定义文件
└── [其他相关文件]/
```

### 文件依赖关系说明
- **[新增]** `当前文件`: 本次设计的核心功能文件
- **[导入]** `依赖文件`: 需要导入的外部函数和类型
- **[导出]** `接口定义`: 对外提供的函数和类型
- **[配置]** `常量定义`: 相关的配置常量和默认值

## 🏗️ 函数关系设计 ⭐ **必须使用Mermaid图表**
### 函数调用与数据流 (使用 graph TB)
[Mermaid graph TB图表 - 设计的函数结构、调用关系、数据流向]

### 函数职责说明
[按照导出函数、内部函数、工具函数等分类说明设计思路]

## 🔄 执行流程设计 ⭐ **必须使用Mermaid流程图**
### 核心函数处理步骤 (使用 flowchart TD)
[Mermaid flowchart TD - 设计的详细执行步骤、决策节点、错误处理]

## 📊 数据转换设计 ⭐ **必须使用Mermaid数据流图**
### 类型定义与转换关系 (使用 graph LR)
[Mermaid graph LR图表 - 设计的数据结构、类型转换、配置常量]

## ⚠️ 错误处理设计 ⭐ **必须使用Mermaid错误流程图**
### 异常情况和恢复机制 (使用 flowchart TD)
[Mermaid flowchart TD - 设计的错误处理策略和恢复流程]

## 💡 设计原则与考量
### 函数设计原则
[说明设计中体现的原则和模式]

### 性能和可维护性考量
[解释关键设计选择的原因]

## 🚀 实施建议
### 开发顺序、测试策略、优化方向
[提供具体的实施指导]
````

---

## 🎨 Mermaid图表设计规范

### 马卡龙柔和色彩风格
```css
%% 定义样式 - 函数设计色彩系统
classDef exportFunc fill:#FFE4E1,stroke:#000000,stroke-width:3px,color:#000000
classDef internalFunc fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
classDef utilFunc fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
classDef inputType fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
classDef outputType fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
classDef intermediateType fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
classDef configType fill:#F5F5DC,stroke:#000000,stroke-width:2px,color:#000000
classDef errorType fill:#FFB6C1,stroke:#000000,stroke-width:2px,color:#000000
classDef planned fill:#E6E6FA,stroke:#000000,stroke-width:2px,color:#000000
```

### 设计原则要求
1. **语义化颜色系统**
   - **导出函数**: `#FFE4E1` (浅粉色) - 对外API，最重要
   - **内部函数**: `#E6F3FF` (浅蓝色) - 内部实现逻辑
   - **工具函数**: `#FFF8DC` (浅黄色) - 辅助功能
   - **输出类型**: `#F0FFF0` (浅绿色) - 返回数据结构
   - **配置类型**: `#F5F5DC` (米色) - 配置和常量
   - **错误类型**: `#FFB6C1` (浅粉红) - 错误和异常
   - **规划中**: `#E6E6FA` (浅紫色) - 待设计的组件

2. **函数签名表达**
   - 包含完整的函数签名：参数类型、返回类型
   - 使用TypeScript语法表达类型信息
   - 包含详细的功能描述和实现要点
   - 标注性能要求和约束条件

3. **线条注释规范** ⭐ **必须添加**
   - **设计关系连接线**: 必须添加设计意图注释
     ```mermaid
     MAIN_FUNC -->|"调用核心逻辑"| CORE_FUNC
     CORE_FUNC -->|"返回处理结果"| MAIN_FUNC
     INPUT_VALIDATOR -->|"验证失败"| ERROR_HANDLER
     ```
   - **数据转换连接线**: 必须添加转换说明注释
     ```mermaid
     RAW_INPUT -->|"解析为InputDTO"| PARSED_DATA
     PARSED_DATA -->|"验证并转换"| VALIDATED_DATA
     VALIDATED_DATA -->|"处理为OutputDTO"| FINAL_RESULT
     ```
   - **注释内容要求**:
     - 使用双引号包围注释文字
     - 设计阶段注释应描述设计意图和预期行为
     - 数据流注释应标明数据转换的具体过程
     - 错误处理注释应说明错误类型和处理策略

### 暗色模式适配规范

#### 图表类型的样式配置差异

**Graph/Flowchart（流程图）**
```mermaid
graph TB
    %% 使用classDef定义样式类
    classDef default fill:#E6F3FF,stroke:#FFFFFF,stroke-width:2px,color:#000000

    %% 使用linkStyle设置连接线为白色
    linkStyle default stroke:#FFFFFF,stroke-width:2px
```

**SequenceDiagram（序列图）** ⭐ **强制要求**
```mermaid
%%{init: {
  'theme': 'dark',
  'themeVariables': {
    'primaryTextColor': '#ffffff',    // 主要文字颜色
    'lineColor': '#ffffff',           // 线条颜色
    'noteTextColor': '#000000',       // Note文字颜色（有背景）
    'noteBkgColor': '#fbbf24',        // Note背景颜色
    'activationBkgColor': '#fbbf24'   // 激活框背景
  }
}}%%
sequenceDiagram
```

#### 颜色分配策略

**文字颜色原则**
- **有背景色的文字** → 黑色 (`#000000`)
  - Note标题（黄色背景）
  - 消息框内容（黄色背景）
  - 组件内文字（有填充色背景）

- **无背景色的文字** → 白色 (`#FFFFFF`)
  - 连接线标签
  - Loop/Alt条件文字
  - 裸露的说明文字

**线条和边框**
- **所有连接线** → 白色 (`#FFFFFF`)
- **组件边框** → 白色 (`#FFFFFF`)
- **线条粗细** → 2px（保证可见性）

#### 最佳实践

**统一配置 vs 单独设置**
```typescript
// ✅ 推荐：统一配置（序列图）
%%{init: {'theme': 'dark', 'themeVariables': {...}}}%%

// ✅ 推荐：统一配置（流程图）
linkStyle default stroke:#FFFFFF,stroke-width:2px

// ❌ 不推荐：单独设置
<span style='color:#FFFFFF'>文字</span>  // 可能渲染失败
```

**配置位置**
- **序列图**：在图表开头使用 `%%{init: {...}}%%`
- **流程图**：在图表末尾使用 `linkStyle` 和 `classDef`

#### 推荐的暗色主题配置

**完整的序列图暗色配置**
```mermaid
%%{init: {
  'theme': 'dark',
  'themeVariables': {
    'primaryColor': '#1f2937',        // 主色调
    'primaryTextColor': '#ffffff',    // 主要文字
    'primaryBorderColor': '#ffffff',  // 主要边框
    'lineColor': '#ffffff',           // 连接线
    'noteTextColor': '#000000',       // Note文字（黑色，因为有黄色背景）
    'noteBkgColor': '#fbbf24',        // Note背景（黄色）
    'noteBorderColor': '#ffffff',     // Note边框
    'activationBkgColor': '#fbbf24',  // 激活框背景
    'sequenceNumberColor': '#ffffff'  // 序号颜色
  }
}}%%
```

**流程图暗色配置**
```mermaid
graph TB
    %% 组件样式（保持填充色，边框改白色）
    classDef default fill:#E6F3FF,stroke:#FFFFFF,stroke-width:2px,color:#000000
    classDef highlight fill:#FFB6C1,stroke:#FFFFFF,stroke-width:2px,color:#000000

    %% 连接线样式
    linkStyle default stroke:#FFFFFF,stroke-width:2px
```

#### 暗色模式检查清单

**序列图检查项**
- [ ] 添加了 `%%{init: {...}}%%` 配置
- [ ] 设置了 `primaryTextColor: '#ffffff'`
- [ ] 设置了 `lineColor: '#ffffff'`
- [ ] 设置了 `noteTextColor: '#000000'`（因为Note有背景）
- [ ] 测试了所有文字在暗色背景下的可读性

**流程图检查项**
- [ ] 添加了 `linkStyle default stroke:#FFFFFF`
- [ ] 更新了 `classDef` 中的 `stroke` 为白色
- [ ] 保持了组件内文字为黑色（因为有填充背景）
- [ ] 添加了连接线标签的白色设置

---

## 📝 具体执行步骤

### 第1步：功能需求分析
1. **理解核心功能**: 分析用户要实现的核心功能和业务逻辑
2. **识别输入输出**: 确定函数的输入参数和期望输出
3. **分析约束条件**: 性能要求、错误处理、兼容性要求
4. **确定设计边界**: 明确文件的职责范围和接口边界

### 第2步：函数关系设计 ⭐ **必须使用 graph TB**
- 使用 `graph TB` 展示函数调用关系和数据流
- 设计合理的函数划分和职责分配
- 确定函数间的调用关系和依赖
- 规划数据在函数间的流转
- 考虑函数的可复用性和可测试性

### 第3步：执行流程设计 ⭐ **必须使用 flowchart TD**
- 使用 `flowchart TD` 展示详细的执行步骤
- 设计核心函数的详细执行步骤
- 规划条件判断和分支逻辑
- 设计错误处理和异常情况
- 考虑性能优化和资源管理

### 第4步：数据转换设计 ⭐ **必须使用 graph LR**
- 使用 `graph LR` 展示数据结构转换关系
- 设计精确的数据结构和类型定义
- 规划数据的转换和映射关系
- 设计配置常量和默认值
- 考虑数据验证和类型安全

### 第5步：错误处理设计 ⭐ **必须使用 flowchart TD**
- 使用 `flowchart TD` 展示错误处理流程
- 设计完善的错误处理策略
- 规划异常情况的恢复机制
- 设计错误信息和日志记录
- 考虑错误的传播和隔离

### 第6步：设计验证和优化
- 验证函数签名的完整性和一致性
- 评估性能和内存使用
- 识别潜在的边界情况和风险
- 提供实施建议和测试策略

---

## 💡 设计重点指南
### **奥卡姆剃刀原则**
1. 如无必要，勿增实体，你需要使用奥卡姆剃刀，简化你的方案，让方案设计刚好满足需求即可。
2. 你所提供的数据结构，必须是满足核心需求的最小实体集合。
3. 然后我们在极度简化的平台上，重新审视和添加我们的方案。
#### 默认选择“最简单路径”
对于任何一个需求，第一版提案，都将是能够满足该需求的、最简单、最线性的版本。刻意避免预先设计复杂的、处理各种边缘情况的分支。

#### 最小化“实体”定义
在定义数据结构时，严格遵守“如无必要，勿增实体”的信条。每一个字段、每一个参数、每一个数据模型，都必须有其存在的、直接且必要的理由。

例如：当我们设计用户注册功能时，一个未经“剃刀”修剪的方案可能会立刻定义一个包含十几个字段的User模型。应用奥卡姆剃刀后，第一版提案将只包含{ id: string, email: string, provider: 'apple' | 'google' }，因为这是满足“无密码登录”这个核心需求的最小实体集合。

#### 从“刚刚好”开始，按需演进

我们将首先构建一个“刚好满足当前需求”的极简平台。然后，我们再在这个极其稳固和清晰的平台上，去审视那些被我们“剃掉”的需求。
只有当一个新需求被证明是必要的、且无法在现有实体上扩展时，我们才为其增加新的实体。这是一种“即时（Just-in-time）”的复杂度引入策略。

#### 持续的“质疑”与“简化”
在我们协作的每一步，主动扮演那个手持剃刀的“质疑者”角色。你会不断地反问：
“这个新的字段，解决了什么当下的问题？”
“我们是否可以用一个现有的实体，通过组合或变换，来满足这个新需求？”
“如果暂时不做这个功能，我们的核心流程是否依然完整？”
### 🔍 **函数设计原则**
1. **单一职责原则**: 每个函数都有明确的单一职责
2. **纯函数优先**: 尽量设计无副作用的纯函数
3. **类型安全**: 使用严格的类型定义确保类型安全
4. **错误处理**: 明确的错误处理和返回值设计

### 🔄 **数据流设计**
1. **输入验证**: 对所有输入参数进行验证
2. **数据转换**: 清晰的数据转换和映射逻辑
3. **状态管理**: 合理的状态管理和数据持久化
4. **输出格式**: 一致的输出格式和错误响应

### 🏗️ **性能考量**
1. **时间复杂度**: 分析算法的时间复杂度
2. **空间复杂度**: 考虑内存使用和优化
3. **并发安全**: 设计线程安全的函数
4. **缓存策略**: 合理的缓存和优化策略

---

## 📚 设计模式参考

### 常用的函数设计模式
1. **工厂函数**: 创建对象的工厂函数
2. **高阶函数**: 接受或返回函数的函数
3. **柯里化**: 函数参数的部分应用
4. **组合函数**: 函数的组合和管道
5. **装饰器模式**: 功能增强的装饰器

### 错误处理模式
1. **Result类型**: 使用Result<T, E>表示可能失败的操作
2. **Option类型**: 使用Option<T>表示可能为空的值
3. **异常传播**: 错误的向上传播和处理
4. **错误恢复**: 错误情况的恢复和重试

---

## 📚 真实设计示例参考

### TTS处理函数设计示例 (真实项目)

#### 函数关系设计图示例 (graph TB)
```mermaid
graph TB
    %% 定义样式
    classDef exportFunc fill:#FFE4E1,stroke:#000000,stroke-width:3px,color:#000000
    classDef internalFunc fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef utilFunc fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef inputType fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef outputType fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    classDef configType fill:#F5F5DC,stroke:#000000,stroke-width:2px,color:#000000

    %% 输入数据类型
    INPUT_SINGLE["TTSTaskInput<br/>{<br/>  ttsId: string,<br/>  text: string,<br/>  type: TTSType<br/>}"]

    INPUT_BATCH["TTSTaskInput[]<br/>// 批量任务数组"]

    %% 导出函数 (对外API)
    PROCESS_SINGLE["🎵 processRealtimeTTS()<br/>async (task: TTSTaskInput, env: Env)<br/>→ Promise&lt;TTSProcessingResult&gt;<br/><br/>// 核心TTS处理函数<br/>// 完整的单任务处理流程<br/>// 性能监控和错误处理"]

    PROCESS_BATCH["📦 processBatchRealtimeTTS()<br/>async (tasks: TTSTaskInput[], env: Env,<br/>       maxConcurrency: number = 50)<br/>→ Promise&lt;TTSProcessingResult[]&gt;<br/><br/>// 批量处理 + 并发控制<br/>// Promise.allSettled错误隔离"]

    UPLOAD_R2["📤 uploadAudioToR2()<br/>async (ttsId: string, audioBuffer: ArrayBuffer,<br/>       env: Env)<br/>→ Promise&lt;string&gt;<br/><br/>// R2存储上传服务<br/>// CDN URL生成"]

    %% 内部函数
    PERFORM_UPLOAD["🔄 performR2Upload()<br/>async (params: R2UploadParams)<br/>→ Promise&lt;string&gt;<br/><br/>// 内部实现函数<br/>// 执行具体的R2上传操作"]

    %% 工具函数
    VALIDATE_TASK["🔍 validateTTSTask()<br/>(task: TTSTaskInput) → boolean<br/><br/>// 任务验证工具<br/>// 参数完整性检查"]

    BUILD_RESULT["🏗️ buildProcessingResult()<br/>(ttsId: string, success: boolean,<br/> metrics: PerformanceMetrics)<br/>→ TTSProcessingResult<br/><br/>// 结果构建工具<br/>// 统一的结果格式"]

    %% 输出数据类型
    OUTPUT_SINGLE["TTSProcessingResult<br/>{<br/>  ttsId: string,<br/>  success: boolean,<br/>  status: 'completed'|'failed',<br/>  audioUrl?: string,<br/>  errorMessage?: string,<br/>  processingTime: number,<br/>  azureCallTime: number,<br/>  r2UploadTime: number,<br/>  dbUpdateTime: number<br/>}"]

    OUTPUT_BATCH["TTSProcessingResult[]<br/>// 批量处理结果数组"]

    %% 配置类型
    CONFIG["TTS_CONFIG<br/>{<br/>  MAX_CONCURRENT_REQUESTS: 50,<br/>  REQUEST_TIMEOUT: 30000,<br/>  RETRY_ATTEMPTS: 3,<br/>  AUDIO_FORMAT: 'riff-24khz-16bit-mono-pcm'<br/>}"]

    %% 主要数据流
    INPUT_SINGLE --> PROCESS_SINGLE
    INPUT_BATCH --> PROCESS_BATCH

    PROCESS_SINGLE --> VALIDATE_TASK
    PROCESS_SINGLE --> UPLOAD_R2
    PROCESS_SINGLE --> BUILD_RESULT
    PROCESS_SINGLE --> OUTPUT_SINGLE

    PROCESS_BATCH --> PROCESS_SINGLE
    PROCESS_BATCH --> OUTPUT_BATCH

    UPLOAD_R2 --> PERFORM_UPLOAD

    CONFIG -.-> PROCESS_SINGLE
    CONFIG -.-> PROCESS_BATCH

    %% 应用样式
    class PROCESS_SINGLE,PROCESS_BATCH,UPLOAD_R2 exportFunc
    class PERFORM_UPLOAD internalFunc
    class VALIDATE_TASK,BUILD_RESULT utilFunc
    class INPUT_SINGLE,INPUT_BATCH inputType
    class OUTPUT_SINGLE,OUTPUT_BATCH outputType
    class CONFIG configType
```

#### 执行流程设计图示例 (flowchart TD)
```mermaid
flowchart TD
    %% 定义样式
    classDef startEnd fill:#FFE4E1,stroke:#000000,stroke-width:3px,color:#000000
    classDef process fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef decision fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef external fill:#F5F5DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef error fill:#FFB6C1,stroke:#000000,stroke-width:2px,color:#000000

    START(["🎵 processRealtimeTTS()<br/>输入: TTSTaskInput, Env<br/>输出: Promise&lt;TTSProcessingResult&gt;"])

    %% 初始化阶段
    INIT["📊 初始化性能计时器<br/>const startTime = Date.now()<br/>const metrics = {<br/>  azureCallTime: 0,<br/>  r2UploadTime: 0,<br/>  dbUpdateTime: 0<br/>}"]

    %% 验证阶段
    VALIDATE{"🔍 validateTTSTask(task)<br/>→ boolean<br/><br/>检查任务有效性:<br/>• ttsId非空<br/>• text长度合理<br/>• type值有效"}

    VALIDATE_ERROR["❌ 验证失败<br/>return buildProcessingResult(<br/>  task.ttsId,<br/>  false,<br/>  '无效的任务参数'<br/>)"]

    %% 状态更新
    UPDATE_PROCESSING["📝 updateTaskStatus()<br/>await updateTaskStatus(<br/>  task.ttsId,<br/>  'processing',<br/>  {},<br/>  env<br/>)<br/><br/>SQL: UPDATE tts_tasks<br/>SET status = 'processing'"]

    %% Azure TTS调用
    AZURE_CALL["☁️ callAzureRealtimeTTS()<br/>const azureStart = Date.now()<br/>const audioBuffer = await<br/>  callAzureRealtimeTTSWithRetry(<br/>    task.text,<br/>    task.type,<br/>    env<br/>  )<br/>metrics.azureCallTime = Date.now() - azureStart"]

    %% R2上传
    R2_UPLOAD["📤 uploadAudioToR2()<br/>const r2Start = Date.now()<br/>const audioUrl = await uploadAudioToR2(<br/>  task.ttsId,<br/>  audioBuffer,<br/>  env<br/>)<br/>metrics.r2UploadTime = Date.now() - r2Start"]

    %% 状态更新2
    UPDATE_COMPLETED["✅ updateTaskStatus()<br/>const dbStart = Date.now()<br/>await updateTaskStatus(<br/>  task.ttsId,<br/>  'completed',<br/>  { audioUrl, completedAt: new Date().toISOString() },<br/>  env<br/>)<br/>metrics.dbUpdateTime = Date.now() - dbStart"]

    SUCCESS_RESULT["🎉 构建成功结果<br/>return buildProcessingResult(<br/>  task.ttsId,<br/>  true,<br/>  metrics,<br/>  audioUrl<br/>)"]

    SUCCESS_END(["✅ 返回成功结果<br/>TTSProcessingResult"])

    %% 错误处理
    CATCH_ERROR["⚠️ catch (error)<br/>const errorMessage = error instanceof Error<br/>  ? error.message<br/>  : '未知错误'<br/><br/>console.error(`[TTS] 处理失败: ${task.ttsId}`, error)"]

    FAILED_RESULT["💔 构建失败结果<br/>return buildProcessingResult(<br/>  task.ttsId,<br/>  false,<br/>  metrics,<br/>  undefined,<br/>  errorMessage<br/>)"]

    FAILED_END(["❌ 返回失败结果<br/>TTSProcessingResult"])

    %% 主流程
    START --> INIT
    INIT --> VALIDATE
    VALIDATE -->|"true"| UPDATE_PROCESSING
    VALIDATE -->|"false"| VALIDATE_ERROR

    UPDATE_PROCESSING --> AZURE_CALL
    AZURE_CALL --> R2_UPLOAD
    R2_UPLOAD --> UPDATE_COMPLETED
    UPDATE_COMPLETED --> SUCCESS_RESULT
    SUCCESS_RESULT --> SUCCESS_END

    %% 错误处理流程
    VALIDATE_ERROR --> FAILED_END
    AZURE_CALL -.->|"异常"| CATCH_ERROR
    R2_UPLOAD -.->|"异常"| CATCH_ERROR
    UPDATE_COMPLETED -.->|"异常"| CATCH_ERROR

    CATCH_ERROR --> FAILED_RESULT
    FAILED_RESULT --> FAILED_END

    %% 应用样式
    class START,SUCCESS_END,FAILED_END startEnd
    class INIT,SUCCESS_RESULT,CATCH_ERROR,FAILED_RESULT process
    class VALIDATE decision
    class UPDATE_PROCESSING,AZURE_CALL,R2_UPLOAD,UPDATE_COMPLETED external
    class VALIDATE_ERROR error
```

#### 数据转换设计图示例 (graph LR)
```mermaid
graph LR
    %% 定义样式
    classDef inputType fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef outputType fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    classDef intermediateType fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef configType fill:#F5F5DC,stroke:#000000,stroke-width:2px,color:#000000

    %% 输入数据类型
    subgraph INPUT["📥 输入数据"]
        TASK_INPUT["TTSTaskInput<br/>{<br/>  ttsId: string,<br/>  text: string,<br/>  type: TTSType<br/>}"]

        ENV_INPUT["Env<br/>{<br/>  TTS_DB: D1Database,<br/>  AUDIO_BUCKET: R2Bucket,<br/>  AZURE_TTS_KEY: string,<br/>  AZURE_TTS_REGION: string<br/>}"]
    end

    %% 中间数据类型
    subgraph INTERMEDIATE["🔄 中间数据"]
        AZURE_PARAMS["AzureTTSParams<br/>{<br/>  text: string,<br/>  voice: string,<br/>  format: string,<br/>  ssml: string<br/>}"]

        AUDIO_BUFFER["ArrayBuffer<br/>// WAV音频数据<br/>// 24kHz-16bit-mono<br/>// 大小: 20-100KB"]

        R2_PARAMS["R2UploadParams<br/>{<br/>  key: string,<br/>  body: ArrayBuffer,<br/>  contentType: 'audio/wav',<br/>  cacheControl: string<br/>}"]

        METRICS["PerformanceMetrics<br/>{<br/>  startTime: number,<br/>  azureCallTime: number,<br/>  r2UploadTime: number,<br/>  dbUpdateTime: number<br/>}"]
    end

    %% 输出数据类型
    subgraph OUTPUT["📤 输出数据"]
        RESULT["TTSProcessingResult<br/>{<br/>  ttsId: string,<br/>  success: boolean,<br/>  status: TaskStatus,<br/>  audioUrl?: string,<br/>  errorMessage?: string,<br/>  processingTime: number,<br/>  azureCallTime: number,<br/>  r2UploadTime: number,<br/>  dbUpdateTime: number<br/>}"]

        AUDIO_URL["string<br/>// CDN URL<br/>'https://audio.senseword.app/<br/>{ttsId}.wav'"]
    end

    %% 配置类型
    subgraph CONFIG["⚙️ 配置常量"]
        VOICE_MAP["VOICE_MAPPING<br/>{<br/>  'phonetic_bre': 'en-GB-MiaNeural',<br/>  'phonetic_name': 'en-US-AndrewNeural',<br/>  'example_sentence': 'en-US-AndrewNeural'<br/>}"]

        TTS_CONFIG["TTS_CONFIG<br/>{<br/>  MAX_TEXT_LENGTH: 1000,<br/>  AUDIO_FORMAT: 'riff-24khz-16bit-mono-pcm',<br/>  CACHE_CONTROL: 'public, max-age=31536000'<br/>}"]
    end

    %% 数据转换流程
    TASK_INPUT --> AZURE_PARAMS
    ENV_INPUT --> AZURE_PARAMS
    AZURE_PARAMS --> AUDIO_BUFFER
    AUDIO_BUFFER --> R2_PARAMS
    R2_PARAMS --> AUDIO_URL

    TASK_INPUT --> METRICS
    METRICS --> RESULT
    AUDIO_URL --> RESULT

    %% 配置影响
    VOICE_MAP -.-> AZURE_PARAMS
    TTS_CONFIG -.-> AZURE_PARAMS
    TTS_CONFIG -.-> R2_PARAMS

    %% 应用样式
    class TASK_INPUT,ENV_INPUT inputType
    class RESULT,AUDIO_URL outputType
    class AZURE_PARAMS,AUDIO_BUFFER,R2_PARAMS,METRICS intermediateType
    class VOICE_MAP,TTS_CONFIG configType
```

#### 错误处理设计图示例 (flowchart TD)
```mermaid
flowchart TD
    %% 定义样式
    classDef normal fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef error fill:#FFB6C1,stroke:#000000,stroke-width:2px,color:#000000
    classDef recovery fill:#90EE90,stroke:#000000,stroke-width:2px,color:#000000
    classDef final fill:#FFE4E1,stroke:#000000,stroke-width:3px,color:#000000

    START(["🎵 函数开始执行"])

    %% 输入验证错误
    VALIDATE_ERROR["❌ 输入验证错误<br/>• 参数为空<br/>• 类型不匹配<br/>• 格式错误"]

    VALIDATION_RECOVERY["🔧 验证错误处理<br/>return {<br/>  success: false,<br/>  status: 'failed',<br/>  errorMessage: '输入参数无效',<br/>  errorCode: 'INVALID_INPUT'<br/>}"]

    %% Azure API错误
    AZURE_ERROR["❌ Azure TTS错误<br/>• 网络超时<br/>• API限流<br/>• 认证失败<br/>• 服务不可用"]

    AZURE_RETRY["🔄 Azure重试机制<br/>for (let i = 0; i < 3; i++) {<br/>  try {<br/>    return await callAzureAPI()<br/>  } catch (error) {<br/>    if (i === 2) throw error<br/>    await delay(1000 * (i + 1))<br/>  }<br/>}"]

    AZURE_RECOVERY["🔧 Azure错误处理<br/>return {<br/>  success: false,<br/>  status: 'failed',<br/>  errorMessage: 'Azure TTS调用失败',<br/>  errorCode: 'AZURE_API_ERROR',<br/>  retryCount: 3<br/>}"]

    %% R2存储错误
    R2_ERROR["❌ R2存储错误<br/>• 上传失败<br/>• 权限不足<br/>• 存储空间不足<br/>• 网络中断"]

    R2_RECOVERY["🔧 R2错误处理<br/>// 清理临时数据<br/>await cleanupTempData(audioBuffer)<br/><br/>return {<br/>  success: false,<br/>  status: 'failed',<br/>  errorMessage: 'R2存储上传失败',<br/>  errorCode: 'R2_UPLOAD_ERROR'<br/>}"]

    %% 数据库错误
    DB_ERROR["❌ 数据库错误<br/>• 连接超时<br/>• 更新失败<br/>• 约束冲突<br/>• 事务回滚"]

    DB_RECOVERY["🔧 数据库错误处理<br/>// 标记为需要重试<br/>await markForRetry(ttsId)<br/><br/>return {<br/>  success: false,<br/>  status: 'failed',<br/>  errorMessage: '数据库更新失败',<br/>  errorCode: 'DB_UPDATE_ERROR',<br/>  needsRetry: true<br/>}"]

    %% 未知错误
    UNKNOWN_ERROR["❌ 未知错误<br/>• 系统异常<br/>• 内存不足<br/>• 其他运行时错误"]

    UNKNOWN_RECOVERY["🔧 未知错误处理<br/>// 记录详细错误信息<br/>console.error('未知错误:', error)<br/>await logError(error, context)<br/><br/>return {<br/>  success: false,<br/>  status: 'failed',<br/>  errorMessage: '系统内部错误',<br/>  errorCode: 'UNKNOWN_ERROR'<br/>}"]

    %% 最终结果
    ERROR_RESULT(["💔 返回错误结果<br/>TTSProcessingResult"])

    %% 错误流程
    START -.-> VALIDATE_ERROR
    START -.-> AZURE_ERROR
    START -.-> R2_ERROR
    START -.-> DB_ERROR
    START -.-> UNKNOWN_ERROR

    VALIDATE_ERROR --> VALIDATION_RECOVERY
    AZURE_ERROR --> AZURE_RETRY
    AZURE_RETRY -.->|"重试失败"| AZURE_RECOVERY
    R2_ERROR --> R2_RECOVERY
    DB_ERROR --> DB_RECOVERY
    UNKNOWN_ERROR --> UNKNOWN_RECOVERY

    VALIDATION_RECOVERY --> ERROR_RESULT
    AZURE_RECOVERY --> ERROR_RESULT
    R2_RECOVERY --> ERROR_RESULT
    DB_RECOVERY --> ERROR_RESULT
    UNKNOWN_RECOVERY --> ERROR_RESULT

    %% 应用样式
    class START,ERROR_RESULT final
    class VALIDATE_ERROR,AZURE_ERROR,R2_ERROR,DB_ERROR,UNKNOWN_ERROR error
    class AZURE_RETRY recovery
    class VALIDATION_RECOVERY,AZURE_RECOVERY,R2_RECOVERY,DB_RECOVERY,UNKNOWN_RECOVERY normal
```

### 设计特点总结
- **清晰的函数职责划分**: 导出函数、内部函数、工具函数各司其职
- **完整的类型定义**: 输入、输出、配置类型完整定义
- **优秀的错误处理**: 多层次的错误处理和恢复机制
- **详细的性能监控**: 分段计时和完整的性能指标
- **可测试的代码结构**: 纯函数设计，易于单元测试
- **数据转换清晰**: 输入→中间→输出的完整数据流转
- **错误分类处理**: 不同类型错误的专门处理策略

---

## ✅ 设计质量检查清单

### 完整性检查
- [ ] 包含所有必要的函数和类型定义
- [ ] 覆盖完整的功能需求
- [ ] 定义清晰的输入输出接口
- [ ] 考虑错误处理和边界情况

### 可行性检查
- [ ] 函数签名设计合理
- [ ] 性能要求可以满足
- [ ] 实现复杂度可控
- [ ] 测试策略可行

### 可视化质量检查
- [ ] 使用统一的马卡龙色彩风格
- [ ] 函数签名信息完整准确
- [ ] 数据流转关系清晰
- [ ] 包含足够的技术细节

---

## 🚀 开始设计

现在，请基于用户的功能需求，创建单文件函数签名设计文档。记住：

### 🎯 核心目标
**通过精确的函数签名和数据结构设计，与用户建立功能实现的具体共识。**

### 📋 设计原则
1. **精确性优于模糊**: 提供具体的函数签名和类型定义
2. **完整性优于简洁**: 包含足够的细节以指导实现
3. **可实现性优于理想**: 确保设计在约束条件下可以实现
4. **可测试性优于复杂**: 设计易于测试和验证的函数

### ⚠️ **强制要求清单**
- [ ] **必须提供文件在项目中的架构位置**
- [ ] **必须使用四种指定的Mermaid图表类型**:
  - `graph TB` - 函数关系设计
  - `flowchart TD` - 执行流程设计
  - `graph LR` - 数据转换设计
  - `flowchart TD` - 错误处理设计
- [ ] **不允许用文字描述替代Mermaid图表**
- [ ] **所有图表必须使用马卡龙色彩风格**
- [ ] **必须包含完整的函数签名和类型定义**

开始你的函数签名设计吧！
