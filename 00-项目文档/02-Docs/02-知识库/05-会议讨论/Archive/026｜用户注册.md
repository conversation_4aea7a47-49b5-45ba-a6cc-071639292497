
#### **功能单元三：新用户首次注册 (`[AUTH-FC-01]`)**

这是对我们`handleUserAuth`能力内部逻辑的一个细化，专门处理“**用户第一次登录**”的场景。

  * **职责**:
    当一个通过Apple/Google认证的用户首次访问我们的系统时，在`users`表中为他创建一个新的记录。

  * **输入 (Keyframe A): `ValidatedIdTokenPayload`**

      * 这是后端在成功验证了Apple/Google的ID Token后，从中解析出的、可信的用户信息。

    <!-- end list -->

    ```typescript
    interface ValidatedIdTokenPayload {
      id: string;      // Apple的sub或Google的sub
      email: string;
      displayName?: string;
      provider: 'apple' | 'google';
    }
    ```

  * **输出 (Keyframe B): `NewUserRecord_DB`**

      * 一个被成功插入到D1数据库`users`表中的新行。

  * **后端伪代码逻辑**:

      * 这个逻辑是`handleUserAuth`服务的一部分。

    <!-- end list -->

    ```typescript
    // 伪代码: workers/api/src/auth/service.ts

    async function findOrCreateUser(payload: ValidatedIdTokenPayload, env: Env): Promise<User> {
        // 1. 尝试根据ID查找用户
        const existingUser = await env.DB.prepare("SELECT * FROM users WHERE id = ?").bind(payload.id).first();

        if (existingUser) {
            // 如果用户已存在，直接返回
            return existingUser;
        }

        // 2. 如果用户不存在，则创建新用户 (关键逻辑)
        const newUser = {
            id: payload.id,
            email: payload.email,
            provider: payload.provider,
            displayName: payload.displayName || 'New User',
            subscription_expires_at: null, // 新用户默认为非Pro
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        const stmt = env.DB.prepare(
            "INSERT INTO users (id, email, provider, displayName, subscription_expires_at, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?)"
        ).bind(...Object.values(newUser));

        await stmt.run();

        return newUser;
    }
    ```
