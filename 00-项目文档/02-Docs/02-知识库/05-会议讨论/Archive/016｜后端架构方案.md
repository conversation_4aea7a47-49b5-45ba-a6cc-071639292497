好的，我们正式进入执行阶段。

根据我们之前所有的战略讨论和技术选型，以下是为您梳理的、关于SenseWord后端当前既定的**存储方案**与**接口能力**的完整概览。这可以作为我们后续所有KDD“函数契约补间链”设计的基础。

---

### **后端存储方案 (Backend Storage Plan)**

我们的存储架构遵循“**源头可靠，边缘极速**”的原则，由两个核心部分构成：

**1. 主数据库 (Primary Database): Cloudflare D1**

* **选型理由**: 我们选择D1作为核心数据存储，而非KV，主要是因为它解决了两个关键问题：
    * **强一致性**: 确保了当一个新单词被AI生成后，该结果在全球范围内立即可用，从根本上避免了因数据同步延迟而导致的“重复生成”和API成本浪费。
    * **事务性更新能力**: D1作为关系型数据库，能完美支持我们为`userFeedbackScore`设计的`+1`或`-1`这样的原子性更新操作，而这是KV不擅长的。
* **存储内容**:
    * **`words` 表**: 存储由AI生成的、包含完整`content`和`metadata`的JSON对象。
    * **`users` 表**: 存储极简的用户信息（如`id`, `email`, `provider`等），不包含任何密码。
    * **`authorized_devices` 表**: 存储通过`App Attest`验证后的设备信息及其绑定的动态密钥。
* **容量考量**: 基于您的亲手测试，35,000个单词仅占用230MB，D1的10GB存储上限足以容纳超过150万个单词，对于我们聚焦英语学习的战略而言，容量完全充裕。

**2. 全球缓存层 (Global Caching Layer): Cloudflare CDN**

* **角色定位**: D1数据库身前的“**第一道、也是最强的防线**”。它像一个自动为我们工作的、全球分布的“只读KV存储”。
* **工作机制**: 自动缓存所有从D1读取的、非个性化的单词查询JSON结果。由于我们的架构是“读取密集型”，预计**99%以上**的用户请求将直接由离他们最近的CDN边缘节点响应，无需触及我们的后端数据库。
* **核心价值**: 带来了极致的全球访问速度和极低的后端负载与成本。

---

### **后端接口能力 (Backend API Capabilities)**

我们的后端逻辑将完全构建在**Cloudflare Workers**之上，扮演一个“**轻量化智能路由**”的角色。

**1. 认证与安全体系 (Authentication & Security)**

* **无密码登录**: 明确**只支持“Sign in with Apple”和“Sign in with Google”**，不在我们的数据库中存储任何密码，将安全责任委托给平台方，同时极大简化技术栈。
* **设备认证与动态密钥**:
    * **强验证注册**: App首次启动时，必须通过苹果官方的 **`App Attest`** 服务获取“身份凭证”，发送给一个专门的认证端点。该端点在通过苹果服务器验证后，才为该设备颁发一个唯一的、长期的**动态密钥**。
    * **日常请求验证**: App的每一次核心API请求，都必须在请求头中携带这个“动态密钥”，Worker会校验其在数据库中的合法性。
* **核心作用**: 这套体系旨在**最大限度地提升自动化脚本爬取我们数据内容的成本**。

**2. 核心功能接口 (Core Functional APIs)**

* **单词查询/生成接口 (`GET /api/word/{wordName}` M `lang={langCode}`)**:
    * 这是最核心的接口。它接收一个英语单词和用户的母语代码。
    * Worker首先查询D1数据库。
    * 如果命中，直接返回已有的JSON数据。
    * 如果未命中，Worker将调用AI（使用我们V8.0版的提示词）进行**实时生成**，将新生成的内容存入D1，然后再返回给用户。
* **用户反馈接口 (`POST /api/word/{wordName}/feedback`)**:
    * 用于处理用户的“赞👍”或“踩👎”操作。
    * 接收一个类似`{ "action": "like" }`或`{ "action": "dislike" }`的请求体。
    * 在D1数据库中对相应单词的`userFeedbackScore`字段执行`+1`或`-1`的原子性更新。

---

**总结**

我们当前确定的后端架构，是一个**高度精益、安全且具备全球化服务能力**的无服务器（Serverless）体系。它将复杂性降至最低，将性能和安全性最大化，完美地契合了您作为独立开发者的需求和我们产品的商业模式。

现在，这个清晰的蓝图已经铺开，我们可以开始针对其中任何一个具体功能，进行我们的第一次KDD“函数契约补间链”设计了。