# SenseWord 用户注册功能设计方案

## 📋 会议背景
本次会议讨论了新用户首次注册功能的设计方案，这是对`handleUserAuth`能力内部逻辑的细化，专门处理"**用户第一次登录**"的场景。

---

## 1. 功能需求定义

### 1.1 核心功能
**新用户首次注册 (`[AUTH-FC-01]`)**：当通过Apple/Google认证的用户首次访问系统时，在`users`表中创建新的用户记录。

### 1.2 功能职责
- 验证用户身份的真实性（通过Apple/Google ID Token）
- 检查用户是否已存在于系统中
- 为新用户创建完整的用户档案记录
- 设置合理的默认值和初始状态

### 1.3 业务场景
- **首次登录**: 用户通过Sign in with Apple/Google首次访问应用
- **身份验证**: 后端验证ID Token的有效性
- **用户创建**: 在数据库中建立用户记录
- **会话建立**: 返回用户信息用于后续API调用

---

## 2. 数据结构设计

### 2.1 输入关键帧A：`ValidatedIdTokenPayload`

#### 2.1.1 数据来源
后端成功验证Apple/Google ID Token后，从中解析出的可信用户信息。

#### 2.1.2 数据结构
```typescript
interface ValidatedIdTokenPayload {
  id: string;           // Apple的sub或Google的sub，用户唯一标识
  email: string;        // 用户邮箱地址
  displayName?: string; // 用户显示名称（可选）
  provider: 'apple' | 'google'; // 认证提供方
}
```

#### 2.1.3 字段说明

| 字段 | 类型 | 必需 | 说明 |
|-----|------|------|------|
| `id` | string | ✅ | 来自认证提供方的唯一用户标识符 |
| `email` | string | ✅ | 用户邮箱，用于联系和账户恢复 |
| `displayName` | string | ❌ | 用户显示名称，Apple可能不提供 |
| `provider` | enum | ✅ | 区分认证来源，便于后续处理 |

### 2.2 输出关键帧B：`NewUserRecord_DB`

#### 2.2.1 数据描述
成功插入到D1数据库`users`表中的新用户记录。

#### 2.2.2 数据结构
```typescript
interface NewUserRecord_DB {
  id: string;                      // 用户唯一标识符
  email: string;                   // 用户邮箱地址
  provider: 'apple' | 'google';    // 认证提供方
  displayName: string;             // 用户显示名称
  subscription_expires_at: null;   // 订阅到期时间（新用户为null）
  createdAt: string;               // 账户创建时间
  updatedAt: string;               // 最后更新时间
}
```

---

## 3. 技术实现方案

### 3.1 核心业务逻辑

#### 3.1.1 函数签名
```typescript
async function findOrCreateUser(
  payload: ValidatedIdTokenPayload, 
  env: Env
): Promise<User>
```

#### 3.1.2 处理流程
1. **查找现有用户**: 根据ID检查用户是否已存在
2. **返回现有用户**: 如果用户存在，直接返回用户信息
3. **创建新用户**: 如果用户不存在，创建新的用户记录
4. **返回新用户**: 返回创建的用户信息

### 3.2 详细实现代码

#### 3.2.1 主要逻辑实现
```typescript
// workers/api/src/auth/service.ts

async function findOrCreateUser(
  payload: ValidatedIdTokenPayload, 
  env: Env
): Promise<User> {
  
  console.log(`[用户注册] 处理用户认证: ${payload.email} (${payload.provider})`);
  
  try {
    // 1. 尝试根据ID查找现有用户
    console.log(`[用户注册] 查找现有用户: ${payload.id}`);
    
    const existingUser = await env.DB.prepare(
      "SELECT * FROM users WHERE id = ?"
    ).bind(payload.id).first();
    
    if (existingUser) {
      console.log(`[用户注册] 用户已存在: ${existingUser.email}`);
      
      // 更新最后登录时间
      await env.DB.prepare(
        "UPDATE users SET updatedAt = ? WHERE id = ?"
      ).bind(new Date().toISOString(), payload.id).run();
      
      return existingUser;
    }
    
    // 2. 用户不存在，创建新用户记录
    console.log(`[用户注册] 创建新用户: ${payload.email}`);
    
    const newUser: NewUserRecord_DB = {
      id: payload.id,
      email: payload.email,
      provider: payload.provider,
      displayName: payload.displayName || generateDefaultDisplayName(payload.email),
      subscription_expires_at: null, // 新用户默认为非Pro
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // 3. 插入新用户记录到数据库
    const insertStmt = env.DB.prepare(`
      INSERT INTO users (
        id, email, provider, displayName, 
        subscription_expires_at, createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `).bind(
      newUser.id,
      newUser.email,
      newUser.provider,
      newUser.displayName,
      newUser.subscription_expires_at,
      newUser.createdAt,
      newUser.updatedAt
    );
    
    const result = await insertStmt.run();
    
    if (!result.success) {
      throw new Error('Failed to create user record');
    }
    
    console.log(`[用户注册] 新用户创建成功: ${newUser.email}`);
    
    // 4. 记录用户注册事件（可选：用于分析）
    await logUserRegistrationEvent(newUser, env);
    
    return newUser;
    
  } catch (error) {
    console.error('[用户注册] 用户创建失败:', error);
    throw new Error(`User registration failed: ${error.message}`);
  }
}
```

#### 3.2.2 辅助函数实现

##### 默认显示名称生成
```typescript
function generateDefaultDisplayName(email: string): string {
  // 从邮箱地址生成默认显示名称
  const username = email.split('@')[0];
  
  // 简单处理：首字母大写，限制长度
  const displayName = username.charAt(0).toUpperCase() + 
                     username.slice(1).toLowerCase();
  
  return displayName.length > 20 ? displayName.substring(0, 20) : displayName;
}
```

##### 用户注册事件记录
```typescript
async function logUserRegistrationEvent(
  user: NewUserRecord_DB, 
  env: Env
): Promise<void> {
  try {
    // 记录用户注册事件，用于后续分析
    console.log(`[用户分析] 新用户注册: ${user.provider} - ${user.email}`);
    
    // 可以发送到分析服务或记录到专门的事件表
    // 这里只做日志记录
    
  } catch (error) {
    // 事件记录失败不应该影响主流程
    console.warn('[用户分析] 注册事件记录失败:', error);
  }
}
```

---

## 4. 错误处理策略

### 4.1 常见错误场景

#### 4.1.1 数据库约束冲突
```typescript
// 处理邮箱重复的情况
if (error.message.includes('UNIQUE constraint failed: users.email')) {
  console.error('[用户注册] 邮箱地址已存在:', payload.email);
  throw new Error('Email address already registered');
}
```

#### 4.1.2 认证信息无效
```typescript
// 验证必需字段
if (!payload.id || !payload.email || !payload.provider) {
  throw new Error('Invalid authentication payload: missing required fields');
}
```

#### 4.1.3 数据库连接异常
```typescript
// 数据库操作失败的处理
if (!result.success) {
  console.error('[用户注册] 数据库插入失败:', result.error);
  throw new Error('Database operation failed');
}
```

### 4.2 错误响应格式
```typescript
interface UserRegistrationError {
  success: false;
  error: 'INVALID_TOKEN' | 'EMAIL_EXISTS' | 'DATABASE_ERROR' | 'SYSTEM_ERROR';
  message: string;
  timestamp: string;
}
```

---

## 5. 安全考虑

### 5.1 输入验证
- **ID Token验证**: 确保Token来自可信的认证提供方
- **邮箱格式验证**: 验证邮箱地址的有效性
- **字段长度限制**: 防止过长的输入导致存储问题

### 5.2 数据保护
- **敏感信息处理**: 不记录或传输敏感的认证信息
- **日志安全**: 确保日志中不包含敏感数据
- **错误信息**: 避免在错误响应中泄露系统内部信息

### 5.3 防重放攻击
```typescript
// 验证Token的时效性
const tokenIssuedAt = new Date(payload.iat * 1000);
const now = new Date();
const tokenAge = now.getTime() - tokenIssuedAt.getTime();

if (tokenAge > 5 * 60 * 1000) { // 5分钟过期
  throw new Error('Token expired');
}
```

---

## 6. 性能优化

### 6.1 数据库优化
- **索引使用**: 确保id和email字段有适当索引
- **查询优化**: 使用prepared statements提高性能
- **连接管理**: 合理管理数据库连接

### 6.2 缓存策略
```typescript
// 可选：对频繁查询的用户信息进行缓存
const cacheKey = `user:${payload.id}`;
const cachedUser = await env.CACHE.get(cacheKey);

if (cachedUser) {
  return JSON.parse(cachedUser);
}
```

---

## 7. 监控和分析

### 7.1 关键指标
- **注册成功率**: 监控用户注册的成功率
- **认证提供方分布**: 统计Apple vs Google的使用比例
- **注册失败原因**: 分析注册失败的主要原因
- **响应时间**: 监控注册流程的性能

### 7.2 日志记录
```typescript
// 结构化日志记录
const logData = {
  event: 'user_registration',
  userId: newUser.id,
  email: newUser.email,
  provider: newUser.provider,
  timestamp: new Date().toISOString(),
  success: true
};

console.log(JSON.stringify(logData));
```

---

## 📊 实施总结

### 8.1 核心价值
用户注册功能是整个认证体系的基础，为用户提供无缝的首次登录体验。

### 8.2 关键特性
- **自动化处理**: 无需用户手动填写注册信息
- **多提供方支持**: 同时支持Apple和Google认证
- **数据完整性**: 确保用户数据的一致性和完整性
- **错误恢复**: 完善的错误处理和恢复机制

### 8.3 架构优势
- **简洁设计**: 符合极简架构哲学
- **安全可靠**: 基于可信的第三方认证
- **性能优秀**: 高效的数据库操作
- **易于维护**: 清晰的代码结构和错误处理

### 8.4 实施建议
这个用户注册功能设计完善，可以直接进入开发阶段。建议优先实现核心逻辑，然后逐步完善监控和分析功能。
