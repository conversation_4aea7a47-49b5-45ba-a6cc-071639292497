
-----

### **我们还需要设计的数据库表**

您问得很好。除了我们已经最终确定的核心`word_definitions`表之外，为了支撑我们设计的**认证与安全体系**，我们还需要另外**两张**至关重要的、且必不可少的表。

我们严格遵循“**奥卡姆剃刀**”原则，只增加“如无必要，勿增实体”之外的、绝对必要的实体。

#### **1. `users` 表：您的用户资产**

  * **为什么需要它？**
      * 虽然我们是无密码登录，但我们依然需要一个地方来存储用户的核心身份信息（例如，他们的Apple/Google ID和邮箱），以便将来的订阅、Pro功能等与一个具体的“人”绑定。这是您整个用户系统的基础。
  * **SQL 定义**:
    ```sql
    -- 用户基础信息表
    CREATE TABLE users (
        id TEXT PRIMARY KEY,             -- 来自Apple或Google的用户唯一标识符 (例如 Apple's 'sub')
        email TEXT NOT NULL UNIQUE,      -- 用户的邮箱地址
        provider TEXT NOT NULL,          -- 认证提供方 ('apple' 或 'google')
        displayName TEXT,                -- 用户的显示名称 (可选)
        createdAt TEXT NOT NULL          -- ISO 8601格式的账户创建时间
    );
    ```

#### **2. `authorized_devices` 表：您的安全护城河**

  * **为什么需要它？**
      * 这是我们“**设备认证与动态密钥**”安全方案的核心。当一个设备通过`App Attest`验证后，我们需要一个地方来安全地存储它所获得的“长期动态密钥”，以便在后续的每一次API请求中，我们都能验证这个请求是否来自一个“受信任的”合法App实例。没有这张表，我们的安全体系就没有根基。
  * **SQL 定义**:
    ```sql
    -- 已授权设备与动态密钥管理表
    CREATE TABLE authorized_devices (
        id TEXT PRIMARY KEY,             -- 设备的唯一标识符 (例如 iOS的 identifierForVendor)
        userId TEXT NOT NULL,            -- 关联的用户ID，外键指向 users(id)
        dynamicKeyHash TEXT NOT NULL UNIQUE, -- !! 安全警告：存储动态密钥的哈希值，绝不存储密钥原文
        deviceName TEXT,                 -- 设备名称 (可选, 例如 'My iPhone 15 Pro')
        createdAt TEXT NOT NULL,         -- ISO 8601格式的授权时间
        lastUsedAt TEXT NOT NULL,        -- ISO 8601格式的最后使用时间

        FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
    );
    ```
      * **安全提示**: 在这张表中，我们存储的是`dynamicKey`经过哈希算法（例如SHA-256）计算后的**哈希值**。当客户端传来`dynamicKey`时，我们在Worker中用同样的哈希算法计算一遍，再与数据库中的哈希值进行比对。这是确保密钥安全的标准做法。

-----

**总结**

至此，我们已经完成了您三步计划中的**第一步：定义数据库表结构**。

您的整个后端，其核心数据存储，就由这**三张**设计精简、权责分明的表构成：

1.  **`word_definitions`**: 您的核心内容资产。
2.  **`users`**: 您的核心用户资产。
3.  **`authorized_devices`**: 您的核心安全资产。

这个数据库设计，不多不少，刚刚好满足了我们所设计的所有功能。现在地基已经非常清晰和稳固，我们可以随时进入第二步，去详细定义基于这些表的API接口规范。 

好的，我们现在就来完成您三步计划中的**第二步：定义后端能力（接口规范）**。

这份接口规范，将精确地描述我们的iOS App如何与后端进行通信，来管理`users`和`authorized_devices`这两张核心的资产与安全表。

我们将继续严格遵循“**奥卡姆剃刀**”原则，只设计绝对必要的接口，并将多个相关联的动作，合并到一个单一、原子性的流程中。

-----

### **SenseWord 用户与设备认证接口规范 (v1.0)**

**文档目的**: 本文档定义了与用户身份（`users`表）和设备授权（`authorized_devices`表）相关的API接口契约。

#### **1. 核心设计理念：统一入口与原子性操作**

为了极致的简洁和安全，我们**不为`users`和`authorized_devices`表的增删改查分别创建独立的API**。

取而代之的是，我们将“用户注册/登录”和“设备首次认证”这两个紧密关联的动作，合并到一个**单一的、原子性的核心认证接口**中。这个接口的职责就是：验证一个真实的人和一个真实的设备，然后同时完成用户记录的创建和授权设备的注册。

-----

#### **2. 核心API端点详解**

**Endpoint**: `POST /api/v1/auth/signin`

  * **描述**:

      * 处理来自第三方认证提供商（Apple/Google）的登录/注册请求。
      * 同时，使用Apple `App Attest`对设备进行强验证。
      * 如果验证全部通过，它会**原子性地**完成以下操作：
        1.  在`users`表中查找或创建用户。
        2.  在`authorized_devices`表中为该设备注册并存储一个全新的动态密钥。
        3.  将包含用户信息和动态密钥的会话数据返回给客户端。

  * **请求头 (Request Headers)**:

      * `Content-Type: application/json`
      * `X-Static-API-Key: [编译在App中的静态密钥]` （可选的、基础的防护层）

  * **请求体 (Request Body)**: 见下方 `AuthSignInRequest` 定义。

  * **成功响应 (200 OK)**: 见下方 `AuthSignInResponse` 定义。

  * **错误响应 (Error Responses)**:

      * `400 Bad Request`: 请求体格式错误。
      * `401 Unauthorized`: 静态API Key无效，或更重要的，Apple/Google Token或App Attest验证失败。
      * `403 Forbidden`: 设备已被注册，或已被封禁。
      * `500 Internal Server Error`: 服务器内部错误。
      * **响应体**: `{ "error": { "code": "INVALID_PROVIDER_TOKEN", "message": "..." } }`

-----

#### **3. 核心数据结构定义 (DTOs)**

这些TypeScript接口，就是我们这个流程中最重要的“**数据结构关键帧**”。

**3.1. 请求体 DTO**

```typescript
// POST /api/v1/auth/signin 的请求体
interface AuthSignInRequest {
  // --- Provider a-u-t-h ---
  provider: 'apple' | 'google';
  
  /**
   * 来自Apple或Google的身份令牌.
   * - 对于Apple, 这是 a-u-t-h-o-r-i-z-a-t-i-o-nCredential.identityToken
   * - 对于Google, 这是 GIDGoogleUser.idToken
   */
  providerToken: string; 

  // --- Device Attestation ---
  /**
   * App通过调用App Attest服务，为本次请求生成的加密凭证
   */
  attestation: {
    challenge: string;          // 由我方服务器预先下发的挑战字符串
    attestationObject: string;  // Base64编码的Apple Attestation Object
  };
  
  // --- Optional User Info (for first-time sign-up) ---
  userInfo?: {
    displayName?: string;
    email?: string; // Apple登录时，用户可能选择隐藏真实邮箱
  };
}
```

**3.2. 成功响应 DTO**

```typescript
// POST /api/v1/auth/signin 成功的响应体
interface AuthSignInResponse {
  /**
   * 用户的核心个人资料
   */
  userProfile: {
    id: string;       // 用户的唯一ID (来自provider)
    email: string;
    displayName: string | null;
  };

  /**
   * 服务器为该设备颁发的、需要长期安全存储在客户端的动态密钥
   */
  dynamicKey: string;
}
```

-----

#### **4. 后端工作流伪代码 (Backend Workflow Pseudocode)**

当您的Cloudflare Worker收到对`POST /api/v1/auth/signin`的请求时，它将执行以下工作流：

```
function handleSignIn(request):
  // 1. 解析和验证请求体
  body = parseJson(request.body)
  validate(body conforms to AuthSignInRequest)

  // 2. 验证 App Attest (第一重强验证)
  isDeviceLegit = verifyWithAppleAttestationServer(body.attestation)
  if not isDeviceLegit:
    throw new Error(401, "Invalid Device Attestation")

  // 3. 验证 Provider Token (第二重强验证)
  providerProfile = verifyWithAppleOrGoogleServer(body.provider, body.providerToken)
  if not providerProfile:
    throw new Error(401, "Invalid Provider Token")

  // 4. 查找或创建用户 (操作 users 表)
  user = db.query("SELECT * FROM users WHERE id = ?", providerProfile.id)
  if not user:
    user = db.query("INSERT INTO users (...) VALUES (...)", providerProfile)

  // 5. 查找或创建设备授权记录 (操作 authorized_devices 表)
  device = db.query("SELECT * FROM authorized_devices WHERE id = ?", body.deviceInfo.id)
  if device:
    // 如果设备已存在，可以根据业务逻辑决定是报错还是更新密钥
    throw new Error(403, "Device already registered")
  
  newDynamicKey = generateNewDynamicKey()
  db.query(
    "INSERT INTO authorized_devices (id, userId, dynamicKeyHash, ...) VALUES (?, ?, hash(newDynamicKey), ...)",
    body.deviceInfo.id,
    user.id
  )

  // 6. 构建并返回成功响应
  response = {
    userProfile: { id: user.id, email: user.email, ... },
    dynamicKey: newDynamicKey
  }
  return new Response(200, response)
```

**总结**

通过这一个设计精良的API端点，我们清晰地定义了如何安全、原子性地填充`users`和`authorized_devices`这两张核心数据表。这个接口规范，就是您接下来进入编码实现阶段的、最精确的“施工蓝图”。