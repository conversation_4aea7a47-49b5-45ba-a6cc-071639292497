# AI审核智能编辑指令系统实现

## 摘要

本文档记录了将AI审核系统从传统的"评价+建议"模式升级为"评价+可执行编辑指令"模式的完整过程。通过引入结构化的编辑指令，实现了AI审核结果的自动化执行，大幅提升了内容修复的效率和准确性。

## 用户故事 (User Stories)

### 开发者价值
- **AS A** 内容管理开发者
- **I WANT** AI审核结果能够直接生成可执行的数据库更新指令
- **SO THAT** 我可以自动化执行内容修复，而不需要人工解读AI的文字建议

### 运营价值
- **AS A** 内容质量管理员
- **I WANT** 系统能够自动修复AI识别的内容问题
- **SO THAT** 我可以专注于更高层次的质量控制，而不是重复性的修改工作

## 功能描述 (Feature Description)

### 核心功能
1. **智能编辑指令生成**：AI在审核时直接生成结构化的编辑指令
2. **自动化执行引擎**：解析并执行编辑指令，自动更新数据库
3. **多种操作支持**：支持更新、删除、替换、插入、单词拼写修正等操作
4. **精确路径定位**：使用JSON路径准确定位需要修改的字段

### 业务逻辑边界
- 仅处理标记为需要重新生成的单词（aiAuditShouldRegenerate = 1）
- 支持contentJson内所有嵌套字段的精确修改
- 保持向后兼容性，不影响现有审核流程

## 背景

之前的AI审核系统只能提供文字形式的改进建议，如"例句与核心含义不符，应删除"或"单词拼写错误，应为分离的两个词"。这些建议需要人工解读并手动执行，效率低下且容易出错。

通过分析85个需要重新生成的单词的AI评论，发现大部分建议都涉及具体字段的微调，完全可以结构化为可执行的指令。

## 使用mermaid视觉化

```mermaid
graph TD
    A[AI审核输入] --> B[传统模式]
    A --> C[v3智能模式]
    
    B --> D[评分 + 文字建议]
    D --> E[人工解读]
    E --> F[手动修改数据库]
    
    C --> G[评分 + 编辑指令]
    G --> H[自动解析指令]
    H --> I[程序化执行修改]
    
    I --> J[数据库自动更新]
    F --> J
    
    style C fill:#e1f5fe
    style G fill:#e8f5e8
    style I fill:#fff3e0
```

## 功能实现

### 核心概念图式

#### 中心概念
- **编辑指令 (Edit Instructions)**：结构化的数据修改命令
- **JSON路径 (JSON Path)**：精确定位嵌套字段的路径表示法
- **操作类型 (Operation Types)**：标准化的修改操作分类

#### 核心要素
1. **指令结构**：operation, path, newValue, oldValue, reason
2. **路径解析**：支持嵌套对象和数组索引
3. **操作执行**：安全的数据修改和错误处理

### 原理

#### 指令生成原理
AI在审核时，不仅识别问题，还要：
1. 确定具体的修改位置（JSON路径）
2. 选择合适的操作类型
3. 提供修改前后的值
4. 说明修改原因

#### 指令执行原理
执行引擎通过以下步骤处理指令：
1. 解析JSON路径为路径组件
2. 递归访问嵌套数据结构
3. 根据操作类型执行相应修改
4. 验证修改结果并更新数据库

### 思维方式

#### 设计模式
采用**命令模式 (Command Pattern)**：
- 将修改请求封装为对象
- 支持撤销、重做、批量执行
- 解耦请求发送者和接收者

#### 问题解决模式
使用**声明式编程**思维：
- AI声明"需要什么样的修改"
- 系统负责"如何执行修改"
- 提高抽象层次，减少实现细节

### 详细流程分解与示例

#### 1. 提示词增强 (v3.0)
```markdown
**新增字段**: aiAuditEditInstructions (ARRAY)
- 当aiAuditShouldRegenerate = 1时，必须提供编辑指令
- 每个指令包含：operation, path, newValue, reason等字段
```

#### 2. 编辑指令格式
```json
{
  "operation": "update|delete|replace|word_correction|insert",
  "path": "content.coreDefinition",
  "newValue": "修正后的定义",
  "oldValue": "原始定义",
  "reason": "修改原因"
}
```

#### 3. 支持的操作类型

##### word_correction - 单词拼写修正
```json
{
  "operation": "word_correction",
  "path": "word",
  "oldValue": "airconditioning",
  "newValue": "air conditioning",
  "reason": "单词拼写错误，应为分离的两个词"
}
```

##### update - 更新字段值
```json
{
  "operation": "update",
  "path": "content.coreDefinition",
  "newValue": "修正后的核心定义",
  "reason": "原定义不够准确"
}
```

##### delete - 删除不当内容
```json
{
  "operation": "delete",
  "path": "content.usageExamples[1].examples[2]",
  "reason": "例句与核心含义不符，应删除"
}
```

##### replace - 替换内容
```json
{
  "operation": "replace",
  "path": "content.usageExamples[0].examples[0].sentence",
  "oldValue": "错误的例句",
  "newValue": "正确的例句",
  "reason": "语法错误需要修正"
}
```

##### insert - 插入新内容
```json
{
  "operation": "insert",
  "path": "content.usageExamples[0].examples",
  "newValue": {
    "sentence": "新的例句",
    "translation": "翻译"
  },
  "reason": "需要补充更合适的例句"
}
```

#### 4. 路径解析示例
```
content.usageExamples[0].examples[1].sentence
↓
["content", "usageExamples", 0, "examples", 1, "sentence"]
```

#### 5. 执行流程
1. **读取审核结果**：解析JSONL格式的AI审核结果
2. **筛选指令**：只处理aiAuditShouldRegenerate = 1的记录
3. **执行指令**：逐条执行编辑指令
4. **更新数据库**：提交修改到words_for_publish表
5. **生成报告**：记录执行结果和统计信息

## 具体实施的步骤和操作

### 步骤1：创建增强版提示词
```bash
# 创建v3提示词文件
touch prompts/SenseWord\ AI内容审核官_v3.md
```

### 步骤2：开发编辑指令执行器
```bash
# 创建执行脚本
python3 08_apply_edit_instructions.py --audit-results results.jsonl --dry-run
```

### 步骤3：生成v3批处理任务
```bash
# 使用v3提示词生成批处理
python3 09_generate_v3_audit_batch.py --max-words 100
```

### 步骤4：执行编辑指令
```bash
# 实际执行修改
python3 08_apply_edit_instructions.py --audit-results audit_results.jsonl
```

### 步骤5：验证修改结果
```bash
# 提取修改后的单词进行验证
python3 07_extract_words_to_regenerate.py
```

## 下一步可选的完善计划

### 短期优化
1. **指令验证**：添加编辑指令的格式验证和安全检查
2. **回滚机制**：支持修改操作的撤销和回滚
3. **批量优化**：优化大批量指令的执行性能

### 中期扩展
1. **可视化界面**：开发Web界面展示编辑指令和执行结果
2. **智能建议**：基于历史数据优化AI生成的编辑指令
3. **A/B测试**：对比传统模式和智能模式的效果

### 长期规划
1. **多语言支持**：扩展到其他语言的内容审核
2. **实时执行**：支持实时的内容修复和质量监控
3. **机器学习**：使用ML优化编辑指令的准确性和效率

---

*文档创建时间: 2025-07-09*
*实现版本: v1.0*
