# KDD-017: AI驱动的全局每日一词推荐系统

## 技术方案概览

| 项目 | 内容 |
|------|------|
| **功能单元名称** | AI驱动的全局每日一词推荐系统 |
| **优先级** | P0 (核心用户引导功能) |
| **预估工期** | 2天 |
| **依赖关系** | 无 (基于现有word_definitions表，新增AI驱动候选池) |
| **技术栈** | Cloudflare Workers + D1数据库 + CDN缓存 + AI智能策展 |
| **设计原则** | 奥卡姆剃刀 + LPLC原则 + AI智能委托 + 全局一致性 |

---

## 核心目标

### 业务目标
实现完全自动化的每日一词推荐功能，通过AI智能策展消除所有人工干预，为用户提供最自然的产品引导入口。

### 技术目标
- **零人工干预**: AI驱动的候选池自动构建和维护
- **零运营成本**: 一次性配置，永久自动化运行
- **零预生产成本**: 基于LPLC原则按需生产多语言版本
- **极致性能**: 利用CDN实现全球边缘缓存加速
- **架构简洁**: 复用现有单词查询逻辑，最小化新增复杂度

### 用户价值
- **自然引导**: 提供探索起点，触发兴趣驱动无限流
- **零学习成本**: 用户无需了解是编辑推荐还是算法选择
- **全球一致**: 创造共同话题和社交分享语境
- **动态进化**: 候选池随用户探索行为有机增长

---

## 核心创新：AI驱动的动态候选池模型

### 创新理念
将"每日一词"的候选资格判定，从前置的人工任务，转变为与内容生产同步发生的、由AI完成的自动化任务。

### 技术突破
1. **AI智能策展**: 委托AI进行高层次的判断和品味任务
2. **动态候选池**: 候选池随用户搜索行为有机增长
3. **零维护成本**: 完全消除人工编辑和维护环节
4. **自我进化**: 系统随使用而变得更加智能

---

## 接口规范

### API端点设计

#### 1. 获取每日一词
```typescript
GET /api/v1/daily-word
```

**响应格式**:
```typescript
interface DailyWordResponse {
  word: string;           // 今日推荐单词
  date: string;          // 日期 (YYYY-MM-DD)
}
```

**示例响应**:
```json
{
  "word": "epiphany",
  "date": "2025-06-24"
}
```
 
## 数据库迁移方案

### 迁移脚本设计（极简版）
```sql
-- KDD-017: 为AI驱动每日一词系统添加候选池字段
-- 目的：支持高效的候选词筛选和查询

-- 1. 添加候选池标识字段（唯一新增字段，使用驼峰命名）
ALTER TABLE word_definitions
ADD COLUMN isWordOfTheDayCandidate BOOLEAN DEFAULT FALSE;

-- 2. 创建高效查询索引（唯一新增索引）
CREATE INDEX IF NOT EXISTS idx_isWordOfTheDayCandidate
ON word_definitions (isWordOfTheDayCandidate);
```

### 数据同步策略（极简版）
```sql
-- 从现有数据中提取候选标识（如果JSON中已存在）
UPDATE word_definitions
SET isWordOfTheDayCandidate = CASE
  WHEN json_extract(contentJson, '$.metadata.isWordOfTheDayCandidate') = true THEN TRUE
  ELSE FALSE
END
WHERE contentJson IS NOT NULL;
```

---

## 伪代码逻辑

### 1. AI提示词增强逻辑（极简版）
```typescript
// 在现有AI提示词中增加策展指令
const enhancedPrompt = `
${originalPrompt}

## 每日一词候选评估
请在metadata中增加：
- isWordOfTheDayCandidate: boolean - 该词是否适合作为"每日一词"？

评估标准：适合大众学习、有趣、不敏感、难度适中(B2-C1)
`;
```

### 2. 动态候选池查询逻辑（极简版）
```typescript
// 替代硬编码种子池的智能查询
async function getDynamicCandidatePool(): Promise<string[]> {
  console.log(`[Daily Word] 查询动态候选池`);

  // 1. 简单查询所有AI标记的候选词
  const query = "SELECT word FROM word_definitions WHERE isWordOfTheDayCandidate = TRUE";
  const candidates = await env.DB.prepare(query).all();

  // 2. 如果候选池为空（极早期），返回默认词
  if (candidates.length === 0) {
    console.log(`[Daily Word] 候选池为空，使用默认词`);
    return ['welcome'];
  }

  console.log(`[Daily Word] 候选池大小: ${candidates.length}`);
  return candidates.map(c => c.word);
}
```

### 3. 确定性选择算法（极简版）
```typescript
// 基于日期的确定性随机选择
async function selectDailyWord(date: string): Promise<string> {
  const candidatePool = await getDynamicCandidatePool();

  // 基于日期的伪随机选择，确保同一天全球一致
  const dateHash = hashDate(date);
  const index = dateHash % candidatePool.length;

  const selectedWord = candidatePool[index];

  console.log(`[Daily Word] 选择结果: ${selectedWord} (池大小: ${candidatePool.length})`);

  return selectedWord;
}

function hashDate(date: string): number {
  // 确定性哈希函数，保证同一日期产生相同结果
  let hash = 0;
  for (let i = 0; i < date.length; i++) {
    hash = ((hash << 5) - hash + date.charCodeAt(i)) & 0xffffffff;
  }
  return Math.abs(hash);
}
```

### 4. LPLC按需生产逻辑增强
```typescript
// 处理每日一词请求，集成AI候选池逻辑
async function handleDailyWordRequest(language: string): Promise<WordDefinitionResponse> {
  // 1. 获取今日AI选定词汇
  const today = new Date().toISOString().split('T')[0];
  const dailyWord = await selectDailyWord(today);

  // 2. 检查该语言版本是否已存在
  const existingDefinition = await findWordDefinition(db, dailyWord, language);

  if (existingDefinition) {
    console.log(`[Daily Word] 缓存命中: ${dailyWord}-${language}`);
    return formatWordResponse(existingDefinition);
  }

  // 3. 首次请求，触发AI生成（使用增强提示词）
  console.log(`[Daily Word] 首次生产: ${dailyWord}-${language}`);
  const aiContent = await generateWordContentWithCandidateEvaluation(dailyWord, language, env);

  // 4. 保存到数据库（包含候选池字段）
  await saveWordDefinitionWithCandidateInfo(db, aiContent, language);

  // 5. 触发CDN缓存预热
  await warmupCDNCache(dailyWord, language);

  return formatWordResponse(aiContent);
}
```

### 5. AI内容生成增强
```typescript
// 使用增强提示词生成内容，包含候选评估
async function generateWordContentWithCandidateEvaluation(
  word: string,
  languageCode: string,
  env: any
): Promise<AIGeneratedContentWithCandidate> {
  console.log(`[AI Service] 生成内容并评估候选资格: ${word}`);

  // 1. 使用增强的提示词（包含候选评估指令）
  const enhancedPrompt = await processEnhancedPromptForGemini(word, languageCode, env);

  // 2. 调用Gemini API
  const aiResponse = await callGeminiAPI(enhancedPrompt);

  // 3. 解析响应并提取候选信息
  const content = parseAIResponse(aiResponse);

  // 4. 提取候选池相关字段（极简版）
  const candidateInfo = {
    isCandidate: content.metadata.isWordOfTheDayCandidate || false
  };

  console.log(`[AI Service] 候选评估结果: ${word} -> ${candidateInfo.isCandidate}`);

  return {
    ...content,
    candidateInfo
  };
}
```

### 6. 数据库保存增强
```typescript
// 保存单词定义时同时更新候选池字段
async function saveWordDefinitionWithCandidateInfo(
  db: any,
  aiContent: AIGeneratedContentWithCandidate,
  language: string
): Promise<boolean> {
  console.log(`[Word Service] 保存单词定义和候选信息: ${aiContent.word}`);

  try {
    const insertQuery = `
      INSERT OR REPLACE INTO word_definitions (
        word, language, contentJson, coreDefinition,
        isWordOfTheDayCandidate,
        feedbackScore, isHumanReviewed, ttsStatus,
        difficulty, frequency, relatedConcepts,
        promptVersion, createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const now = new Date().toISOString();

    const result = await db.prepare(insertQuery)
      .bind(
        aiContent.word.toLowerCase().trim(),
        language.toLowerCase().trim(),
        JSON.stringify(aiContent.content),
        aiContent.content.coreDefinition,
        aiContent.candidateInfo.isCandidate ? 1 : 0,  // 布尔值转整数
        0.0, // 初始反馈评分
        0,   // 未人工审核
        'pending', // TTS状态
        aiContent.content.difficulty,
        aiContent.metadata.wordFrequency,
        JSON.stringify(aiContent.metadata.relatedConcepts),
        'v8.0', // 新版本提示词
        now,
        now
      )
      .run();

    if (result.success) {
      console.log(`[Word Service] 候选池信息已保存: isWordOfTheDayCandidate=${aiContent.candidateInfo.isCandidate}`);
      return true;
    } else {
      console.error(`[Word Service] 保存失败:`, result);
      return false;
    }

  } catch (error) {
    console.error(`[Word Service] 保存异常:`, error);
    return false;
  }
}
```

---

## Mermaid可视化流程

### 1. AI驱动候选池生命周期
```mermaid
graph TD
    A[用户搜索单词<br/>例如: serendipity] --> B{数据库中<br/>是否存在?}

    B -->|不存在| C[AI生成内容<br/>使用增强提示词]
    B -->|存在| D[直接返回<br/>现有内容]

    C --> E[AI评估候选资格<br/>isWordOfTheDayCandidate: true]

    E --> F[保存到数据库<br/>isWordOfTheDayCandidate = TRUE]

    F --> G[候选池自动扩充<br/>+1个高质量候选词]

    G --> H[下次每日一词选择<br/>可能被选中]

    H --> I[全球用户共享<br/>相同每日一词]

    style A fill:#e1f5fe
    style C fill:#fff3e0
    style E fill:#e8f5e8
    style F fill:#f3e5f5
    style G fill:#fff9c4
    style I fill:#ffebee
```

### 2. 每日选择算法流程
```mermaid
graph LR
    A[每日零点<br/>2025-06-24] --> B[查询候选池<br/>SELECT * FROM word_definitions<br/>WHERE isWordOfTheDayCandidate = TRUE]

    B --> C{候选池<br/>是否为空?}

    C -->|为空| D[返回默认词<br/>welcome]
    C -->|不为空| E[候选池大小<br/>例如: 1,247个词]

    E --> F[日期哈希<br/>hash('2025-06-24') = 42,891]

    F --> G[确定性选择<br/>index = 42,891 % 1,247 = 156]

    G --> H[选中单词<br/>candidates[156] = 'epiphany']

    H --> I[全球缓存<br/>daily-word:2025-06-24 = epiphany]

    I --> J[全球一致性<br/>所有用户看到相同词汇]

    style A fill:#ffcdd2
    style E fill:#c8e6c9
    style H fill:#fff9c4
    style J fill:#e1f5fe
```

### 3. 成本分摊与性能优化
```mermaid
graph TD
    A[第一个德语用户<br/>请求 epiphany] --> B[AI生成德语版本<br/>成本: $0.001]

    B --> C[保存到数据库<br/>包含候选池信息]

    C --> D[CDN缓存分发<br/>德国节点]

    D --> E[第2-10,000个德语用户<br/>零成本消费]

    E --> F[成本分摊<br/>$0.001 ÷ 10,000 = $0.0000001]

    G[候选池查询<br/>isWordOfTheDayCandidate = TRUE] --> H[索引优化查询<br/>< 10ms]

    H --> I[确定性选择<br/>零计算成本]

    I --> J[全球缓存命中<br/>< 50ms 响应]

    style A fill:#ffcdd2
    style B fill:#fff3e0
    style E fill:#c8e6c9
    style F fill:#e8f5e8
    style H fill:#e1f5fe
    style J fill:#f3e5f5
```

---

## 实现优先级与阶段规划（极简版）

### Phase 1: 数据库迁移与AI增强 (Day 1)
- [ ] 执行数据库迁移脚本，添加 `isWordOfTheDayCandidate` 字段
- [ ] 创建 `idx_isWordOfTheDayCandidate` 索引
- [ ] 同步现有数据的候选标识
- [ ] 更新AI提示词，添加候选评估指令
- [ ] 测试AI候选评估功能

### Phase 2: 算法实现与部署 (Day 2)
- [ ] 实现动态候选池查询逻辑
- [ ] 开发确定性选择算法
- [ ] 创建每日一词API端点
- [ ] 集成LPLC按需生产逻辑
- [ ] 配置CDN缓存策略
- [ ] 完整的集成测试和部署

---

## 成功指标

### 技术指标
- **候选池查询性能**: < 10ms (基于索引优化)
- **每日选择算法**: < 1ms (确定性计算)
- **API响应时间**: < 100ms (缓存命中)
- **首次生产时间**: < 3s (AI生成)
- **缓存命中率**: > 95%

### 业务指标
- **候选池增长率**: 每日新增 5-10 个候选词
- **AI评估准确性**: > 90% 的候选词符合质量标准
- **用户引导成功率**: > 90%
- **每日一词点击率**: > 60%
- **后续探索转化率**: > 40%

### 成本效益
- **开发成本**: 2人天 (极简化设计)
- **运营成本**: $0/天 (完全自动化)
- **扩展成本**: $0 (候选池自动增长)
- **维护成本**: 最小 (AI驱动，无人工干预)

---

## 风险评估与缓解

### 技术风险
| 风险项 | 概率 | 影响 | 缓解措施 |
|-------|------|------|---------|
| AI候选评估不准确 | 中 | 中 | 多轮测试优化提示词，设置评分阈值 |
| 候选池增长缓慢 | 低 | 中 | 预设少量高质量种子词作为备选 |
| 数据库迁移风险 | 低 | 高 | 充分测试，准备回滚方案 |
| 索引性能问题 | 低 | 中 | 监控查询性能，优化索引策略 |

### 业务风险
| 风险项 | 概率 | 影响 | 缓解措施 |
|-------|------|------|---------|
| AI选择词汇不受欢迎 | 中 | 低 | 用户反馈机制，动态调整评估标准 |
| 候选池质量下降 | 低 | 中 | 定期审查，设置质量阈值 |
| 缺乏个性化推荐 | 高 | 低 | 通过后续探索实现个性化 |

---

## 与现有架构集成

### 复用现有组件
- **word_definitions表**: 扩展字段，保持向后兼容
- **AI生成服务**: 增强提示词，复用现有Gemini集成
- **LPLC引擎**: 完全复用按需生产逻辑
- **CDN配置**: 复用现有Cloudflare设置

### 新增最小组件
- **候选池字段**: isWordOfTheDayCandidate (唯一新增字段)
- **增强提示词**: 包含候选评估指令
- **动态查询逻辑**: 替代硬编码种子池
- **每日选择API**: 单一端点

### 架构优势
- **零新增复杂度**: 基于现有架构的自然扩展
- **向后兼容**: 不影响现有功能
- **统一数据流**: 与搜索功能使用相同逻辑
- **一致性保证**: 相同的质量标准和错误处理

---

**技术方案总结**: KDD-017通过AI智能委托实现了完全自动化的每日一词推荐系统，消除所有人工干预，候选池随用户探索行为有机增长，完美融合LPLC原则和CDN缓存，为用户提供最自然的产品引导入口。
```
