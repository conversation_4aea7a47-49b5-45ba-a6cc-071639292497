# KDD-008: App Store购买验证技术方案

## 功能单元概览

| 属性 | 值 |
|-----|---|
| **功能单元ID** | KDD-008 |
| **功能名称** | App Store购买验证 |
| **优先级** | P1 (高) |
| **预估工期** | 3天 |
| **依赖关系** | KDD-005 (用户认证注册系统) |
| **功能类型** | 商业功能 |
| **被依赖功能** | KDD-009 (付费墙UI组件系统) |

## 核心目标

实现完整的App Store购买验证和用户订阅状态更新系统，确保购买流程的安全性和可靠性，支持订阅管理和状态同步。

### 依赖关系说明
- **依赖KDD-005用户认证系统**: 需要用户身份识别来关联购买记录
- **用户账户关联**: 购买验证需要更新users表的subscription_expires_at字段
- **安全性保障**: 确保购买记录与正确的用户账户关联

---

## 📊 接口规范

### 1. 购买验证API接口

#### 1.1 验证购买凭证
```typescript
// POST /api/v1/purchase/verify
interface VerifyPurchaseRequest {
  receiptData: string;                // Base64编码的App Store收据
  productId: string;                  // 产品ID (如: com.senseword.pro.monthly)
  transactionId?: string;             // 交易ID (可选)
}

interface VerifyPurchaseResponse {
  success: true;
  user: {
    id: string;
    isPro: boolean;
    subscriptionExpiresAt: string;    // ISO 8601格式
    subscriptionType: string;         // monthly, yearly等
  };
  transaction: {
    transactionId: string;
    productId: string;
    purchaseDate: string;
    expiresDate: string;
  };
}
```

#### 1.2 恢复购买
```typescript
// POST /api/v1/purchase/restore
interface RestorePurchaseRequest {
  receiptData: string;                // 最新的App Store收据
}

interface RestorePurchaseResponse {
  success: true;
  restored: boolean;
  user: {
    id: string;
    isPro: boolean;
    subscriptionExpiresAt?: string;
  };
  restoredTransactions: TransactionInfo[];
}

interface TransactionInfo {
  transactionId: string;
  productId: string;
  purchaseDate: string;
  expiresDate: string;
  isActive: boolean;
}
```

#### 1.3 订阅状态查询
```typescript
// GET /api/v1/purchase/status
interface SubscriptionStatusResponse {
  success: true;
  subscription: {
    isActive: boolean;
    productId?: string;
    expiresAt?: string;
    autoRenewStatus?: boolean;
    gracePeriodExpiresAt?: string;
  };
  nextBillingDate?: string;
}
```

### 2. iOS客户端购买接口

#### 2.1 StoreKit集成接口
```swift
// iOS购买管理协议
protocol PurchaseManagerProtocol {
    func loadProducts() async -> Result<[SKProduct], PurchaseError>
    func purchaseProduct(_ product: SKProduct) async -> Result<PurchaseResult, PurchaseError>
    func restorePurchases() async -> Result<RestoreResult, PurchaseError>
    func getReceiptData() -> Data?
}

struct PurchaseResult {
    let transaction: SKPaymentTransaction
    let receiptData: Data
    let productId: String
}

struct RestoreResult {
    let restoredTransactions: [SKPaymentTransaction]
    let receiptData: Data
}

enum PurchaseError {
    case productsNotAvailable
    case purchaseCancelled
    case purchaseFailed(Error)
    case receiptNotFound
    case verificationFailed
    case networkError
}
```

---

## 🔄 数据结构设计

### 关键帧A: 用户购买请求
```swift
// 用户发起购买时的状态
struct PurchaseInitiation {
    let userId: String
    let productId: String
    let timestamp: Date
    let deviceInfo: DeviceInfo
    let sessionId: String
}

struct DeviceInfo {
    let deviceId: String
    let platform: String
    let osVersion: String
    let appVersion: String
}
```

### 关键帧B: App Store交易完成
```swift
// App Store交易完成后的数据
struct CompletedTransaction {
    let transactionId: String
    let productId: String
    let receiptData: Data
    let transactionDate: Date
    let transactionState: SKPaymentTransactionState
}
```

### 关键帧C: 后端验证请求
```typescript
// 发送到后端验证的数据
interface BackendVerificationRequest {
  userId: string;
  receiptData: string;              // Base64编码
  productId: string;
  transactionId: string;
  deviceInfo: {
    deviceId: string;
    platform: string;
    appVersion: string;
  };
  timestamp: string;
}
```

### 关键帧D: Apple服务器验证
```typescript
// 与Apple服务器通信的数据
interface AppleVerificationRequest {
  'receipt-data': string;           // Base64编码的收据
  'password': string;               // App Store Connect共享密钥
  'exclude-old-transactions': boolean;
}

interface AppleVerificationResponse {
  status: number;                   // 0表示成功
  environment: string;              // Sandbox或Production
  receipt: AppleReceipt;
  latest_receipt_info?: AppleTransaction[];
  pending_renewal_info?: AppleRenewalInfo[];
}
```

### 关键帧E: 数据库状态更新
```typescript
// 更新用户订阅状态的数据
interface SubscriptionUpdate {
  userId: string;
  subscriptionExpiresAt: string;
  productId: string;
  transactionId: string;
  purchaseDate: string;
  environment: string;
  updatedAt: string;
}
```

---

## 💻 核心实现逻辑

### 1. 后端购买验证服务

#### 1.1 购买验证主逻辑
```typescript
// services/purchaseVerificationService.ts

export class PurchaseVerificationService {
  
  /**
   * 验证App Store购买
   */
  static async verifyPurchase(
    request: VerifyPurchaseRequest,
    userId: string,
    env: Env
  ): Promise<VerifyPurchaseResponse> {
    
    console.log(`[购买验证] 开始验证用户${userId}的购买`);
    console.log(`[购买验证] 产品ID: ${request.productId}`);
    
    try {
      // 1. 与Apple服务器验证收据
      const appleResponse = await this.verifyReceiptWithApple(request.receiptData, env);
      
      if (appleResponse.status !== 0) {
        throw new Error(`Apple验证失败: ${appleResponse.status}`);
      }
      
      console.log(`[购买验证] Apple验证成功`);
      
      // 2. 解析交易信息
      const transactionInfo = this.parseTransactionInfo(appleResponse, request.productId);
      
      if (!transactionInfo) {
        throw new Error('未找到匹配的交易信息');
      }
      
      // 3. 验证产品ID
      if (!this.isValidProductId(request.productId)) {
        throw new Error('无效的产品ID');
      }
      
      // 4. 更新用户订阅状态
      const subscriptionUpdate = await this.updateUserSubscription(
        userId,
        transactionInfo,
        env
      );
      
      console.log(`[购买验证] 用户订阅状态已更新`);
      
      // 5. 记录购买事件
      await this.logPurchaseEvent(userId, transactionInfo, env);
      
      // 6. 构建响应
      return {
        success: true,
        user: {
          id: userId,
          isPro: true,
          subscriptionExpiresAt: subscriptionUpdate.expiresAt,
          subscriptionType: this.getSubscriptionType(request.productId)
        },
        transaction: {
          transactionId: transactionInfo.transactionId,
          productId: transactionInfo.productId,
          purchaseDate: transactionInfo.purchaseDate,
          expiresDate: transactionInfo.expiresDate
        }
      };
      
    } catch (error) {
      console.error('[购买验证] 验证失败:', error);
      throw new Error(`购买验证失败: ${error.message}`);
    }
  }
  
  /**
   * 与Apple服务器验证收据
   */
  private static async verifyReceiptWithApple(
    receiptData: string,
    env: Env
  ): Promise<AppleVerificationResponse> {
    
    const verificationURL = env.APPLE_PRODUCTION 
      ? 'https://buy.itunes.apple.com/verifyReceipt'
      : 'https://sandbox.itunes.apple.com/verifyReceipt';
    
    const requestBody: AppleVerificationRequest = {
      'receipt-data': receiptData,
      'password': env.APP_STORE_CONNECT_SECRET,
      'exclude-old-transactions': true
    };
    
    console.log(`[Apple验证] 发送验证请求到: ${verificationURL}`);
    
    const response = await fetch(verificationURL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
      throw new Error(`Apple API请求失败: ${response.status}`);
    }
    
    const result = await response.json() as AppleVerificationResponse;
    
    console.log(`[Apple验证] 验证响应状态: ${result.status}`);
    
    return result;
  }
  
  /**
   * 解析交易信息
   */
  private static parseTransactionInfo(
    appleResponse: AppleVerificationResponse,
    productId: string
  ): ParsedTransactionInfo | null {
    
    // 优先从latest_receipt_info查找
    const latestTransactions = appleResponse.latest_receipt_info || [];
    const matchingTransaction = latestTransactions.find(
      transaction => transaction.product_id === productId
    );
    
    if (matchingTransaction) {
      return {
        transactionId: matchingTransaction.transaction_id,
        productId: matchingTransaction.product_id,
        purchaseDate: new Date(parseInt(matchingTransaction.purchase_date_ms)).toISOString(),
        expiresDate: new Date(parseInt(matchingTransaction.expires_date_ms)).toISOString(),
        isActive: new Date(parseInt(matchingTransaction.expires_date_ms)) > new Date()
      };
    }
    
    // 如果没找到，从receipt.in_app查找
    const inAppTransactions = appleResponse.receipt.in_app || [];
    const receiptTransaction = inAppTransactions.find(
      transaction => transaction.product_id === productId
    );
    
    if (receiptTransaction) {
      return {
        transactionId: receiptTransaction.transaction_id,
        productId: receiptTransaction.product_id,
        purchaseDate: new Date(parseInt(receiptTransaction.purchase_date_ms)).toISOString(),
        expiresDate: new Date(parseInt(receiptTransaction.expires_date_ms)).toISOString(),
        isActive: new Date(parseInt(receiptTransaction.expires_date_ms)) > new Date()
      };
    }
    
    return null;
  }
  
  /**
   * 更新用户订阅状态
   */
  private static async updateUserSubscription(
    userId: string,
    transactionInfo: ParsedTransactionInfo,
    env: Env
  ): Promise<{ expiresAt: string }> {
    
    const updateStmt = env.DB.prepare(`
      UPDATE users 
      SET 
        subscription_expires_at = ?,
        updatedAt = ?
      WHERE id = ?
    `).bind(
      transactionInfo.expiresDate,
      new Date().toISOString(),
      userId
    );
    
    const result = await updateStmt.run();
    
    if (!result.success) {
      throw new Error('数据库更新失败');
    }
    
    return {
      expiresAt: transactionInfo.expiresDate
    };
  }
  
  /**
   * 验证产品ID
   */
  private static isValidProductId(productId: string): boolean {
    const validProductIds = [
      'com.senseword.pro.monthly',
      'com.senseword.pro.yearly',
      'com.senseword.pro.lifetime'
    ];
    
    return validProductIds.includes(productId);
  }
  
  /**
   * 获取订阅类型
   */
  private static getSubscriptionType(productId: string): string {
    if (productId.includes('monthly')) return 'monthly';
    if (productId.includes('yearly')) return 'yearly';
    if (productId.includes('lifetime')) return 'lifetime';
    return 'unknown';
  }
  
  /**
   * 记录购买事件
   */
  private static async logPurchaseEvent(
    userId: string,
    transactionInfo: ParsedTransactionInfo,
    env: Env
  ): Promise<void> {
    
    try {
      const event = {
        timestamp: new Date().toISOString(),
        eventType: 'PURCHASE_COMPLETED',
        userId,
        productId: transactionInfo.productId,
        transactionId: transactionInfo.transactionId,
        purchaseDate: transactionInfo.purchaseDate,
        expiresDate: transactionInfo.expiresDate
      };
      
      console.log('[购买事件]', JSON.stringify(event));
      
      // 可以发送到分析服务
      // await this.sendToAnalyticsService(event, env);
      
    } catch (error) {
      console.warn('[购买事件] 事件记录失败:', error);
    }
  }
}

interface ParsedTransactionInfo {
  transactionId: string;
  productId: string;
  purchaseDate: string;
  expiresDate: string;
  isActive: boolean;
}
```

### 2. iOS客户端购买管理

#### 2.1 PurchaseManager实现
```swift
// PurchaseManager.swift

import StoreKit

class PurchaseManager: NSObject, PurchaseManagerProtocol {
    
    private var products: [SKProduct] = []
    private var purchaseCompletion: ((Result<PurchaseResult, PurchaseError>) -> Void)?
    private var restoreCompletion: ((Result<RestoreResult, PurchaseError>) -> Void)?
    
    override init() {
        super.init()
        SKPaymentQueue.default().add(self)
    }
    
    deinit {
        SKPaymentQueue.default().remove(self)
    }
    
    // MARK: - Public Methods
    
    func loadProducts() async -> Result<[SKProduct], PurchaseError> {
        print("[购买管理] 加载产品信息")
        
        let productIds: Set<String> = [
            "com.senseword.pro.monthly",
            "com.senseword.pro.yearly"
        ]
        
        return await withCheckedContinuation { continuation in
            let request = SKProductsRequest(productIdentifiers: productIds)
            request.delegate = self
            
            // 存储continuation以便在delegate中使用
            self.productRequestContinuation = continuation
            
            request.start()
        }
    }
    
    func purchaseProduct(_ product: SKProduct) async -> Result<PurchaseResult, PurchaseError> {
        print("[购买管理] 开始购买产品: \(product.productIdentifier)")
        
        guard SKPaymentQueue.canMakePayments() else {
            return .failure(.purchaseFailed(NSError(domain: "PurchaseManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "设备不支持内购"])))
        }
        
        return await withCheckedContinuation { continuation in
            self.purchaseCompletion = { result in
                continuation.resume(returning: result)
            }
            
            let payment = SKPayment(product: product)
            SKPaymentQueue.default().add(payment)
        }
    }
    
    func restorePurchases() async -> Result<RestoreResult, PurchaseError> {
        print("[购买管理] 恢复购买")
        
        return await withCheckedContinuation { continuation in
            self.restoreCompletion = { result in
                continuation.resume(returning: result)
            }
            
            SKPaymentQueue.default().restoreCompletedTransactions()
        }
    }
    
    func getReceiptData() -> Data? {
        guard let receiptURL = Bundle.main.appStoreReceiptURL,
              let receiptData = try? Data(contentsOf: receiptURL) else {
            print("[购买管理] 收据数据不可用")
            return nil
        }
        
        return receiptData
    }
    
    // MARK: - Private Methods
    
    private func verifyPurchaseWithBackend(_ transaction: SKPaymentTransaction) async {
        guard let receiptData = getReceiptData() else {
            print("[购买管理] 无法获取收据数据")
            return
        }
        
        do {
            let base64Receipt = receiptData.base64EncodedString()
            
            let verificationRequest = VerifyPurchaseRequest(
                receiptData: base64Receipt,
                productId: transaction.payment.productIdentifier,
                transactionId: transaction.transactionIdentifier
            )
            
            let response = try await APIClient.shared.verifyPurchase(verificationRequest)
            
            if response.success {
                print("[购买管理] 后端验证成功")
                
                // 更新本地用户状态
                UserManager.shared.updateProStatus(
                    isPro: response.user.isPro,
                    expiresAt: response.user.subscriptionExpiresAt
                )
                
                // 完成交易
                SKPaymentQueue.default().finishTransaction(transaction)
                
                // 通知购买成功
                let purchaseResult = PurchaseResult(
                    transaction: transaction,
                    receiptData: receiptData,
                    productId: transaction.payment.productIdentifier
                )
                
                purchaseCompletion?(.success(purchaseResult))
                
            } else {
                print("[购买管理] 后端验证失败")
                purchaseCompletion?(.failure(.verificationFailed))
            }
            
        } catch {
            print("[购买管理] 验证过程出错: \(error)")
            purchaseCompletion?(.failure(.verificationFailed))
        }
    }
    
    private var productRequestContinuation: CheckedContinuation<Result<[SKProduct], PurchaseError>, Never>?
}

// MARK: - SKPaymentTransactionObserver

extension PurchaseManager: SKPaymentTransactionObserver {
    
    func paymentQueue(_ queue: SKPaymentQueue, updatedTransactions transactions: [SKPaymentTransaction]) {
        for transaction in transactions {
            switch transaction.transactionState {
            case .purchased:
                print("[购买管理] 交易完成: \(transaction.payment.productIdentifier)")
                Task {
                    await verifyPurchaseWithBackend(transaction)
                }
                
            case .failed:
                print("[购买管理] 交易失败: \(transaction.error?.localizedDescription ?? "未知错误")")
                SKPaymentQueue.default().finishTransaction(transaction)
                
                if let error = transaction.error as? SKError {
                    switch error.code {
                    case .paymentCancelled:
                        purchaseCompletion?(.failure(.purchaseCancelled))
                    default:
                        purchaseCompletion?(.failure(.purchaseFailed(error)))
                    }
                } else {
                    purchaseCompletion?(.failure(.purchaseFailed(transaction.error ?? NSError())))
                }
                
            case .restored:
                print("[购买管理] 交易已恢复: \(transaction.payment.productIdentifier)")
                Task {
                    await verifyPurchaseWithBackend(transaction)
                }
                
            case .deferred:
                print("[购买管理] 交易延迟: \(transaction.payment.productIdentifier)")
                
            case .purchasing:
                print("[购买管理] 正在购买: \(transaction.payment.productIdentifier)")
                
            @unknown default:
                print("[购买管理] 未知交易状态")
            }
        }
    }
    
    func paymentQueueRestoreCompletedTransactionsFinished(_ queue: SKPaymentQueue) {
        print("[购买管理] 恢复购买完成")
        
        guard let receiptData = getReceiptData() else {
            restoreCompletion?(.failure(.receiptNotFound))
            return
        }
        
        let restoreResult = RestoreResult(
            restoredTransactions: queue.transactions,
            receiptData: receiptData
        )
        
        restoreCompletion?(.success(restoreResult))
    }
    
    func paymentQueue(_ queue: SKPaymentQueue, restoreCompletedTransactionsFailedWithError error: Error) {
        print("[购买管理] 恢复购买失败: \(error.localizedDescription)")
        restoreCompletion?(.failure(.purchaseFailed(error)))
    }
}

// MARK: - SKProductsRequestDelegate

extension PurchaseManager: SKProductsRequestDelegate {
    
    func productsRequest(_ request: SKProductsRequest, didReceive response: SKProductsResponse) {
        print("[购买管理] 产品信息加载完成: \(response.products.count)个产品")
        
        self.products = response.products
        
        if !response.invalidProductIdentifiers.isEmpty {
            print("[购买管理] 无效产品ID: \(response.invalidProductIdentifiers)")
        }
        
        productRequestContinuation?(.success(response.products))
        productRequestContinuation = nil
    }
    
    func request(_ request: SKRequest, didFailWithError error: Error) {
        print("[购买管理] 产品信息加载失败: \(error.localizedDescription)")
        
        productRequestContinuation?(.failure(.productsNotAvailable))
        productRequestContinuation = nil
    }
}
```

---

## 🧪 测试策略

### 1. 后端验证测试
```typescript
// tests/purchaseVerification.test.ts

describe('App Store购买验证测试', () => {
  
  test('有效收据验证', async () => {
    const mockReceipt = generateMockAppleReceipt();
    
    const request: VerifyPurchaseRequest = {
      receiptData: mockReceipt,
      productId: 'com.senseword.pro.monthly'
    };
    
    const response = await PurchaseVerificationService.verifyPurchase(
      request,
      'test-user-123',
      testEnv
    );
    
    expect(response.success).toBe(true);
    expect(response.user.isPro).toBe(true);
    expect(response.transaction.productId).toBe('com.senseword.pro.monthly');
  });
  
  test('无效收据处理', async () => {
    const invalidRequest: VerifyPurchaseRequest = {
      receiptData: 'invalid-receipt-data',
      productId: 'com.senseword.pro.monthly'
    };
    
    await expect(
      PurchaseVerificationService.verifyPurchase(invalidRequest, 'test-user', testEnv)
    ).rejects.toThrow('Apple验证失败');
  });
  
  test('用户订阅状态更新', async () => {
    const userId = 'test-user-123';
    const mockTransaction = createMockTransactionInfo();
    
    await PurchaseVerificationService.updateUserSubscription(
      userId,
      mockTransaction,
      testEnv
    );
    
    const user = await getUserById(userId, testEnv);
    expect(user.subscription_expires_at).toBe(mockTransaction.expiresDate);
  });
});
```

### 2. iOS客户端测试
```swift
// PurchaseManagerTests.swift

class PurchaseManagerTests: XCTestCase {
    
    var purchaseManager: PurchaseManager!
    
    override func setUp() {
        super.setUp()
        purchaseManager = PurchaseManager()
    }
    
    func testLoadProducts() async {
        let result = await purchaseManager.loadProducts()
        
        switch result {
        case .success(let products):
            XCTAssertGreaterThan(products.count, 0)
            XCTAssertTrue(products.contains { $0.productIdentifier == "com.senseword.pro.monthly" })
            
        case .failure(let error):
            XCTFail("产品加载失败: \(error)")
        }
    }
    
    func testReceiptDataAvailability() {
        let receiptData = purchaseManager.getReceiptData()
        
        // 在测试环境中，收据可能不可用
        // 这里主要测试方法不会崩溃
        XCTAssertNoThrow(receiptData)
    }
}
```

---

## 📊 验收标准

### ✅ 功能验收
- [ ] App Store收据验证正常工作
- [ ] 用户订阅状态正确更新
- [ ] 购买恢复功能正常
- [ ] 错误处理机制完善
- [ ] 交易日志记录完整

### ✅ 安全验收
- [ ] 收据验证严格有效
- [ ] 产品ID验证正确
- [ ] 敏感信息不泄露
- [ ] 重放攻击防护有效

### ✅ 性能验收
- [ ] 验证响应时间 < 3秒
- [ ] Apple API调用稳定
- [ ] 数据库更新及时
- [ ] 错误恢复机制可靠

这个App Store购买验证系统为SenseWord提供了安全可靠的付费功能基础。
