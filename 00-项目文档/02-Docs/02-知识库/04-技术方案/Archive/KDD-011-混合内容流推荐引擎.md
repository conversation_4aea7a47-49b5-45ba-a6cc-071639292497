# KDD-011 混合内容流推荐引擎技术方案

## 概述

基于031会议"生词也是无限内容流的一部分"的核心决策，实现客户端智能调度中心，将生词本、关联概念、惊喜发现等多源内容动态组合为个性化学习流。

## 核心目标

实现"瘦服务器，富客户端"架构下的混合内容流生成算法，将复杂的推荐逻辑从云端转移到iOS设备，降低运营成本，提升用户体验个性化程度。

## 接口规范

### 输入接口

#### 1. 生词本数据源
```swift
// 来源：GET /api/v1/bookmarks
struct BookmarkItem {
    let word: String
    let language: String
    let createdAt: Date
}

typealias BookmarkList = [BookmarkItem]
```

#### 2. 关联概念数据源
```swift
// 来源：当前单词的 relatedConcepts 字段
struct RelatedConcept {
    let word: String
    let relevanceScore: Double // 0.0-1.0
}

typealias RelatedConceptList = [RelatedConcept]
```

#### 3. 当前学习上下文
```swift
struct LearningContext {
    let currentWord: String?
    let recentWords: [String] // 最近查看的5个单词
    let sessionStartTime: Date
    let totalWordsInSession: Int
}
```

### 输出接口

#### 推荐结果
```swift
struct ContentRecommendation {
    let word: String
    let source: RecommendationSource
    let priority: Double // 0.0-1.0，越高越优先
    let metadata: RecommendationMetadata
}

enum RecommendationSource {
    case bookmark(reviewInterval: TimeInterval)
    case relatedConcept(parentWord: String)
    case discovery(reason: String)
}

struct RecommendationMetadata {
    let isBookmarked: Bool
    let lastReviewedAt: Date?
    let reviewCount: Int
    let estimatedDifficulty: Double
}
```

## 核心数据结构生命周期

### 关键帧1: 初始化状态 (App启动)
```swift
struct InitialState {
    let bookmarks: BookmarkList = []
    let learningContext: LearningContext = .empty
    let recommendationQueue: [ContentRecommendation] = []
    let algorithmWeights: AlgorithmWeights = .default
}
```

### 关键帧2: 数据加载完成状态
```swift
struct DataLoadedState {
    let bookmarks: BookmarkList // 从API获取的完整生词本
    let learningContext: LearningContext // 当前学习上下文
    let recommendationQueue: [ContentRecommendation] = [] // 待生成
    let algorithmWeights: AlgorithmWeights // 算法权重配置
}
```

### 关键帧3: 推荐生成状态
```swift
struct RecommendationGeneratedState {
    let bookmarks: BookmarkList
    let learningContext: LearningContext
    let recommendationQueue: [ContentRecommendation] // 已生成的推荐队列
    let algorithmWeights: AlgorithmWeights
    let nextRecommendation: ContentRecommendation? // 下一个推荐
}
```

### 关键帧4: 用户交互更新状态
```swift
struct InteractionUpdatedState {
    let bookmarks: BookmarkList // 可能新增收藏
    let learningContext: LearningContext // 更新学习上下文
    let recommendationQueue: [ContentRecommendation] // 动态调整队列
    let algorithmWeights: AlgorithmWeights // 可能调整权重
    let lastInteraction: UserInteraction
}
```

## 算法设计

### 智能调度规则

#### 规则1: 巩固优先 (Consolidation Priority)
```swift
func calculateReviewPriority(bookmark: BookmarkItem) -> Double {
    let daysSinceCreated = Date().timeIntervalSince(bookmark.createdAt) / 86400
    let optimalReviewInterval = calculateOptimalInterval(bookmark)
    
    // 间隔重复算法：越接近最佳复习时间，优先级越高
    let timingScore = 1.0 - abs(daysSinceCreated - optimalReviewInterval) / optimalReviewInterval
    
    return max(0.0, min(1.0, timingScore))
}

func calculateOptimalInterval(_ bookmark: BookmarkItem) -> Double {
    // 基于艾宾浩斯遗忘曲线的间隔重复
    // 1天 -> 3天 -> 7天 -> 15天 -> 30天
    let reviewCount = getReviewCount(bookmark.word)
    return pow(2.0, Double(reviewCount)) // 指数增长间隔
}
```

#### 规则2: 探索优先 (Exploration Priority)
```swift
func calculateExplorationPriority(concept: RelatedConcept, context: LearningContext) -> Double {
    var priority = concept.relevanceScore
    
    // 避免重复：最近查看过的单词降低优先级
    if context.recentWords.contains(concept.word) {
        priority *= 0.3
    }
    
    // 会话多样性：鼓励不同类型的单词
    priority *= calculateDiversityBonus(concept.word, context: context)
    
    return priority
}
```

#### 规则3: 惊喜发现 (Discovery Priority)
```swift
func calculateDiscoveryPriority(context: LearningContext) -> Double {
    let sessionProgress = Double(context.totalWordsInSession) / 10.0 // 每10个单词一个惊喜
    
    // 会话进度越长，惊喜发现的概率越高
    return min(0.2, sessionProgress * 0.05) // 最高20%概率
}
```

### 权重分配算法

```swift
struct AlgorithmWeights {
    let consolidation: Double = 0.6  // 60% - 巩固学习
    let exploration: Double = 0.3    // 30% - 探索学习
    let discovery: Double = 0.1      // 10% - 惊喜发现
    
    static let `default` = AlgorithmWeights()
    
    func adjustForUserBehavior(_ behavior: UserBehavior) -> AlgorithmWeights {
        // 根据用户行为动态调整权重
        switch behavior.learningStyle {
        case .conservative:
            return AlgorithmWeights(consolidation: 0.8, exploration: 0.15, discovery: 0.05)
        case .exploratory:
            return AlgorithmWeights(consolidation: 0.4, exploration: 0.5, discovery: 0.1)
        case .balanced:
            return .default
        }
    }
}
```

## 伪代码逻辑

### 主要算法流程

```swift
class ContentStreamRecommendationEngine {
    
    // [FC-01] 初始化推荐引擎
    func initialize() async throws -> DataLoadedState {
        let bookmarks = try await fetchBookmarks()
        let context = LearningContext.current()
        let weights = AlgorithmWeights.default
        
        return DataLoadedState(
            bookmarks: bookmarks,
            learningContext: context,
            algorithmWeights: weights
        )
    }
    
    // [FC-02] 生成下一个推荐
    func generateNextRecommendation(state: DataLoadedState) -> ContentRecommendation? {
        let consolidationCandidates = generateConsolidationCandidates(state.bookmarks)
        let explorationCandidates = generateExplorationCandidates(state.learningContext)
        let discoveryCandidates = generateDiscoveryCandidates(state.learningContext)
        
        let weightedCandidates = applyWeights(
            consolidation: consolidationCandidates,
            exploration: explorationCandidates,
            discovery: discoveryCandidates,
            weights: state.algorithmWeights
        )
        
        return selectBestCandidate(weightedCandidates)
    }
    
    // [FC-03] 更新学习上下文
    func updateContext(
        currentState: RecommendationGeneratedState,
        userInteraction: UserInteraction
    ) -> InteractionUpdatedState {
        let updatedContext = updateLearningContext(
            currentState.learningContext,
            interaction: userInteraction
        )
        
        let adjustedWeights = currentState.algorithmWeights
            .adjustForUserBehavior(userInteraction.behavior)
        
        return InteractionUpdatedState(
            bookmarks: currentState.bookmarks,
            learningContext: updatedContext,
            recommendationQueue: adjustRecommendationQueue(
                currentState.recommendationQueue,
                basedOn: userInteraction
            ),
            algorithmWeights: adjustedWeights,
            lastInteraction: userInteraction
        )
    }
}
```

### 数据持久化策略

```swift
class RecommendationStateManager {
    
    // 本地状态存储 (UserDefaults)
    func saveState(_ state: InteractionUpdatedState) {
        UserDefaults.standard.set(
            try? JSONEncoder().encode(state.learningContext),
            forKey: "learningContext"
        )
        UserDefaults.standard.set(
            try? JSONEncoder().encode(state.algorithmWeights),
            forKey: "algorithmWeights"
        )
    }
    
    // 状态恢复
    func restoreState() -> DataLoadedState? {
        guard let contextData = UserDefaults.standard.data(forKey: "learningContext"),
              let weightsData = UserDefaults.standard.data(forKey: "algorithmWeights"),
              let context = try? JSONDecoder().decode(LearningContext.self, from: contextData),
              let weights = try? JSONDecoder().decode(AlgorithmWeights.self, from: weightsData)
        else { return nil }
        
        return DataLoadedState(
            bookmarks: [], // 需要重新获取
            learningContext: context,
            algorithmWeights: weights
        )
    }
}
```

## 性能优化

### 内存管理
- 推荐队列最大长度：20个
- 生词本缓存：最多1000个条目
- 学习上下文历史：最近50次交互

### 计算优化
- 推荐计算：异步后台线程
- 权重调整：防抖动，最小间隔5秒
- 状态持久化：批量写入，减少I/O

### 网络优化
- 生词本数据：启动时获取，后台定期同步
- 单词详情：JIT加载，预加载下2个推荐
- 离线支持：本地缓存最近100个单词

## 测试策略

### 单元测试
- 算法权重计算准确性
- 间隔重复逻辑验证
- 状态转换完整性

### 集成测试
- 多源数据融合正确性
- 推荐队列管理稳定性
- 用户交互响应及时性

### 性能测试
- 1000个生词本条目的推荐生成时间 < 100ms
- 内存使用峰值 < 50MB
- 电池消耗影响 < 5%

## 风险评估

### 高风险
- 算法复杂度可能影响性能
- 用户行为数据不足影响个性化效果

### 中风险
- 本地状态管理的数据一致性
- 多线程并发访问的线程安全

### 低风险
- API数据格式变更
- iOS版本兼容性问题

## 依赖关系

### 前置依赖
- KDD-006: 用户生词本管理 (提供数据源)

### 后续依赖
- KDD-012: 无限内容流UI组件系统 (消费推荐结果)
- KDD-014: 生词本数据源优化 (优化数据获取)
