# KDD-004: 实时搜索建议服务技术方案

## 📋 功能单元概览

| 属性 | 值 |
|-----|---|
| **功能单元ID** | KDD-004 |
| **功能名称** | 实时搜索建议服务 |
| **优先级** | P1 (高) |
| **预估工期** | 2天 |
| **依赖关系** | KDD-003 (API安全防护中间件) |
| **功能类型** | 用户功能 |

## 🎯 核心目标

提供快速、智能的单词搜索建议功能，在用户输入过程中实时返回匹配的单词列表，提升搜索体验和容错性。

---

## 📊 接口规范

### 1. 搜索建议API接口

#### 1.1 搜索建议请求
```typescript
// GET /api/v1/suggestions?q={query}&limit={limit}&lang={language}
interface SearchSuggestionsRequest {
  q: string;                          // 用户输入的查询字符串
  limit?: number;                     // 返回结果数量限制 (默认10，最大20)
  lang?: string;                      // 语言过滤 (可选: en, zh, de等)
}
```

#### 1.2 搜索建议响应
```typescript
interface SearchSuggestionsResponse {
  success: true;
  suggestions: SuggestionItem[];
  query: string;                      // 原始查询字符串
  total: number;                      // 匹配结果总数
  responseTime: number;               // 响应时间(毫秒)
}

interface SuggestionItem {
  word: string;                       // 单词文本
  definition: string;                 // 核心释义
  language: string;                   // 语言代码
  relevanceScore: number;             // 相关性评分 (0-1)
}
```

#### 1.3 错误响应
```typescript
interface SearchSuggestionsError {
  success: false;
  error: 'INVALID_QUERY' | 'QUERY_TOO_SHORT' | 'RATE_LIMITED' | 'SYSTEM_ERROR';
  message: string;
  query?: string;
}
```

---

## 🔄 数据结构设计

### 关键帧A: 原始查询输入
```typescript
// 用户输入的原始查询
interface RawSearchQuery {
  query: string;                      // 用户输入 (如: "progres")
  timestamp: number;                  // 查询时间戳
  userId?: string;                    // 用户ID (如果已登录)
  sessionId: string;                  // 会话标识符
}
```

### 关键帧B: 处理后的查询
```typescript
// 经过预处理的查询
interface ProcessedSearchQuery {
  originalQuery: string;              // 原始查询
  normalizedQuery: string;            // 标准化查询 (小写、去空格)
  queryPattern: string;               // SQL LIKE模式 (如: "progres%")
  minLength: boolean;                 // 是否满足最小长度要求
  language?: string;                  // 语言过滤
  limit: number;                      // 结果数量限制
}
```

### 关键帧C: 数据库查询结果
```typescript
// 数据库原始查询结果
interface DatabaseQueryResult {
  results: DatabaseRow[];
  executionTime: number;              // 查询执行时间
  rowsScanned: number;                // 扫描行数
}

interface DatabaseRow {
  word: string;
  core_definition: string;
  language: string;
  word_length: number;
}
```

### 关键帧D: 最终响应数据
```typescript
// 经过处理和排序的最终结果
interface FinalSuggestionResult {
  suggestions: SuggestionItem[];
  metadata: {
    query: string;
    total: number;
    responseTime: number;
    cacheHit: boolean;
    dbExecutionTime: number;
  };
}
```

---

## 💻 核心实现逻辑

### 1. 搜索建议服务主逻辑

#### 1.1 API端点处理器
```typescript
// handlers/searchSuggestionsHandler.ts

export async function handleSearchSuggestions(
  request: Request,
  env: Env
): Promise<Response> {
  
  const startTime = Date.now();
  const url = new URL(request.url);
  
  try {
    // 1. 解析查询参数
    const queryParams = parseQueryParameters(url);
    if (!queryParams.isValid) {
      return createErrorResponse('INVALID_QUERY', queryParams.error);
    }
    
    console.log(`[搜索建议] 处理查询: "${queryParams.query}"`);
    
    // 2. 预处理查询
    const processedQuery = preprocessQuery(queryParams);
    if (!processedQuery.minLength) {
      return createErrorResponse('QUERY_TOO_SHORT', '查询字符串过短，至少需要2个字符');
    }
    
    // 3. 检查缓存
    const cacheKey = generateCacheKey(processedQuery);
    const cachedResult = await getCachedSuggestions(cacheKey, env);
    
    if (cachedResult) {
      console.log(`[搜索建议] 缓存命中: "${processedQuery.originalQuery}"`);
      return createSuccessResponse(cachedResult, startTime, true);
    }
    
    // 4. 执行数据库查询
    const dbResult = await executeSearchQuery(processedQuery, env);
    
    // 5. 处理和排序结果
    const suggestions = processSearchResults(dbResult.results, processedQuery);
    
    // 6. 构建最终响应
    const finalResult: FinalSuggestionResult = {
      suggestions,
      metadata: {
        query: processedQuery.originalQuery,
        total: suggestions.length,
        responseTime: Date.now() - startTime,
        cacheHit: false,
        dbExecutionTime: dbResult.executionTime
      }
    };
    
    // 7. 缓存结果
    await cacheSuggestions(cacheKey, finalResult, env);
    
    console.log(`[搜索建议] 查询完成: ${suggestions.length}个结果, ${finalResult.metadata.responseTime}ms`);
    
    return createSuccessResponse(finalResult, startTime, false);
    
  } catch (error) {
    console.error('[搜索建议] 查询失败:', error);
    return createErrorResponse('SYSTEM_ERROR', '搜索服务暂时不可用');
  }
}
```

#### 1.2 查询参数解析
```typescript
interface QueryParameters {
  isValid: boolean;
  query?: string;
  limit?: number;
  language?: string;
  error?: string;
}

function parseQueryParameters(url: URL): QueryParameters {
  const query = url.searchParams.get('q');
  const limitParam = url.searchParams.get('limit');
  const language = url.searchParams.get('lang');
  
  // 验证查询字符串
  if (!query) {
    return { isValid: false, error: '缺少查询参数 q' };
  }
  
  if (query.length < 2) {
    return { isValid: false, error: '查询字符串过短' };
  }
  
  if (query.length > 50) {
    return { isValid: false, error: '查询字符串过长' };
  }
  
  // 解析限制参数
  let limit = 10; // 默认值
  if (limitParam) {
    const parsedLimit = parseInt(limitParam, 10);
    if (parsedLimit > 0 && parsedLimit <= 20) {
      limit = parsedLimit;
    }
  }
  
  // 验证语言参数
  const validLanguages = ['en', 'zh', 'de', 'fr', 'es'];
  const validatedLanguage = language && validLanguages.includes(language) ? language : undefined;
  
  return {
    isValid: true,
    query: query.trim(),
    limit,
    language: validatedLanguage
  };
}
```

### 2. 数据库查询优化

#### 2.1 核心查询逻辑
```typescript
// services/searchQueryService.ts

export class SearchQueryService {
  
  /**
   * 执行搜索查询
   */
  static async executeSearchQuery(
    processedQuery: ProcessedSearchQuery,
    env: Env
  ): Promise<DatabaseQueryResult> {
    
    const startTime = Date.now();
    
    try {
      // 构建SQL查询
      const sql = this.buildSearchSQL(processedQuery);
      const params = this.buildQueryParams(processedQuery);
      
      console.log(`[数据库查询] SQL: ${sql}`);
      console.log(`[数据库查询] 参数:`, params);
      
      // 执行查询
      const stmt = env.DB.prepare(sql).bind(...params);
      const { results } = await stmt.all();
      
      const executionTime = Date.now() - startTime;
      
      console.log(`[数据库查询] 完成: ${results.length}行, ${executionTime}ms`);
      
      return {
        results: results as DatabaseRow[],
        executionTime,
        rowsScanned: results.length
      };
      
    } catch (error) {
      console.error('[数据库查询] 查询失败:', error);
      throw new Error(`Database query failed: ${error.message}`);
    }
  }
  
  /**
   * 构建SQL查询语句
   */
  private static buildSearchSQL(query: ProcessedSearchQuery): string {
    let sql = `
      SELECT 
        word, 
        core_definition, 
        language,
        LENGTH(word) as word_length
      FROM words 
      WHERE word LIKE ?
    `;
    
    // 添加语言过滤
    if (query.language) {
      sql += ' AND language = ?';
    }
    
    // 添加排序和限制
    sql += `
      ORDER BY 
        LENGTH(word) ASC,     -- 优先显示较短的单词
        word ASC              -- 字母顺序
      LIMIT ?
    `;
    
    return sql;
  }
  
  /**
   * 构建查询参数
   */
  private static buildQueryParams(query: ProcessedSearchQuery): any[] {
    const params = [query.queryPattern];
    
    if (query.language) {
      params.push(query.language);
    }
    
    params.push(query.limit);
    
    return params;
  }
}
```

#### 2.2 查询预处理
```typescript
function preprocessQuery(params: QueryParameters): ProcessedSearchQuery {
  const originalQuery = params.query!;
  
  // 标准化查询字符串
  const normalizedQuery = originalQuery
    .toLowerCase()                    // 转小写
    .trim()                          // 去除首尾空格
    .replace(/[^a-z0-9]/g, '');      // 只保留字母和数字
  
  // 生成SQL LIKE模式
  const queryPattern = `${normalizedQuery}%`;
  
  return {
    originalQuery,
    normalizedQuery,
    queryPattern,
    minLength: normalizedQuery.length >= 2,
    language: params.language,
    limit: params.limit || 10
  };
}
```

### 3. 结果处理和排序

#### 3.1 搜索结果处理
```typescript
function processSearchResults(
  dbResults: DatabaseRow[],
  query: ProcessedSearchQuery
): SuggestionItem[] {
  
  return dbResults.map(row => {
    // 计算相关性评分
    const relevanceScore = calculateRelevanceScore(row, query);
    
    return {
      word: row.word,
      definition: truncateDefinition(row.core_definition, 100),
      language: row.language,
      relevanceScore
    };
  }).sort((a, b) => {
    // 按相关性评分降序排序
    return b.relevanceScore - a.relevanceScore;
  });
}

/**
 * 计算相关性评分
 */
function calculateRelevanceScore(row: DatabaseRow, query: ProcessedSearchQuery): number {
  let score = 0;
  
  // 1. 精确匹配加分
  if (row.word.toLowerCase() === query.normalizedQuery) {
    score += 1.0;
  }
  
  // 2. 前缀匹配长度加分
  const matchLength = getCommonPrefixLength(row.word.toLowerCase(), query.normalizedQuery);
  score += matchLength / row.word.length * 0.8;
  
  // 3. 单词长度加分 (较短的单词更相关)
  score += (1 / row.word_length) * 0.2;
  
  return Math.min(score, 1.0); // 限制在0-1范围内
}

/**
 * 获取公共前缀长度
 */
function getCommonPrefixLength(str1: string, str2: string): number {
  let length = 0;
  const minLength = Math.min(str1.length, str2.length);
  
  for (let i = 0; i < minLength; i++) {
    if (str1[i] === str2[i]) {
      length++;
    } else {
      break;
    }
  }
  
  return length;
}
```

### 4. 缓存策略

#### 4.1 缓存管理
```typescript
// services/suggestionCacheService.ts

export class SuggestionCacheService {
  
  private static readonly CACHE_TTL = 300; // 5分钟
  private static readonly CACHE_PREFIX = 'suggestions:';
  
  /**
   * 生成缓存键
   */
  static generateCacheKey(query: ProcessedSearchQuery): string {
    const keyParts = [
      this.CACHE_PREFIX,
      query.normalizedQuery,
      query.language || 'all',
      query.limit.toString()
    ];
    
    return keyParts.join(':');
  }
  
  /**
   * 获取缓存的建议
   */
  static async getCachedSuggestions(
    cacheKey: string,
    env: Env
  ): Promise<FinalSuggestionResult | null> {
    
    try {
      if (!env.CACHE) return null;
      
      const cached = await env.CACHE.get(cacheKey);
      if (!cached) return null;
      
      const result = JSON.parse(cached) as FinalSuggestionResult;
      
      // 更新缓存命中标记
      result.metadata.cacheHit = true;
      
      return result;
      
    } catch (error) {
      console.warn('[缓存] 获取缓存失败:', error);
      return null;
    }
  }
  
  /**
   * 缓存搜索建议
   */
  static async cacheSuggestions(
    cacheKey: string,
    result: FinalSuggestionResult,
    env: Env
  ): Promise<void> {
    
    try {
      if (!env.CACHE) return;
      
      // 只缓存有结果的查询
      if (result.suggestions.length === 0) return;
      
      await env.CACHE.put(
        cacheKey,
        JSON.stringify(result),
        { expirationTtl: this.CACHE_TTL }
      );
      
      console.log(`[缓存] 缓存建议结果: ${cacheKey}`);
      
    } catch (error) {
      console.warn('[缓存] 缓存写入失败:', error);
    }
  }
}
```

---

## 🧪 测试策略

### 1. 功能测试
```typescript
// tests/searchSuggestions.test.ts

describe('实时搜索建议服务测试', () => {
  
  test('基本搜索功能', async () => {
    const response = await handleSearchSuggestions(
      createMockRequest('/api/v1/suggestions?q=prog'),
      testEnv
    );
    
    const data = await response.json();
    
    expect(data.success).toBe(true);
    expect(data.suggestions).toBeInstanceOf(Array);
    expect(data.suggestions.length).toBeGreaterThan(0);
    expect(data.suggestions[0].word).toContain('prog');
  });
  
  test('查询参数验证', async () => {
    // 测试过短查询
    const shortQuery = await handleSearchSuggestions(
      createMockRequest('/api/v1/suggestions?q=a'),
      testEnv
    );
    
    const shortData = await shortQuery.json();
    expect(shortData.success).toBe(false);
    expect(shortData.error).toBe('QUERY_TOO_SHORT');
  });
  
  test('缓存功能', async () => {
    const query = 'test';
    const cacheKey = SuggestionCacheService.generateCacheKey({
      normalizedQuery: query,
      limit: 10
    });
    
    // 第一次查询
    const response1 = await handleSearchSuggestions(
      createMockRequest(`/api/v1/suggestions?q=${query}`),
      testEnv
    );
    
    // 第二次查询应该命中缓存
    const response2 = await handleSearchSuggestions(
      createMockRequest(`/api/v1/suggestions?q=${query}`),
      testEnv
    );
    
    const data2 = await response2.json();
    expect(data2.metadata.cacheHit).toBe(true);
  });
});
```

### 2. 性能测试
```typescript
describe('搜索建议性能测试', () => {
  
  test('响应时间测试', async () => {
    const startTime = Date.now();
    
    const response = await handleSearchSuggestions(
      createMockRequest('/api/v1/suggestions?q=progressive'),
      testEnv
    );
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    expect(responseTime).toBeLessThan(500); // 响应时间小于500ms
    
    const data = await response.json();
    expect(data.metadata.responseTime).toBeLessThan(500);
  });
  
  test('并发查询测试', async () => {
    const queries = ['prog', 'test', 'word', 'lang', 'code'];
    
    const promises = queries.map(query =>
      handleSearchSuggestions(
        createMockRequest(`/api/v1/suggestions?q=${query}`),
        testEnv
      )
    );
    
    const responses = await Promise.all(promises);
    
    // 所有查询都应该成功
    for (const response of responses) {
      const data = await response.json();
      expect(data.success).toBe(true);
    }
  });
});
```

---

## 📊 验收标准

### ✅ 功能验收
- [ ] 搜索建议API正常工作
- [ ] 查询参数验证完整
- [ ] 结果排序逻辑正确
- [ ] 缓存机制有效
- [ ] 错误处理完善

### ✅ 性能验收
- [ ] API响应时间 < 500ms
- [ ] 数据库查询时间 < 100ms
- [ ] 缓存命中率 > 60%
- [ ] 并发支持 > 100 req/s

### ✅ 质量验收
- [ ] 搜索结果相关性高
- [ ] 支持多语言过滤
- [ ] 查询日志记录完整
- [ ] 监控指标准确

这个实时搜索建议服务为SenseWord用户提供了快速、准确的搜索体验。
