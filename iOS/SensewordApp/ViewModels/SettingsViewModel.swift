//
//  SettingsViewModel.swift
//  SensewordApp
//
//  Created by KDD-设置页面 Implementation on 2025-06-29.
//  设置页面视图模型
//

import Foundation
import SwiftUI
import Combine

/// 设置页面视图模型
@MainActor
class SettingsViewModel: ObservableObject {
    
    // MARK: - 发布属性
    
    /// 用户设置
    @Published var userSettings: UserSettings
    
    /// 是否正在加载
    @Published var isLoading: Bool = false
    
    /// 错误消息
    @Published var errorMessage: String? = nil
    
    /// 是否显示错误提示
    @Published var showError: Bool = false
    
    /// 是否显示确认对话框
    @Published var showConfirmation: Bool = false

    /// 当前待确认的操作
    @Published var pendingAction: AccountAction? = nil

    /// 是否显示订阅页面
    @Published var showSubscription: Bool = false

    /// 是否显示语言选择页面
    @Published var showLanguageSelection: Bool = false

    /// 产品信息
    @Published var productInfo: ProductInfo = ProductInfo.current
    
    // MARK: - 私有属性
    
    /// 设置服务
    private let settingsService: SettingsServiceProtocol
    
    /// 订阅取消器
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    
    init(settingsService: SettingsServiceProtocol = SettingsService.shared) {
        self.settingsService = settingsService
        self.userSettings = settingsService.currentSettings
        
        setupSubscriptions()
    }
    
    // MARK: - 设置订阅
    
    private func setupSubscriptions() {
        // 监听设置变化
        settingsService.settingsPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] settings in
                self?.userSettings = settings
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 设置更新方法
    
    /// 更新布尔类型设置
    func updateBoolSetting(_ key: SettingKey, value: Bool) {
        let request = SettingUpdateRequest(settingKey: key, boolValue: value)
        let response = settingsService.updateSetting(request)
        
        if !response.success {
            showErrorMessage(response.error ?? "设置更新失败")
        }
    }
    
    /// 更新自动播放音频设置
    func updateAutoPlayAudio(_ enabled: Bool) {
        updateBoolSetting(.autoPlayAudio, value: enabled)
    }
    
    /// 更新触感反馈设置
    func updateHapticFeedback(_ enabled: Bool) {
        updateBoolSetting(.hapticFeedback, value: enabled)
    }
    
    /// 更新每日通知设置
    func updateDailyNotification(_ enabled: Bool) {
        updateBoolSetting(.dailyNotification, value: enabled)
    }
    
    // MARK: - 账户操作方法
    
    /// 执行账户操作
    func performAccountAction(_ action: AccountAction) {
        pendingAction = action
        showConfirmation = true
    }
    
    /// 确认账户操作
    func confirmAccountAction() {
        guard let action = pendingAction else { return }

        showConfirmation = false
        isLoading = true

        Task {
            do {
                switch action {
                case .logout:
                    try await performLogout()
                case .deleteAccount:
                    try await performDeleteAccount()
                }
            } catch {
                await MainActor.run {
                    showErrorMessage("操作失败: \(error.localizedDescription)")
                }
            }

            await MainActor.run {
                isLoading = false
                pendingAction = nil
            }
        }
    }

    /// 执行登出操作（无注册机制下的简化实现）
    private func performLogout() async throws {
        NSLog("🚪 SettingsViewModel: 执行登出操作（清理本地数据）")

        // 在无注册机制下，登出主要是清理本地数据
        await MainActor.run {
            // 重置用户设置到默认值
            userSettings = UserSettings.default
            settingsService.saveSettings(userSettings)

            // 清理缓存
            // TODO: 添加缓存清理逻辑

            NSLog("🚪 SettingsViewModel: 登出完成")
        }
    }

    /// 执行删除账户操作（无注册机制下的简化实现）
    private func performDeleteAccount() async throws {
        NSLog("🗑️ SettingsViewModel: 执行删除账户操作（清理所有本地数据）")

        // 在无注册机制下，删除账户主要是清理所有本地数据
        await MainActor.run {
            // 重置用户设置
            userSettings = UserSettings.default
            settingsService.saveSettings(userSettings)

            // 清理所有本地数据
            // TODO: 添加完整的数据清理逻辑
            // 1. 清理收藏数据
            // 2. 清理缓存
            // 3. 清理用户偏好设置

            NSLog("🗑️ SettingsViewModel: 删除账户完成")
        }
    }
    
    /// 取消账户操作
    func cancelAccountAction() {
        showConfirmation = false
        pendingAction = nil
    }
    
    // MARK: - 私有方法
    

    
    /// 显示错误消息
    private func showErrorMessage(_ message: String) {
        errorMessage = message
        showError = true
    }
    
    // MARK: - 外部操作方法
    
    /// 打开订阅管理页面
    func openSubscriptionManagement() {
        if isPremium {
            // 如果已经是Premium用户，跳转到App Store管理订阅
            guard let url = URL(string: "https://apps.apple.com/account/subscriptions") else {
                showErrorMessage("无法打开订阅管理页面")
                return
            }

            UIApplication.shared.open(url) { success in
                if !success {
                    DispatchQueue.main.async {
                        self.showErrorMessage("无法打开订阅管理页面")
                    }
                }
            }
        } else {
            // 如果是免费用户，显示我们的订阅页面
            showSubscription = true
            NSLog("🎯 [SettingsViewModel] 显示订阅页面")
        }
    }
    
    /// 打开App Store评分
    func openAppStoreRating() {
        // TODO: 替换为实际的App Store链接
        guard let url = URL(string: "https://apps.apple.com/app/senseword/id123456789?action=write-review") else {
            showErrorMessage("无法打开App Store")
            return
        }
        
        UIApplication.shared.open(url) { success in
            if !success {
                DispatchQueue.main.async {
                    self.showErrorMessage("无法打开App Store")
                }
            }
        }
    }
    
    /// 清除缓存
    func clearCache() {
        isLoading = true
        
        Task {
            do {
                // TODO: 实现缓存清理逻辑
                // 1. 清除图片缓存
                // 2. 清除音频缓存
                // 3. 清除临时文件
                
                // 模拟异步操作
                try await Task.sleep(nanoseconds: 1_000_000_000)
                
                await MainActor.run {
                    isLoading = false
                    // TODO: 显示清理结果
                    NSLog("🧹 [SettingsViewModel] 缓存清理完成")
                }
                
            } catch {
                await MainActor.run {
                    isLoading = false
                    showErrorMessage("缓存清理失败: \(error.localizedDescription)")
                }
            }
        }
    }
}

// MARK: - 计算属性

extension SettingsViewModel {
    
    /// 是否为Premium用户
    var isPremium: Bool {
        userSettings.subscriptionStatus.isPremium
    }
    
    /// 订阅状态文本
    var subscriptionStatusText: String {
        userSettings.subscriptionStatus.displayText
    }
    

}

// MARK: - 语言设置管理

extension SettingsViewModel {

    /// 显示语言选择界面
    func showLanguageSelectionView() {
        showLanguageSelection = true
    }

    /// 更新用户偏好语言
    func updatePreferredLanguage(_ language: LanguageCode) {
        isLoading = true

        let response = SettingsService.shared.updatePreferredLanguage(language)

        if response.success {
            // 更新成功，界面会通过 settingsPublisher 自动更新
            NSLog("✅ [SettingsViewModel] 语言设置更新成功: \(language.displayName)")
        } else {
            // 显示错误信息
            errorMessage = response.error ?? "语言设置更新失败"
            showError = true
            NSLog("❌ [SettingsViewModel] 语言设置更新失败: \(response.error ?? "未知错误")")
        }

        isLoading = false
        showLanguageSelection = false
    }

    /// 取消语言选择
    func cancelLanguageSelection() {
        showLanguageSelection = false
    }

    /// 获取当前语言显示名称
    var currentLanguageDisplayName: String {
        return userSettings.preferredLanguage.displayName
    }

    /// 获取可用语言列表
    var availableLanguages: [LanguageCode] {
        return SettingsService.shared.getAvailableLanguages()
    }
}
