//
//  SettingsService.swift
//  SensewordApp
//
//  Created by KDD-设置页面 Implementation on 2025-06-29.
//  设置服务 - 管理用户偏好设置
//

import Foundation
import Combine
import StoreKit

/// 设置服务协议
protocol SettingsServiceProtocol {
    /// 当前用户设置
    var currentSettings: UserSettings { get }
    
    /// 设置变化发布者
    var settingsPublisher: AnyPublisher<UserSettings, Never> { get }
    
    /// 加载用户设置
    func loadSettings() -> UserSettings
    
    /// 更新设置项
    func updateSetting(_ request: SettingUpdateRequest) -> SettingUpdateResponse
    
    /// 保存设置
    func saveSettings(_ settings: UserSettings)
    
    /// 重置为默认设置
    func resetToDefaults()

    /// 更新用户偏好语言
    func updatePreferredLanguage(_ language: LanguageCode) -> LanguageUpdateResponse

    /// 获取当前用户偏好语言
    func getCurrentPreferredLanguage() -> LanguageCode

    /// 获取所有可用语言
    func getAvailableLanguages() -> [LanguageCode]
}

/// 设置服务实现
class SettingsService: SettingsServiceProtocol, ObservableObject {
    
    // MARK: - 属性
    
    /// 当前用户设置
    @Published private(set) var currentSettings: UserSettings
    
    /// 设置变化发布者
    var settingsPublisher: AnyPublisher<UserSettings, Never> {
        $currentSettings.eraseToAnyPublisher()
    }
    
    /// UserDefaults 键值
    private enum UserDefaultsKeys {
        static let userSettings = "user_settings"
    }
    
    // MARK: - 初始化
    
    init() {
        self.currentSettings = Self.loadSettingsFromStorage()
    }
    
    // MARK: - 公共方法
    
    /// 加载用户设置
    func loadSettings() -> UserSettings {
        let settings = Self.loadSettingsFromStorage()
        self.currentSettings = settings
        return settings
    }
    
    /// 更新设置项
    func updateSetting(_ request: SettingUpdateRequest) -> SettingUpdateResponse {
        var updatedSettings = currentSettings

        // 根据设置键更新对应的值
        switch request.settingKey {
        case .autoPlayAudio:
            if let boolValue = request.newValue as? Bool {
                updatedSettings.autoPlayAudio = boolValue
            }
        case .hapticFeedback:
            if let boolValue = request.newValue as? Bool {
                updatedSettings.hapticFeedback = boolValue
            }
        case .dailyNotification:
            if let boolValue = request.newValue as? Bool {
                updatedSettings.dailyNotification = boolValue
            }
        case .phoneticPreference:
            if let preference = request.newValue as? PhoneticPreference {
                updatedSettings.phoneticPreference = preference
            }
        }

        // 保存更新后的设置
        saveSettings(updatedSettings)

        // 更新当前设置
        self.currentSettings = updatedSettings

        NSLog("✅ [SettingsService] 设置更新成功: \(request.settingKey.displayName) = \(request.newValue)")

        return SettingUpdateResponse(
            success: true,
            updatedSettings: updatedSettings,
            error: nil
        )
    }
    
    /// 保存设置
    func saveSettings(_ settings: UserSettings) {
        do {
            let data = try JSONEncoder().encode(settings)
            UserDefaults.standard.set(data, forKey: UserDefaultsKeys.userSettings)
            NSLog("✅ [SettingsService] 设置保存成功")
        } catch {
            NSLog("❌ [SettingsService] 设置保存失败: \(error.localizedDescription)")
        }
    }
    
    /// 重置为默认设置
    func resetToDefaults() {
        let defaultSettings = UserSettings.default
        saveSettings(defaultSettings)
        self.currentSettings = defaultSettings
        NSLog("🔄 [SettingsService] 设置已重置为默认值")
    }
    
    // MARK: - 私有方法
    
    /// 从存储加载设置
    private static func loadSettingsFromStorage() -> UserSettings {
        guard let data = UserDefaults.standard.data(forKey: UserDefaultsKeys.userSettings) else {
            NSLog("📱 [SettingsService] 未找到保存的设置，使用默认设置")
            return UserSettings.default
        }
        
        do {
            let settings = try JSONDecoder().decode(UserSettings.self, from: data)
            NSLog("✅ [SettingsService] 设置加载成功")
            return settings
        } catch {
            NSLog("⚠️ [SettingsService] 设置解码失败，尝试迁移旧版本设置: \(error.localizedDescription)")

            // 尝试迁移旧版本设置
            if let migratedSettings = migrateOldSettings(from: data) {
                NSLog("✅ [SettingsService] 旧版本设置迁移成功")
                return migratedSettings
            } else {
                NSLog("❌ [SettingsService] 设置迁移失败，使用默认设置")
                return UserSettings.default
            }
        }
    }

    /// 迁移旧版本设置
    private static func migrateOldSettings(from data: Data) -> UserSettings? {
        do {
            // 尝试解析为字典
            guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] else {
                return nil
            }

            NSLog("🔄 [SettingsService] 开始迁移旧版本设置")

            // 提取已有字段
            let autoPlayAudio = json["autoPlayAudio"] as? Bool ?? true
            let hapticFeedback = json["hapticFeedback"] as? Bool ?? true
            let dailyNotification = json["dailyNotification"] as? Bool ?? false

            // 处理音标偏好
            let phoneticPreferenceString = json["phoneticPreference"] as? String ?? "NAmE"
            let phoneticPreference = PhoneticPreference(rawValue: phoneticPreferenceString) ?? .american

            // 处理订阅状态
            var subscriptionStatus: SubscriptionStatus = .free
            if let subscriptionData = json["subscriptionStatus"] as? [String: Any],
               let type = subscriptionData["type"] as? String {
                if type == "premium" || type == "pro", let expiryDateString = subscriptionData["expiryDate"] as? String {
                    let formatter = ISO8601DateFormatter()
                    if let expiryDate = formatter.date(from: expiryDateString) {
                        subscriptionStatus = .premium(expiryDate: expiryDate)
                    }
                }
            }



            // 创建迁移后的设置，使用默认语言
            let migratedSettings = UserSettings(
                autoPlayAudio: autoPlayAudio,
                hapticFeedback: hapticFeedback,
                dailyNotification: dailyNotification,
                phoneticPreference: phoneticPreference,
                subscriptionStatus: subscriptionStatus,
                preferredLanguage: .chinese  // 默认中文
            )

            NSLog("✅ [SettingsService] 设置迁移完成，默认语言设为中文")
            return migratedSettings

        } catch {
            NSLog("❌ [SettingsService] 设置迁移失败: \(error.localizedDescription)")
            return nil
        }
    }
}

// MARK: - 设置服务扩展

extension SettingsService {
    

    
    /// 更新订阅状态
    func updateSubscriptionStatus(_ status: SubscriptionStatus, isLocalEntitlement: Bool = false) {
        // 集成AnonymousPurchaseService进行权益检查
        Task {
            do {
                let entitlementStatus = try await AnonymousPurchaseService.shared.checkEntitlements()
                let subscriptionStatus = entitlementStatus.toSubscriptionStatus()

                var updatedSettings = currentSettings
                updatedSettings.subscriptionStatus = subscriptionStatus
                saveSettings(updatedSettings)
                self.currentSettings = updatedSettings

                NSLog("📱 [SettingsService] 订阅状态已更新 (集成权益检查): \(subscriptionStatus)")
            } catch {
                NSLog("❌ [SettingsService] 权益检查失败，使用传入状态: \(error)")

                // 权益检查失败时使用传入的状态
                var updatedSettings = currentSettings
                updatedSettings.subscriptionStatus = status
                saveSettings(updatedSettings)
                self.currentSettings = updatedSettings
            }
        }
    }
    
    /// 获取特定设置的值
    func getBoolSetting(for key: SettingKey) -> Bool {
        switch key {
        case .autoPlayAudio:
            return currentSettings.autoPlayAudio
        case .hapticFeedback:
            return currentSettings.hapticFeedback
        case .dailyNotification:
            return currentSettings.dailyNotification
        case .phoneticPreference:
            return false // 音标偏好不是布尔值
        }
    }

    /// 更新音标偏好
    func updatePhoneticPreference(_ preference: PhoneticPreference) {
        let request = SettingUpdateRequest(settingKey: .phoneticPreference, phoneticPreference: preference)
        _ = updateSetting(request)
    }

    /// 获取当前音标偏好
    func getCurrentPhoneticPreference() -> PhoneticPreference {
        return currentSettings.phoneticPreference
    }



    /// 更新用户偏好语言
    func updatePreferredLanguage(_ language: LanguageCode) -> LanguageUpdateResponse {
        NSLog("🌍 [SettingsService] 开始更新用户偏好语言: \(language.displayName)")

        // 验证语言代码
        guard LanguageCode.allCases.contains(language) else {
            NSLog("❌ [SettingsService] 无效的语言代码: \(language.rawValue)")
            return LanguageUpdateResponse(
                success: false,
                updatedSettings: currentSettings,
                error: "不支持的语言代码: \(language.rawValue)"
            )
        }

        // 创建更新后的设置
        var updatedSettings = currentSettings
        updatedSettings.preferredLanguage = language

        // 保存设置
        saveSettings(updatedSettings)

        // 更新当前设置
        self.currentSettings = updatedSettings

        NSLog("✅ [SettingsService] 语言设置更新成功: \(language.displayName)")

        return LanguageUpdateResponse(
            success: true,
            updatedSettings: updatedSettings,
            error: nil
        )
    }

    /// 获取当前用户偏好语言
    func getCurrentPreferredLanguage() -> LanguageCode {
        return currentSettings.preferredLanguage
    }

    /// 获取所有可用语言
    func getAvailableLanguages() -> [LanguageCode] {
        return LanguageCode.allCases
    }
}

// MARK: - 单例访问

extension SettingsService {
    /// 共享实例
    static let shared = SettingsService()
}
