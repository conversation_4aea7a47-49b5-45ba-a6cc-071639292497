//
//  SettingsModels.swift
//  SensewordApp
//
//  Created by KDD-设置页面 Implementation on 2025-06-29.
//  设置页面相关数据模型
//

import Foundation

// MARK: - 用户设置数据模型

/// 用户设置统一数据模型
struct UserSettings: Codable {
    /// 自动播放音频
    var autoPlayAudio: Bool

    /// 触感反馈
    var hapticFeedback: Bool

    /// 每日提醒通知
    var dailyNotification: Bool

    /// 音标偏好
    var phoneticPreference: PhoneticPreference

    /// 订阅状态
    var subscriptionStatus: SubscriptionStatus

    /// 用户偏好语言
    var preferredLanguage: LanguageCode



    /// 默认设置
    static let `default` = UserSettings(
        autoPlayAudio: true,
        hapticFeedback: true,
        dailyNotification: false,
        phoneticPreference: .american,
        subscriptionStatus: .free,
        preferredLanguage: .chinese
    )
}

// MARK: - 音标偏好

/// 音标偏好枚举
enum PhoneticPreference: String, Codable, CaseIterable {
    case american = "NAmE"  // 美式音标
    case british = "BrE"    // 英式音标

    /// 显示名称
    var displayName: String {
        switch self {
        case .american:
            return "美式"
        case .british:
            return "英式"
        }
    }

    /// 音标类型标识
    var phoneticType: String {
        return self.rawValue
    }
}

// MARK: - 订阅状态

/// 订阅状态枚举
enum SubscriptionStatus: Codable, Equatable {
    case free
    case premium(expiryDate: Date)

    // MARK: - Codable 实现

    enum CodingKeys: String, CodingKey {
        case type
        case expiryDate
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let type = try container.decode(String.self, forKey: .type)

        switch type {
        case "free":
            self = .free
        case "premium", "pro": // 兼容旧的"pro"格式
            let expiryDate = try container.decode(Date.self, forKey: .expiryDate)
            self = .premium(expiryDate: expiryDate)
        default:
            throw DecodingError.dataCorruptedError(forKey: .type, in: container, debugDescription: "Invalid subscription type")
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        switch self {
        case .free:
            try container.encode("free", forKey: .type)
        case .premium(let expiryDate):
            try container.encode("premium", forKey: .type)
            try container.encode(expiryDate, forKey: .expiryDate)
        }
    }
    
    /// 是否为 Premium 用户
    var isPremium: Bool {
        switch self {
        case .free:
            return false
        case .premium(let expiryDate):
            return expiryDate > Date()
        }
    }

    /// 订阅状态显示文本
    var displayText: String {
        switch self {
        case .free:
            return "免费用户"
        case .premium(let expiryDate):
            if expiryDate > Date() {
                let formatter = DateFormatter()
                formatter.dateStyle = .medium
                return "Premium 会员 · 到期 \(formatter.string(from: expiryDate))"
            } else {
                return "Premium 会员已过期"
            }
        }
    }
}



// MARK: - 设置项类型

/// 设置项键值枚举
enum SettingKey: String, CaseIterable {
    case autoPlayAudio = "auto_play_audio"
    case hapticFeedback = "haptic_feedback"
    case dailyNotification = "daily_notification"
    case phoneticPreference = "phonetic_preference"

    /// 显示名称
    var displayName: String {
        switch self {
        case .autoPlayAudio:
            return "自动播放音频"
        case .hapticFeedback:
            return "触感反馈"
        case .dailyNotification:
            return "每日提醒"
        case .phoneticPreference:
            return "音标偏好"
        }
    }

    /// 描述文本
    var description: String {
        switch self {
        case .autoPlayAudio:
            return "自动播放单词和例句的音频"
        case .hapticFeedback:
            return "在交互时提供震动反馈"
        case .dailyNotification:
            return "每日推送一个新单词"
        case .phoneticPreference:
            return "选择显示美式或英式音标"
        }
    }
}

// MARK: - 设置更新请求

/// 设置更新请求
struct SettingUpdateRequest {
    let settingKey: SettingKey
    let newValue: Any

    /// 便利初始化方法 - 布尔值
    init(settingKey: SettingKey, boolValue: Bool) {
        self.settingKey = settingKey
        self.newValue = boolValue
    }

    /// 便利初始化方法 - 音标偏好
    init(settingKey: SettingKey, phoneticPreference: PhoneticPreference) {
        self.settingKey = settingKey
        self.newValue = phoneticPreference
    }
}

/// 设置更新响应
struct SettingUpdateResponse {
    let success: Bool
    let updatedSettings: UserSettings
    let error: String?
}

// MARK: - 缓存清理

/// 缓存清理响应
struct CacheClearResponse {
    let success: Bool
    let clearedSize: String
    let error: String?
}

// MARK: - 账户操作

/// 账户操作类型
enum AccountAction {
    case logout
    case deleteAccount
    
    /// 显示名称
    var displayName: String {
        switch self {
        case .logout:
            return "退出登录"
        case .deleteAccount:
            return "注销账户"
        }
    }
    
    /// 确认消息
    var confirmationMessage: String {
        switch self {
        case .logout:
            return "确定要退出登录吗？"
        case .deleteAccount:
            return "注销账户将永久删除您的所有数据，此操作无法撤销。确定要继续吗？"
        }
    }
}

/// 账户操作响应
struct AccountActionResponse {
    let success: Bool
    let requiresConfirmation: Bool
    let error: String?
}

// MARK: - 产品信息

/// 产品信息
struct ProductInfo {
    let appName: String
    let version: String
    let buildNumber: String
    let description: String
    
    /// 获取当前应用信息
    static var current: ProductInfo {
        let bundle = Bundle.main
        return ProductInfo(
            appName: bundle.object(forInfoDictionaryKey: "CFBundleDisplayName") as? String ?? "SenseWord",
            version: bundle.object(forInfoDictionaryKey: "CFBundleShortVersionString") as? String ?? "1.0.0",
            buildNumber: bundle.object(forInfoDictionaryKey: "CFBundleVersion") as? String ?? "1",
            description: "不止于词，抵达心语"
        )
    }
    
    /// 版本信息文本
    var versionText: String {
        return "版本 \(version) (\(buildNumber))"
    }
}

// MARK: - 语言设置相关模型

/// 语言选择请求
struct LanguageSelectionRequest {
    /// 当前选择的语言
    let currentLanguage: LanguageCode

    /// 可用的语言列表
    let availableLanguages: [LanguageCode]
}

/// 语言选择响应
struct LanguageSelectionResponse {
    /// 用户选择的语言
    let selectedLanguage: LanguageCode

    /// 操作是否成功
    let success: Bool

    /// 错误信息（如果有）
    let error: String?
}

/// 语言设置更新请求
struct LanguageUpdateRequest {
    /// 新的语言设置
    let newLanguage: LanguageCode

    /// 用户ID（可选）
    let userId: String?
}

/// 语言设置更新响应
struct LanguageUpdateResponse {
    /// 操作是否成功
    let success: Bool

    /// 更新后的用户设置
    let updatedSettings: UserSettings

    /// 错误信息（如果有）
    let error: String?
}

// MARK: - 语言对设置模型

/// 语言对设置
/// 支持学习语言+脚手架语言组合配置
struct LanguagePairSettings: Codable, Identifiable, Hashable {
    /// 唯一标识符
    let id: String

    /// 学习语言（目标语言）
    let learningLanguage: LanguageCode

    /// 脚手架语言（母语/辅助语言）
    let scaffoldingLanguage: LanguageCode

    /// 是否启用此语言对
    var isEnabled: Bool

    /// 下载状态
    var downloadStatus: IndexDownloadStatus

    /// 最后更新时间
    var lastUpdated: Date

    /// 初始化语言对设置
    init(learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode, isEnabled: Bool = false) {
        self.id = "\(learningLanguage.rawValue)-\(scaffoldingLanguage.rawValue)"
        self.learningLanguage = learningLanguage
        self.scaffoldingLanguage = scaffoldingLanguage
        self.isEnabled = isEnabled
        self.downloadStatus = .notStarted
        self.lastUpdated = Date()
    }

    /// 显示名称
    var displayName: String {
        return "\(learningLanguage.displayName)→\(scaffoldingLanguage.displayName)"
    }
}

/// 索引下载状态
public enum IndexDownloadStatus: String, Codable, CaseIterable {
    case notStarted = "not_started"     // 未开始下载
    case downloading = "downloading"    // 正在下载
    case completed = "completed"        // 下载完成
    case failed = "failed"             // 下载失败
    case paused = "paused"             // 下载暂停

    /// 显示名称
    var displayName: String {
        switch self {
        case .notStarted:
            return "未下载"
        case .downloading:
            return "下载中"
        case .completed:
            return "已完成"
        case .failed:
            return "下载失败"
        case .paused:
            return "已暂停"
        }
    }

    /// 状态颜色（用于UI显示）
    var statusColor: String {
        switch self {
        case .notStarted:
            return "yellow"     // 黄色：未开始
        case .downloading:
            return "blue"       // 蓝色：下载中
        case .completed:
            return "green"      // 绿色：完成
        case .failed:
            return "red"        // 红色：失败
        case .paused:
            return "orange"     // 橙色：暂停
        }
    }
}
