//
//  AnonymousPurchaseModels.swift
//  SensewordApp
//
//  Created by KDD Implementation on 2025-07-27.
//  匿名用户购买相关数据模型
//

import Foundation

// MARK: - 权益状态枚举

/// 权益状态
/// 定义用户当前的权益级别和过期时间
enum EntitlementStatus: Codable, Equatable {
    case free
    case premium(expiryDate: Date?)
    case gracePeriod(expiryDate: Date?)
    case billingRetry(expiryDate: Date?)

    /// 是否为Premium用户
    var isPremium: Bool {
        switch self {
        case .free:
            return false
        case .premium(let expiryDate):
            if let expiry = expiryDate {
                return expiry > Date()
            }
            return true // 永久Premium
        case .gracePeriod(let expiryDate):
            if let expiry = expiryDate {
                return expiry > Date()
            }
            return true // 宽限期内仍为Premium
        case .billingRetry(let expiryDate):
            if let expiry = expiryDate {
                return expiry > Date()
            }
            return true // 账单重试期内仍为Premium
        }
    }
    
    /// 过期时间
    var expiryDate: Date? {
        switch self {
        case .free:
            return nil
        case .pro(let expiryDate):
            return expiryDate
        case .gracePeriod(let expiryDate):
            return expiryDate
        case .billingRetry(let expiryDate):
            return expiryDate
        }
    }
    
    /// 显示文本
    var displayText: String {
        switch self {
        case .free:
            return "免费用户"
        case .pro(let expiryDate):
            if let expiry = expiryDate {
                let formatter = DateFormatter()
                formatter.dateStyle = .medium
                return "Pro用户 (到期: \(formatter.string(from: expiry)))"
            } else {
                return "Pro用户 (永久)"
            }
        case .gracePeriod(let expiryDate):
            if let expiry = expiryDate {
                let formatter = DateFormatter()
                formatter.dateStyle = .medium
                return "Pro用户 - 宽限期 (到期: \(formatter.string(from: expiry)))"
            } else {
                return "Pro用户 - 宽限期"
            }
        case .billingRetry(let expiryDate):
            if let expiry = expiryDate {
                let formatter = DateFormatter()
                formatter.dateStyle = .medium
                return "Pro用户 - 账单重试期 (到期: \(formatter.string(from: expiry)))"
            } else {
                return "Pro用户 - 账单重试期"
            }
        }
    }

    /// 转换为SubscriptionStatus
    func toSubscriptionStatus() -> SubscriptionStatus {
        switch self {
        case .free:
            return .free
        case .premium(let expiryDate):
            if let expiry = expiryDate {
                return .premium(expiryDate: expiry)
            } else {
                return .premium(expiryDate: Date.distantFuture)
            }
        case .gracePeriod(let expiryDate):
            if let expiry = expiryDate {
                return .premium(expiryDate: expiry)
            } else {
                return .premium(expiryDate: Date.distantFuture)
            }
        case .billingRetry(let expiryDate):
            if let expiry = expiryDate {
                return .premium(expiryDate: expiry)
            } else {
                return .premium(expiryDate: Date.distantFuture)
            }
        }
    }
}

// MARK: - 本地权益记录

/// 本地权益记录
/// 存储在Keychain中的购买记录结构
struct LocalEntitlementRecord: Codable, Identifiable {
    let id = UUID()
    let productId: String
    let purchaseDate: Date
    let expiryDate: Date?
    
    /// 是否已过期
    var isExpired: Bool {
        if let expiry = expiryDate {
            return expiry <= Date()
        }
        return false // 永久权益不过期
    }
    
    /// 剩余天数
    var daysRemaining: Int? {
        guard let expiry = expiryDate else { return nil }
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: Date(), to: expiry)
        return max(0, components.day ?? 0)
    }
    
    /// 产品类型
    var productType: ProductType {
        if productId.contains("yearly") {
            return .yearlyPremium
        } else if productId.contains("monthly") {
            return .monthlyPremium
        } else {
            return .unknown
        }
    }
}

// MARK: - 产品类型

/// 产品类型枚举
enum ProductType: String, CaseIterable, Codable {
    case monthlyPremium = "monthly_premium"
    case yearlyPremium = "yearly_premium"
    case unknown = "unknown"
    
    /// 显示名称
    var displayName: String {
        switch self {
        case .monthlyPremium:
            return "月度订阅"
        case .yearlyPremium:
            return "年度订阅"
        case .unknown:
            return "未知产品"
        }
    }
    
    /// 订阅周期（天数）
    var subscriptionDays: Int {
        switch self {
        case .monthlyPremium:
            return 30
        case .yearlyPremium:
            return 365
        case .unknown:
            return 0
        }
    }
}

// MARK: - 恢复购买结果

/// 恢复购买结果枚举
enum RestoreResult {
    case success([ProductId])
    case noProductsToRestore
    case failed(Error)
}

/// 恢复购买详细结果
/// 包含恢复操作的详细信息和结果状态
struct RestoreResultDetail: Codable {
    let success: Bool
    let restoredCount: Int
    let entitlementStatus: EntitlementStatus
    let error: String?

    /// 初始化
    init(success: Bool, restoredCount: Int, entitlementStatus: EntitlementStatus, error: Error?) {
        self.success = success
        self.restoredCount = restoredCount
        self.entitlementStatus = entitlementStatus
        self.error = error?.localizedDescription
    }

    /// 显示消息
    var displayMessage: String {
        if success {
            if restoredCount > 0 {
                return "成功恢复 \(restoredCount) 个购买项目"
            } else {
                return "没有找到可恢复的购买项目"
            }
        } else {
            return error ?? "恢复购买失败"
        }
    }
}

// MARK: - 购买验证状态

/// 购买验证状态
/// 用于跟踪购买验证过程的状态
enum PurchaseVerificationStatus: String, CaseIterable, Codable {
    case pending = "pending"
    case verified = "verified"
    case failed = "failed"
    case expired = "expired"
    
    /// 显示文本
    var displayText: String {
        switch self {
        case .pending:
            return "验证中"
        case .verified:
            return "已验证"
        case .failed:
            return "验证失败"
        case .expired:
            return "已过期"
        }
    }
    
    /// 是否为有效状态
    var isValid: Bool {
        return self == .verified
    }
}

// MARK: - 权益管理配置

/// 权益管理配置
/// 定义权益管理的各种配置参数
struct EntitlementConfig {
    /// 权益检查间隔（秒）
    static let checkInterval: TimeInterval = 3600 // 1小时
    
    /// 过期权益清理间隔（秒）
    static let cleanupInterval: TimeInterval = 86400 // 24小时
    
    /// 最大缓存权益数量
    static let maxCachedEntitlements = 10
    
    /// 权益验证超时时间（秒）
    static let verificationTimeout: TimeInterval = 30
    
    /// 支持的产品ID前缀
    static let supportedProductPrefixes = ["com.senseword.premium", "com.senseword.pro"]
}

// MARK: - 扩展方法

extension EntitlementStatus {
    /// 从SubscriptionStatus转换
    static func from(_ subscriptionStatus: SubscriptionStatus) -> EntitlementStatus {
        switch subscriptionStatus {
        case .free:
            return .free
        case .premium(let expiryDate):
            return .premium(expiryDate: expiryDate)
        }
    }
}

extension LocalEntitlementRecord {
    /// 创建测试数据
    static func createTestRecord(productId: String = "com.senseword.premium.yearly", daysFromNow: Int = 365) -> LocalEntitlementRecord {
        let purchaseDate = Date()
        let expiryDate = Calendar.current.date(byAdding: .day, value: daysFromNow, to: purchaseDate)
        
        return LocalEntitlementRecord(
            productId: productId,
            purchaseDate: purchaseDate,
            expiryDate: expiryDate
        )
    }
}
